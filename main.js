import Vue from 'vue'
import App from './App'

import uview from 'uview-ui'
import utils from "@/utils"
import events from "@/events"
import constants from '@/constants'
import apis from '@/apis'

Vue.use(uview)
Vue.use(utils)
Vue.use(events)
Vue.use(constants)
Vue.use(apis)
Vue.config.silent = true

App.mpType = 'app'

// 注册组件  =======================================================

// 表单组件
import CascaderField from '@/components/form/field/CascaderField'
import CheckboxField from '@/components/form/field/CheckboxField'
import DateField from '@/components/form/field/DateField'
import IntegerField from '@/components/form/field/IntegerField'
import LonLatField from '@/components/form/field/LonLatField'
import NumberField from '@/components/form/field/NumberField'
import NumberWithUnitField from '@/components/form/field/NumberWithUnitField'
import PictureField from '@/components/form/field/PictureField'
import RadioField from '@/components/form/field/RadioField'
import SelectField from '@/components/form/field/SelectField'
import SubForm from '@/components/form/field/SubForm'
import TextField from '@/components/form/field/TextField'
import DateRangeField from '@/components/form/field/DateRangeField'
import DateTimeField from '@/components/form/field/DateTimeField'

Vue.component( 'CascaderField', CascaderField )
Vue.component( 'DateTimeField', DateTimeField )
Vue.component( 'CheckboxField', CheckboxField )
Vue.component( 'DateField', DateField )
Vue.component( 'IntegerField', IntegerField )
Vue.component( 'LonLatField', LonLatField )
Vue.component( 'NumberField', NumberField )
Vue.component( 'NumberWithUnitField', NumberWithUnitField )
Vue.component( 'PictureField', PictureField )
Vue.component( 'RadioField', RadioField )
Vue.component( 'SelectField', SelectField )
Vue.component( 'SubForm', SubForm )
Vue.component( 'TextField', TextField )
Vue.component( 'DateRangeField', DateRangeField )

// 布局组件
import Collapse from '@/components/form/layout/Collapse'
import Grid from '@/components/form/layout/Grid'
import SplitLine from '@/components/form/layout/SplitLine'
import Title from '@/components/form/layout/Title'

Vue.component( 'Collapse', Collapse )
Vue.component( 'Grid', Grid )
Vue.component( 'SplitLine', SplitLine )
Vue.component( 'Title', Title )

// 注册组件  =======================================================

// 注册扩展组件  ====================================================

import TiledSubForm from '@/app/components/form/TiledSubForm'
import YearSelectField from '@/app/components/form/YearSelectField'
import PeerSelectField from '@/app/components/form/PeerSelectField'
import TextAreaField from '@/app/components/form/TextAreaField'

Vue.component( 'TiledSubForm', TiledSubForm )
Vue.component( 'PeerSelectField', PeerSelectField )
Vue.component( 'YearSelectField', YearSelectField )
Vue.component( 'TextAreaField', TextAreaField )

// 注册扩展组件  ====================================================

const app = new Vue({
    ...App
})

app.$mount()