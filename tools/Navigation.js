const MAPS = [{
		title: '高德地图'
	},
	{
		title: '百度地图'
	}
]

const navigateTo = (lon, lat, name = '目的地') => {
	plus.nativeUI.actionSheet({
			title: '选择地图',
			cancel: '取消',
			buttons: MAPS
		},
		function(e) {
			if(e.index === 0) return
			let url = e.index === 1 ? getAMapUrl(lon, lat, name) : getBaiduUrl(lon, lat, name)
			url = encodeURI(url)
			plus.runtime.openURL(url, function(err) {
				showError(`未安装${MAPS[e.index - 1].title}`)
			})
	})
}

const getBaiduUrl = (lon, lat, name) => {
	return `baidumap://map/marker?location=${lat},${lon}&title=${name}&content=${name}&coord_type=gcj02`
}

const getAMapUrl = (lon, lat, name) => {
	const platform = plus.os.name.toLowerCase()
	return `${platform}amap://viewMap?sourceApplication=appname&poiname=${name}&lat=${lat}&lon=${lon}&dev=0`
}

const showError = (msg) => {
	uni.showToast({
		title: msg,
		icon: 'error',
		duration: 2000
	})
}
		
export default {
	navigateTo
}
