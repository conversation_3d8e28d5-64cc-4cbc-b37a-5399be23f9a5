
const FORM_DATA_KEY_PREFIX = '__FORM_DATA_CACHE__'
const PARENT_FORM_DATA_KEY = '__PARENT_FORM_DATA_CACHE_KEY__'
const USER_INFO_KEY = '__USER_INFO_CACHE__'
const SHARE_DATA_KEY_PREFIX = '__SHARE_DATA_CACHE__'

/**
 * 生成表单数据存储KEY
 * @param {*} uid
 */
const genFormDataKey = (uid) => {
	return `${FORM_DATA_KEY_PREFIX}${uid}`
}

/**
 * 保存表单数据到本地存储
 * @param {*} formRecordUid
 * @param {*} data
 */
const saveFormData = (formRecordUid, data) => {
	if(!formRecordUid || !data) return false
	try{
		const key = genFormDataKey(formRecordUid)
		uni.setStorageSync(key, data)
		return true
	}
	catch(e){}
	return false
}

/**
 * 从本地存储获取表单数据
 * @param {*} formRecordUid    记录UID
 * @param {*} once             获取后删除
 */
const getFormData = (formRecordUid, once=true) => {
	if(!formRecordUid) return
	const key = genFormDataKey(formRecordUid)
	try{
		const result = uni.getStorageSync(key)
		if(result && once){
			removeFormData(formRecordUid)
		}
		return result
	}
	catch(e){}
}

/**
 * 从本地存储删除表单数据
 * @param {*} formRecordUid
 */
const removeFormData = (formRecordUid) => {
	if(!formRecordUid) return
	const key = genFormDataKey(formRecordUid)
	try{
		uni.removeStorageSync(key)
	}
	catch(e){}
}

/**
 * 保存父表单数据到本地存储
 * @param {*} data
 */
const saveParentFormData = (data) => {
	if(!data) return false
	try{
		uni.setStorageSync(PARENT_FORM_DATA_KEY, data)
		return true
	}
	catch(e){}
	return false
}

/**
 * 从本地存储获取父表单数据
 * @param {*} once             获取后删除
 */
const getParentFormData = () => {
	try{
		const result = uni.getStorageSync(PARENT_FORM_DATA_KEY)
		if(result){
			removeParentFormData()
		}
		return result
	}
	catch(e){}
}

/**
 * 从本地存储删除表单数据
 */
const removeParentFormData = () => {
	try{
		uni.removeStorageSync(PARENT_FORM_DATA_KEY)
	}
	catch(e){}
}

const saveUser = (userInfo) => {
	if(!userInfo) return false
	try{
		const info = {
			'user.nickname': userInfo.nickname,
			'user.contact': userInfo.phone,
			'user.company': userInfo.company,
			'user.pname': userInfo.pname,
			'user.pcode': userInfo.pcode,
			'user.cname': userInfo.cname,
			'user.ccode': userInfo.ccode,
			'user.fname': userInfo.fname,
			'user.fcode': userInfo.fcode,
		}
		uni.setStorageSync(USER_INFO_KEY, info)
		return true
	}
	catch(e){}
	return false
}

const getUserInfo = (key) => {
	try{
		const user = uni.getStorageSync(USER_INFO_KEY)
		return user && user[key]
	}
	catch(e){}
}

const clearUser = () => {
	try{
		uni.removeStorageSync(USER_INFO_KEY)
	}
	catch(e){}
}

// 共享数据
const setShareData = (key, data) => {
	if(!key) return
	const shareKey = genShareDataKey(key)
	try{
		uni.setStorageSync(shareKey, data)
		return true
	}
	catch(e){}
	return false
	
}

const getShareData = (key) => {
	if(!key) return
	const shareKey = genShareDataKey(key)
	try{
		return uni.getStorageSync(shareKey)
	}
	catch(e){}
}

const removeShareData = (key) => {
	if(!key) return
	const shareKey = genShareDataKey(key)
	try{
		uni.removeStorageSync(shareKey)
	}
	catch(e){}
}

const genShareDataKey = (key) => {
	return `${SHARE_DATA_KEY_PREFIX}${key}`
}

export default {
	saveFormData,
	getFormData,
	removeFormData,
	saveParentFormData,
	getParentFormData,
	removeParentFormData,
	saveUser,
	getUserInfo,
	clearUser,
	setShareData,
	getShareData,
	removeShareData
}