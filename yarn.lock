# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@lukeed/csprng@^1.0.0":
  version "1.0.1"
  resolved "https://registry.npmmirror.com/@lukeed/csprng/-/csprng-1.0.1.tgz"
  integrity sha512-uSvJdwQU5nK+Vdf6zxcWAY2A8r7uqe+gePwLWzJ+fsQehq18pc0I2hJKwypZ2aLM90+Er9u1xn4iLJPZ+xlL4g==

axios-miniprogram-adapter@^0.3.2:
  version "0.3.4"
  resolved "https://registry.npmmirror.com/axios-miniprogram-adapter/-/axios-miniprogram-adapter-0.3.4.tgz"
  integrity sha512-nQVl5bIUn9bW9IWT/pWBjsFt0RtXTMc24t5ISHS6NVYg/U6EUDzbPW1vWq5nXFc3MniJYi+6iNlliVB6lWpfNg==
  dependencies:
    axios "^0.19.2"

axios@^0.19.2:
  version "0.19.2"
  resolved "https://registry.npmmirror.com/axios/-/axios-0.19.2.tgz"
  integrity sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==
  dependencies:
    follow-redirects "1.5.10"

axios@^0.26.1:
  version "0.26.1"
  resolved "https://registry.npmmirror.com/axios/-/axios-0.26.1.tgz"
  integrity sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==
  dependencies:
    follow-redirects "^1.14.8"

crypto-js@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/crypto-js/-/crypto-js-4.1.1.tgz"
  integrity sha512-o2JlM7ydqd3Qk9CA0L4NL6mTzU2sdx96a+oOfPu8Mkl/PK51vSyoi8/rQ8NknZtk44vq15lmhAj9CIAGwgeWKw==

dayjs@^1.11.0:
  version "1.11.6"
  resolved "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.6.tgz"
  integrity sha512-zZbY5giJAinCG+7AGaw0wIhNZ6J8AhWuSXKvuc1KAyMiRsvGQWqh4L+MomvhdAYjN+lqvVCMq1I41e3YHvXkyQ==

debug@=3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/debug/-/debug-3.1.0.tgz"
  integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
  dependencies:
    ms "2.0.0"

dotenv@^16.0.1:
  version "16.0.3"
  resolved "https://registry.npmmirror.com/dotenv/-/dotenv-16.0.3.tgz"
  integrity sha512-7GO6HghkA5fYG9TYnNxi14/7K9f5occMlp3zXAuSxn7CKCxt9xbNWG7yF8hTCSUchlfWSe3uLmlPfigevRItzQ==

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.5.10.tgz"
  integrity sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.14.8:
  version "1.15.2"
  resolved "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.2.tgz"
  integrity sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==

lodash@^4.17.21, lodash@~4.17.21:
  version "4.17.21"
  resolved "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

moment@^2.29.1:
  version "2.29.4"
  resolved "https://registry.npmmirror.com/moment/-/moment-2.29.4.tgz"
  integrity sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz"
  integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==

omit-deep-lodash@^1.1.7:
  version "1.1.7"
  resolved "https://registry.npmmirror.com/omit-deep-lodash/-/omit-deep-lodash-1.1.7.tgz"
  integrity sha512-9m9gleSMoxq3YO8aCq5pGUrqG9rKF0w/P70JHQ1ymjUQA/3+fVa2Stju9XORJKLmyLYEO3zzX40MJYaYl5Og4w==
  dependencies:
    lodash "~4.17.21"

uid@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/uid/-/uid-2.0.1.tgz"
  integrity sha512-PF+1AnZgycpAIEmNtjxGBVmKbZAQguaa4pBUq6KNaGEcpzZ2klCNZLM34tsjp76maN00TttiiUf6zkIBpJQm2A==
  dependencies:
    "@lukeed/csprng" "^1.0.0"

uview-ui@^2.0.38:
  version "2.0.38"
  resolved "https://registry.npmmirror.com/uview-ui/-/uview-ui-2.0.38.tgz#02a0585f2c966a81c2997c79cfff0e8538a89c61"
  integrity sha512-6egHDf9lXHKpG3hEjRE0vMx4+VWwKk/ReTf5x18KrIKqdvdPRqO3+B8Unh7vYYwrIxzAWIlmhZ9RJpKI/4UqPQ==
