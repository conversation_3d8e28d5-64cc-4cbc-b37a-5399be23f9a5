Arguments: 
  /usr/local/bin/node /usr/local/bin/yarn

PATH: 
  /Users/<USER>/Library/pnpm:/Library/Frameworks/Python.framework/Versions/2.7/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/opt/local/bin:/opt/local/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/go/bin

Yarn version: 
  1.22.19

Node version: 
  16.20.2

Platform: 
  darwin x64

Trace: 
  Error: incorrect data check
      at Zlib.zlibOnError [as onerror] (node:zlib:189:17)

npm manifest: 
  {
    "name": "gt-mis-uniapp",
    "version": "1.0.0",
    "main": "main.js",
    "scripts": {
      "test": "echo \"Error: no test specified\" && exit 1",
      "prepare": "husky install"
    },
    "repository": {
      "type": "git",
      "url": "http://**************/mis/gt-mis-uniapp.git"
    },
    "author": "",
    "license": "ISC",
    "dependencies": {
      "@turf/turf": "^6.5.0",
      "axios": "^0.26.1",
      "axios-miniprogram-adapter": "^0.3.2",
      "crypto-js": "^4.1.1",
      "dayjs": "^1.11.0",
      "dotenv": "^16.0.1",
      "gt-mis-app-components": "^1.2.7",
      "lodash": "^4.17.21",
      "mapbox-gl": "2.12.1",
      "moment": "^2.29.1",
      "omit-deep-lodash": "^1.1.7",
      "uid": "^2.0.0",
      "uview-ui": "^2.0.35"
    },
    "eslintConfig": {
      "root": true,
      "env": {
        "node": true
      },
      "extends": [
        "plugin:vue/essential",
        "eslint:recommended"
      ],
      "parserOptions": {
        "parser": "babel-eslint"
      },
      "rules": {}
    },
    "description": "",
    "–minimize": {
      "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize"
    },
    "devDependencies": {
      "@babel/core": "^7.21.0",
      "@babel/eslint-parser": "^7.19.1",
      "eslint": "^8.36.0",
      "eslint-config-airbnb-base": "^15.0.0",
      "eslint-config-ali": "^14.0.2",
      "eslint-config-prettier": "^8.7.0",
      "eslint-plugin-import": "^2.27.5",
      "eslint-plugin-prettier": "^4.2.1",
      "eslint-plugin-vue": "^9.9.0",
      "prettier": "^2.8.4",
      "vue-eslint-parser": "^9.1.0"
    },
    "lint-staged": {
      "*.js": "eslint --cache --fix"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@ampproject/remapping@^2.2.0":
    version "2.3.0"
    resolved "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.3.0.tgz"
    integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
    dependencies:
      "@jridgewell/gen-mapping" "^0.3.5"
      "@jridgewell/trace-mapping" "^0.3.24"
  
  "@babel/code-frame@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.24.7.tgz"
    integrity sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==
    dependencies:
      "@babel/highlight" "^7.24.7"
      picocolors "^1.0.0"
  
  "@babel/compat-data@^7.25.2":
    version "7.25.4"
    resolved "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.25.4.tgz"
    integrity sha512-+LGRog6RAsCJrrrg/IO6LGmpphNe5DiK30dGjCoxxeGv49B10/3XYGxPsAwrDlMFcFEvdAUavDT8r9k/hSyQqQ==
  
  "@babel/core@^7.16.0", "@babel/core@^7.21.0", "@babel/core@^7.23.6":
    version "7.25.2"
    resolved "https://registry.npmmirror.com/@babel/core/-/core-7.25.2.tgz"
    integrity sha512-BBt3opiCOxUr9euZ5/ro/Xv8/V7yJ5bjYMqG/C1YAo8MIKAnumZalCN+msbci3Pigy4lIQfPUpfMM27HMGaYEA==
    dependencies:
      "@ampproject/remapping" "^2.2.0"
      "@babel/code-frame" "^7.24.7"
      "@babel/generator" "^7.25.0"
      "@babel/helper-compilation-targets" "^7.25.2"
      "@babel/helper-module-transforms" "^7.25.2"
      "@babel/helpers" "^7.25.0"
      "@babel/parser" "^7.25.0"
      "@babel/template" "^7.25.0"
      "@babel/traverse" "^7.25.2"
      "@babel/types" "^7.25.2"
      convert-source-map "^2.0.0"
      debug "^4.1.0"
      gensync "^1.0.0-beta.2"
      json5 "^2.2.3"
      semver "^6.3.1"
  
  "@babel/eslint-parser@^7.16.3", "@babel/eslint-parser@^7.19.1", "@babel/eslint-parser@^7.23.3":
    version "7.25.1"
    resolved "https://registry.npmmirror.com/@babel/eslint-parser/-/eslint-parser-7.25.1.tgz"
    integrity sha512-Y956ghgTT4j7rKesabkh5WeqgSFZVFwaPR0IWFm7KFHFmmJ4afbG49SmfW4S+GyRPx0Dy5jxEWA5t0rpxfElWg==
    dependencies:
      "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
      eslint-visitor-keys "^2.1.0"
      semver "^6.3.1"
  
  "@babel/generator@^7.25.0", "@babel/generator@^7.25.6":
    version "7.25.6"
    resolved "https://registry.npmmirror.com/@babel/generator/-/generator-7.25.6.tgz"
    integrity sha512-VPC82gr1seXOpkjAAKoLhP50vx4vGNlF4msF64dSFq1P8RfB+QAuJWGHPXXPc8QyfVWwwB/TNNU4+ayZmHNbZw==
    dependencies:
      "@babel/types" "^7.25.6"
      "@jridgewell/gen-mapping" "^0.3.5"
      "@jridgewell/trace-mapping" "^0.3.25"
      jsesc "^2.5.1"
  
  "@babel/helper-annotate-as-pure@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.24.7.tgz"
    integrity sha512-BaDeOonYvhdKw+JoMVkAixAAJzG2jVPIwWoKBPdYuY9b452e2rPuI9QPYh3KpofZ3pW2akOmwZLOiOsHMiqRAg==
    dependencies:
      "@babel/types" "^7.24.7"
  
  "@babel/helper-compilation-targets@^7.25.2":
    version "7.25.2"
    resolved "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.25.2.tgz"
    integrity sha512-U2U5LsSaZ7TAt3cfaymQ8WHh0pxvdHoEk6HVpaexxixjyEquMh0L0YNJNM6CTGKMXV1iksi0iZkGw4AcFkPaaw==
    dependencies:
      "@babel/compat-data" "^7.25.2"
      "@babel/helper-validator-option" "^7.24.8"
      browserslist "^4.23.1"
      lru-cache "^5.1.1"
      semver "^6.3.1"
  
  "@babel/helper-module-imports@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.24.7.tgz"
    integrity sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==
    dependencies:
      "@babel/traverse" "^7.24.7"
      "@babel/types" "^7.24.7"
  
  "@babel/helper-module-transforms@^7.25.2":
    version "7.25.2"
    resolved "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.25.2.tgz"
    integrity sha512-BjyRAbix6j/wv83ftcVJmBt72QtHI56C7JXZoG2xATiLpmoC7dpd8WnkikExHDVPpi/3qCmO6WY1EaXOluiecQ==
    dependencies:
      "@babel/helper-module-imports" "^7.24.7"
      "@babel/helper-simple-access" "^7.24.7"
      "@babel/helper-validator-identifier" "^7.24.7"
      "@babel/traverse" "^7.25.2"
  
  "@babel/helper-plugin-utils@^7.24.7", "@babel/helper-plugin-utils@^7.24.8":
    version "7.24.8"
    resolved "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.8.tgz"
    integrity sha512-FFWx5142D8h2Mgr/iPVGH5G7w6jDn4jUSpZTyDnQO0Yn7Ks2Kuz6Pci8H6MPCoUJegd/UZQ3tAvfLCxQSnWWwg==
  
  "@babel/helper-simple-access@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.24.7.tgz"
    integrity sha512-zBAIvbCMh5Ts+b86r/CjU+4XGYIs+R1j951gxI3KmmxBMhCg4oQMsv6ZXQ64XOm/cvzfU1FmoCyt6+owc5QMYg==
    dependencies:
      "@babel/traverse" "^7.24.7"
      "@babel/types" "^7.24.7"
  
  "@babel/helper-string-parser@^7.24.8":
    version "7.24.8"
    resolved "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.24.8.tgz"
    integrity sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==
  
  "@babel/helper-validator-identifier@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.7.tgz"
    integrity sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==
  
  "@babel/helper-validator-option@^7.24.7", "@babel/helper-validator-option@^7.24.8":
    version "7.24.8"
    resolved "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.24.8.tgz"
    integrity sha512-xb8t9tD1MHLungh/AIoWYN+gVHaB9kwlu8gffXGSt3FFEIT7RjS+xWbc2vUD1UTZdIpKj/ab3rdqJ7ufngyi2Q==
  
  "@babel/helpers@^7.25.0":
    version "7.25.6"
    resolved "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.25.6.tgz"
    integrity sha512-Xg0tn4HcfTijTwfDwYlvVCl43V6h4KyVVX2aEm4qdO/PC6L2YvzLHFdmxhoeSA3eslcE6+ZVXHgWwopXYLNq4Q==
    dependencies:
      "@babel/template" "^7.25.0"
      "@babel/types" "^7.25.6"
  
  "@babel/highlight@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.24.7.tgz"
    integrity sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==
    dependencies:
      "@babel/helper-validator-identifier" "^7.24.7"
      chalk "^2.4.2"
      js-tokens "^4.0.0"
      picocolors "^1.0.0"
  
  "@babel/parser@^7.25.0", "@babel/parser@^7.25.6":
    version "7.25.6"
    resolved "https://registry.npmmirror.com/@babel/parser/-/parser-7.25.6.tgz"
    integrity sha512-trGdfBdbD0l1ZPmcJ83eNxB9rbEax4ALFTF7fN386TMYbeCQbyme5cOEXQhbGXKebwGaB/J52w1mrklMcbgy6Q==
    dependencies:
      "@babel/types" "^7.25.6"
  
  "@babel/plugin-syntax-jsx@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.7.tgz"
    integrity sha512-6ddciUPe/mpMnOKv/U+RSd2vvVy+Yw/JfBB0ZHYjEZt9NLHmCUylNYlsbqCCS1Bffjlb0fCwC9Vqz+sBz6PsiQ==
    dependencies:
      "@babel/helper-plugin-utils" "^7.24.7"
  
  "@babel/plugin-transform-react-display-name@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.24.7.tgz"
    integrity sha512-H/Snz9PFxKsS1JLI4dJLtnJgCJRoo0AUm3chP6NYr+9En1JMKloheEiLIhlp5MDVznWo+H3AAC1Mc8lmUEpsgg==
    dependencies:
      "@babel/helper-plugin-utils" "^7.24.7"
  
  "@babel/plugin-transform-react-jsx-development@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.24.7.tgz"
    integrity sha512-QG9EnzoGn+Qar7rxuW+ZOsbWOt56FvvI93xInqsZDC5fsekx1AlIO4KIJ5M+D0p0SqSH156EpmZyXq630B8OlQ==
    dependencies:
      "@babel/plugin-transform-react-jsx" "^7.24.7"
  
  "@babel/plugin-transform-react-jsx@^7.24.7":
    version "7.25.2"
    resolved "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.25.2.tgz"
    integrity sha512-KQsqEAVBpU82NM/B/N9j9WOdphom1SZH3R+2V7INrQUH+V9EBFwZsEJl8eBIVeQE62FxJCc70jzEZwqU7RcVqA==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.24.7"
      "@babel/helper-module-imports" "^7.24.7"
      "@babel/helper-plugin-utils" "^7.24.8"
      "@babel/plugin-syntax-jsx" "^7.24.7"
      "@babel/types" "^7.25.2"
  
  "@babel/plugin-transform-react-pure-annotations@^7.24.7":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.24.7.tgz"
    integrity sha512-PLgBVk3fzbmEjBJ/u8kFzOqS9tUeDjiaWud/rRym/yjCo/M9cASPlnrd2ZmmZpQT40fOOrvR8jh+n8jikrOhNA==
    dependencies:
      "@babel/helper-annotate-as-pure" "^7.24.7"
      "@babel/helper-plugin-utils" "^7.24.7"
  
  "@babel/preset-react@^7.23.3":
    version "7.24.7"
    resolved "https://registry.npmmirror.com/@babel/preset-react/-/preset-react-7.24.7.tgz"
    integrity sha512-AAH4lEkpmzFWrGVlHaxJB7RLH21uPQ9+He+eFLWHmF9IuFQVugz8eAsamaW0DXRrTfco5zj1wWtpdcXJUOfsag==
    dependencies:
      "@babel/helper-plugin-utils" "^7.24.7"
      "@babel/helper-validator-option" "^7.24.7"
      "@babel/plugin-transform-react-display-name" "^7.24.7"
      "@babel/plugin-transform-react-jsx" "^7.24.7"
      "@babel/plugin-transform-react-jsx-development" "^7.24.7"
      "@babel/plugin-transform-react-pure-annotations" "^7.24.7"
  
  "@babel/template@^7.25.0":
    version "7.25.0"
    resolved "https://registry.npmmirror.com/@babel/template/-/template-7.25.0.tgz"
    integrity sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==
    dependencies:
      "@babel/code-frame" "^7.24.7"
      "@babel/parser" "^7.25.0"
      "@babel/types" "^7.25.0"
  
  "@babel/traverse@^7.24.7", "@babel/traverse@^7.25.2":
    version "7.25.6"
    resolved "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.25.6.tgz"
    integrity sha512-9Vrcx5ZW6UwK5tvqsj0nGpp/XzqthkT0dqIc9g1AdtygFToNtTF67XzYS//dm+SAK9cp3B9R4ZO/46p63SCjlQ==
    dependencies:
      "@babel/code-frame" "^7.24.7"
      "@babel/generator" "^7.25.6"
      "@babel/parser" "^7.25.6"
      "@babel/template" "^7.25.0"
      "@babel/types" "^7.25.6"
      debug "^4.3.1"
      globals "^11.1.0"
  
  "@babel/types@^7.24.7", "@babel/types@^7.25.0", "@babel/types@^7.25.2", "@babel/types@^7.25.6":
    version "7.25.6"
    resolved "https://registry.npmmirror.com/@babel/types/-/types-7.25.6.tgz"
    integrity sha512-/l42B1qxpG6RdfYf343Uw1vmDjeNhneUXtzhojE7pDgfpEypmRhI6j1kr17XCVv4Cgl9HdAiQY2x0GwKm7rWCw==
    dependencies:
      "@babel/helper-string-parser" "^7.24.8"
      "@babel/helper-validator-identifier" "^7.24.7"
      to-fast-properties "^2.0.0"
  
  "@es-joy/jsdoccomment@~0.41.0":
    version "0.41.0"
    resolved "https://registry.npmmirror.com/@es-joy/jsdoccomment/-/jsdoccomment-0.41.0.tgz"
    integrity sha512-aKUhyn1QI5Ksbqcr3fFJj16p99QdjUxXAEuFst1Z47DRyoiMwivIH9MV/ARcJOCXVjPfjITciej8ZD2O/6qUmw==
    dependencies:
      comment-parser "1.4.1"
      esquery "^1.5.0"
      jsdoc-type-pratt-parser "~4.0.0"
  
  "@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0":
    version "4.4.0"
    resolved "https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
    integrity sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==
    dependencies:
      eslint-visitor-keys "^3.3.0"
  
  "@eslint-community/regexpp@^4.5.1", "@eslint-community/regexpp@^4.6.1":
    version "4.11.0"
    resolved "https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.11.0.tgz"
    integrity sha512-G/M/tIiMrTAxEWRfLfQJMmGNX28IxBg4PBz8XqQhqUHLFI6TL2htpIB1iQCj144V5ee/JaKyT9/WZ0MGZWfA7A==
  
  "@eslint/eslintrc@^2.1.4":
    version "2.1.4"
    resolved "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
    integrity sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==
    dependencies:
      ajv "^6.12.4"
      debug "^4.3.2"
      espree "^9.6.0"
      globals "^13.19.0"
      ignore "^5.2.0"
      import-fresh "^3.2.1"
      js-yaml "^4.1.0"
      minimatch "^3.1.2"
      strip-json-comments "^3.1.1"
  
  "@eslint/js@8.57.0":
    version "8.57.0"
    resolved "https://registry.npmmirror.com/@eslint/js/-/js-8.57.0.tgz"
    integrity sha512-Ys+3g2TaW7gADOJzPt83SJtCDhMjndcDMFVQ/Tj9iA1BfJzFKD9mAUXT3OenpuPHbI6P/myECxRJrofUsDx/5g==
  
  "@humanwhocodes/config-array@^0.11.14":
    version "0.11.14"
    resolved "https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.11.14.tgz"
    integrity sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg==
    dependencies:
      "@humanwhocodes/object-schema" "^2.0.2"
      debug "^4.3.1"
      minimatch "^3.0.5"
  
  "@humanwhocodes/module-importer@^1.0.1":
    version "1.0.1"
    resolved "https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
    integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==
  
  "@humanwhocodes/object-schema@^2.0.2":
    version "2.0.3"
    resolved "https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz"
    integrity sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==
  
  "@jridgewell/gen-mapping@^0.3.5":
    version "0.3.5"
    resolved "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
    integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
    dependencies:
      "@jridgewell/set-array" "^1.2.1"
      "@jridgewell/sourcemap-codec" "^1.4.10"
      "@jridgewell/trace-mapping" "^0.3.24"
  
  "@jridgewell/resolve-uri@^3.1.0":
    version "3.1.2"
    resolved "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
    integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==
  
  "@jridgewell/set-array@^1.2.1":
    version "1.2.1"
    resolved "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.2.1.tgz"
    integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==
  
  "@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
    version "1.5.0"
    resolved "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
    integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==
  
  "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
    version "0.3.25"
    resolved "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
    integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
    dependencies:
      "@jridgewell/resolve-uri" "^3.1.0"
      "@jridgewell/sourcemap-codec" "^1.4.14"
  
  "@lukeed/csprng@^1.0.0":
    version "1.1.0"
    resolved "https://registry.npmmirror.com/@lukeed/csprng/-/csprng-1.1.0.tgz"
    integrity sha512-Z7C/xXCiGWsg0KuKsHTKJxbWhpI3Vs5GwLfOean7MGyVFGqdRgBbAjOCh6u4bbjPc/8MJ2pZmK/0DLdCbivLDA==
  
  "@mapbox/geojson-rewind@^0.5.2":
    version "0.5.2"
    resolved "https://registry.npmmirror.com/@mapbox/geojson-rewind/-/geojson-rewind-0.5.2.tgz"
    integrity sha512-tJaT+RbYGJYStt7wI3cq4Nl4SXxG8W7JDG5DMJu97V25RnbNg3QtQtf+KD+VLjNpWKYsRvXDNmNrBgEETr1ifA==
    dependencies:
      get-stream "^6.0.1"
      minimist "^1.2.6"
  
  "@mapbox/jsonlint-lines-primitives@^2.0.2":
    version "2.0.2"
    resolved "https://registry.npmmirror.com/@mapbox/jsonlint-lines-primitives/-/jsonlint-lines-primitives-2.0.2.tgz"
    integrity sha512-rY0o9A5ECsTQRVhv7tL/OyDpGAoUB4tTvLiW1DSzQGq4bvTPhNw1VpSNjDJc5GFZ2XuyOtSWSVN05qOtcD71qQ==
  
  "@mapbox/mapbox-gl-supported@^2.0.1":
    version "2.0.1"
    resolved "https://registry.npmmirror.com/@mapbox/mapbox-gl-supported/-/mapbox-gl-supported-2.0.1.tgz"
    integrity sha512-HP6XvfNIzfoMVfyGjBckjiAOQK9WfX0ywdLubuPMPv+Vqf5fj0uCbgBQYpiqcWZT6cbyyRnTSXDheT1ugvF6UQ==
  
  "@mapbox/point-geometry@0.1.0", "@mapbox/point-geometry@^0.1.0", "@mapbox/point-geometry@~0.1.0":
    version "0.1.0"
    resolved "https://registry.npmmirror.com/@mapbox/point-geometry/-/point-geometry-0.1.0.tgz"
    integrity sha512-6j56HdLTwWGO0fJPlrZtdU/B13q8Uwmo18Ck2GnGgN9PCFyKTZ3UbXeEdRFh18i9XQ92eH2VdtpJHpBD3aripQ==
  
  "@mapbox/tiny-sdf@^2.0.6":
    version "2.0.6"
    resolved "https://registry.npmmirror.com/@mapbox/tiny-sdf/-/tiny-sdf-2.0.6.tgz"
    integrity sha512-qMqa27TLw+ZQz5Jk+RcwZGH7BQf5G/TrutJhspsca/3SHwmgKQ1iq+d3Jxz5oysPVYTGP6aXxCo5Lk9Er6YBAA==
  
  "@mapbox/unitbezier@^0.0.1":
    version "0.0.1"
    resolved "https://registry.npmmirror.com/@mapbox/unitbezier/-/unitbezier-0.0.1.tgz"
    integrity sha512-nMkuDXFv60aBr9soUG5q+GvZYL+2KZHVvsqFCzqnkGEf46U2fvmytHaEVc1/YZbiLn8X+eR3QzX1+dwDO1lxlw==
  
  "@mapbox/vector-tile@^1.3.1":
    version "1.3.1"
    resolved "https://registry.npmmirror.com/@mapbox/vector-tile/-/vector-tile-1.3.1.tgz"
    integrity sha512-MCEddb8u44/xfQ3oD+Srl/tNcQoqTw3goGk2oLsrFxOTc3dUp+kAnby3PvAeeBYSMSjSPD1nd1AJA6W49WnoUw==
    dependencies:
      "@mapbox/point-geometry" "~0.1.0"
  
  "@mapbox/whoots-js@^3.1.0":
    version "3.1.0"
    resolved "https://registry.npmmirror.com/@mapbox/whoots-js/-/whoots-js-3.1.0.tgz"
    integrity sha512-Es6WcD0nO5l+2BOQS4uLfNPYQaNDfbot3X1XUoloz+x0mPDS3eeORZJl06HXjwBG1fOGwCRnzK88LMdxKRrd6Q==
  
  "@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
    version "5.1.1-v1"
    resolved "https://registry.npmmirror.com/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz"
    integrity sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==
    dependencies:
      eslint-scope "5.1.1"
  
  "@nodelib/fs.scandir@2.1.5":
    version "2.1.5"
    resolved "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
    integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
    dependencies:
      "@nodelib/fs.stat" "2.0.5"
      run-parallel "^1.1.9"
  
  "@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
    version "2.0.5"
    resolved "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
    integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==
  
  "@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
    version "1.2.8"
    resolved "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
    integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
    dependencies:
      "@nodelib/fs.scandir" "2.1.5"
      fastq "^1.6.0"
  
  "@nolyfill/is-core-module@1.0.39":
    version "1.0.39"
    resolved "https://registry.npmmirror.com/@nolyfill/is-core-module/-/is-core-module-1.0.39.tgz"
    integrity sha512-nn5ozdjYQpUCZlWGuxcJY/KpxkWQs4DcbMCmKojjyrYDEAGy4Ce19NN4v5MduafTwJlbKc99UA8YhSVqq9yPZA==
  
  "@turf/along@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/along/-/along-6.5.0.tgz"
    integrity sha512-LLyWQ0AARqJCmMcIEAXF4GEu8usmd4Kbz3qk1Oy5HoRNpZX47+i5exQtmIWKdqJ1MMhW26fCTXgpsEs5zgJ5gw==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/angle@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/angle/-/angle-6.5.0.tgz"
    integrity sha512-4pXMbWhFofJJAOvTMCns6N4C8CMd5Ih4O2jSAG9b3dDHakj3O4yN1+Zbm+NUei+eVEZ9gFeVp9svE3aMDenIkw==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
  
  "@turf/area@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/area/-/area-6.5.0.tgz"
    integrity sha512-xCZdiuojokLbQ+29qR6qoMD89hv+JAgWjLrwSEWL+3JV8IXKeNFl6XkEJz9HGkVpnXvQKJoRz4/liT+8ZZ5Jyg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/bbox-clip@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bbox-clip/-/bbox-clip-6.5.0.tgz"
    integrity sha512-F6PaIRF8WMp8EmgU/Ke5B1Y6/pia14UAYB5TiBC668w5rVVjy5L8rTm/m2lEkkDMHlzoP9vNY4pxpNthE7rLcQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/bbox-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bbox-polygon/-/bbox-polygon-6.5.0.tgz"
    integrity sha512-+/r0NyL1lOG3zKZmmf6L8ommU07HliP4dgYToMoTxqzsWzyLjaj/OzgQ8rBmv703WJX+aS6yCmLuIhYqyufyuw==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/bbox@*":
    version "7.1.0"
    resolved "https://registry.npmmirror.com/@turf/bbox/-/bbox-7.1.0.tgz"
    integrity sha512-PdWPz9tW86PD78vSZj2fiRaB8JhUHy6piSa/QXb83lucxPK+HTAdzlDQMTKj5okRCU8Ox/25IR2ep9T8NdopRA==
    dependencies:
      "@turf/helpers" "^7.1.0"
      "@turf/meta" "^7.1.0"
      "@types/geojson" "^7946.0.10"
      tslib "^2.6.2"
  
  "@turf/bbox@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bbox/-/bbox-6.5.0.tgz"
    integrity sha512-RBbLaao5hXTYyyg577iuMtDB8ehxMlUqHEJiMs8jT1GHkFhr6sYre3lmLsPeYEi/ZKj5TP5tt7fkzNdJ4GIVyw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/bearing@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bearing/-/bearing-6.5.0.tgz"
    integrity sha512-dxINYhIEMzgDOztyMZc20I7ssYVNEpSv04VbMo5YPQsqa80KO3TFvbuCahMsCAW5z8Tncc8dwBlEFrmRjJG33A==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/bezier-spline@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/bezier-spline/-/bezier-spline-6.5.0.tgz"
    integrity sha512-vokPaurTd4PF96rRgGVm6zYYC5r1u98ZsG+wZEv9y3kJTuJRX/O3xIY2QnTGTdbVmAJN1ouOsD0RoZYaVoXORQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-clockwise@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-clockwise/-/boolean-clockwise-6.5.0.tgz"
    integrity sha512-45+C7LC5RMbRWrxh3Z0Eihsc8db1VGBO5d9BLTOAwU4jR6SgsunTfRWR16X7JUwIDYlCVEmnjcXJNi/kIU3VIw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-contains@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-contains/-/boolean-contains-6.5.0.tgz"
    integrity sha512-4m8cJpbw+YQcKVGi8y0cHhBUnYT+QRfx6wzM4GI1IdtYH3p4oh/DOBJKrepQyiDzFDaNIjxuWXBh0ai1zVwOQQ==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/boolean-point-on-line" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-crosses@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-crosses/-/boolean-crosses-6.5.0.tgz"
    integrity sha512-gvshbTPhAHporTlQwBJqyfW+2yV8q/mOTxG6PzRVl6ARsqNoqYQWkd4MLug7OmAqVyBzLK3201uAeBjxbGw0Ng==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/polygon-to-line" "^6.5.0"
  
  "@turf/boolean-disjoint@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-disjoint/-/boolean-disjoint-6.5.0.tgz"
    integrity sha512-rZ2ozlrRLIAGo2bjQ/ZUu4oZ/+ZjGvLkN5CKXSKBcu6xFO6k2bgqeM8a1836tAW+Pqp/ZFsTA5fZHsJZvP2D5g==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/polygon-to-line" "^6.5.0"
  
  "@turf/boolean-equal@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-equal/-/boolean-equal-6.5.0.tgz"
    integrity sha512-cY0M3yoLC26mhAnjv1gyYNQjn7wxIXmL2hBmI/qs8g5uKuC2hRWi13ydufE3k4x0aNRjFGlg41fjoYLwaVF+9Q==
    dependencies:
      "@turf/clean-coords" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      geojson-equality "0.1.6"
  
  "@turf/boolean-intersects@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-intersects/-/boolean-intersects-6.5.0.tgz"
    integrity sha512-nIxkizjRdjKCYFQMnml6cjPsDOBCThrt+nkqtSEcxkKMhAQj5OO7o2CecioNTaX8EayqwMGVKcsz27oP4mKPTw==
    dependencies:
      "@turf/boolean-disjoint" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/boolean-overlap@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-overlap/-/boolean-overlap-6.5.0.tgz"
    integrity sha512-8btMIdnbXVWUa1M7D4shyaSGxLRw6NjMcqKBcsTXcZdnaixl22k7ar7BvIzkaRYN3SFECk9VGXfLncNS3ckQUw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/line-overlap" "^6.5.0"
      "@turf/meta" "^6.5.0"
      geojson-equality "0.1.6"
  
  "@turf/boolean-parallel@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-parallel/-/boolean-parallel-6.5.0.tgz"
    integrity sha512-aSHJsr1nq9e5TthZGZ9CZYeXklJyRgR5kCLm5X4urz7+MotMOp/LsGOsvKvK9NeUl9+8OUmfMn8EFTT8LkcvIQ==
    dependencies:
      "@turf/clean-coords" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
  
  "@turf/boolean-point-in-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-point-in-polygon/-/boolean-point-in-polygon-6.5.0.tgz"
    integrity sha512-DtSuVFB26SI+hj0SjrvXowGTUCHlgevPAIsukssW6BG5MlNSBQAo70wpICBNJL6RjukXg8d2eXaAWuD/CqL00A==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-point-on-line@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-point-on-line/-/boolean-point-on-line-6.5.0.tgz"
    integrity sha512-A1BbuQ0LceLHvq7F/P7w3QvfpmZqbmViIUPHdNLvZimFNLo4e6IQunmzbe+8aSStH9QRZm3VOflyvNeXvvpZEQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/boolean-within@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/boolean-within/-/boolean-within-6.5.0.tgz"
    integrity sha512-YQB3oU18Inx35C/LU930D36RAVe7LDXk1kWsQ8mLmuqYn9YdPsDQTMTkLJMhoQ8EbN7QTdy333xRQ4MYgToteQ==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/boolean-point-on-line" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/buffer@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/buffer/-/buffer-6.5.0.tgz"
    integrity sha512-qeX4N6+PPWbKqp1AVkBVWFerGjMYMUyencwfnkCesoznU6qvfugFHNAngNqIBVnJjZ5n8IFyOf+akcxnrt9sNg==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/center" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/projection" "^6.5.0"
      d3-geo "1.7.1"
      turf-jsts "*"
  
  "@turf/center-mean@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/center-mean/-/center-mean-6.5.0.tgz"
    integrity sha512-AAX6f4bVn12pTVrMUiB9KrnV94BgeBKpyg3YpfnEbBpkN/znfVhL8dG8IxMAxAoSZ61Zt9WLY34HfENveuOZ7Q==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/center-median@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/center-median/-/center-median-6.5.0.tgz"
    integrity sha512-dT8Ndu5CiZkPrj15PBvslpuf01ky41DEYEPxS01LOxp5HOUHXp1oJxsPxvc+i/wK4BwccPNzU1vzJ0S4emd1KQ==
    dependencies:
      "@turf/center-mean" "^6.5.0"
      "@turf/centroid" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/center-of-mass@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/center-of-mass/-/center-of-mass-6.5.0.tgz"
    integrity sha512-EWrriU6LraOfPN7m1jZi+1NLTKNkuIsGLZc2+Y8zbGruvUW+QV7K0nhf7iZWutlxHXTBqEXHbKue/o79IumAsQ==
    dependencies:
      "@turf/centroid" "^6.5.0"
      "@turf/convex" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/center@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/center/-/center-6.5.0.tgz"
    integrity sha512-T8KtMTfSATWcAX088rEDKjyvQCBkUsLnK/Txb6/8WUXIeOZyHu42G7MkdkHRoHtwieLdduDdmPLFyTdG5/e7ZQ==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/centroid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/centroid/-/centroid-6.5.0.tgz"
    integrity sha512-MwE1oq5E3isewPprEClbfU5pXljIK/GUOMbn22UM3IFPDJX0KeoyLNwghszkdmFp/qMGL/M13MMWvU+GNLXP/A==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/circle@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/circle/-/circle-6.5.0.tgz"
    integrity sha512-oU1+Kq9DgRnoSbWFHKnnUdTmtcRUMmHoV9DjTXu9vOLNV5OWtAAh1VZ+mzsioGGzoDNT/V5igbFOkMfBQc0B6A==
    dependencies:
      "@turf/destination" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/clean-coords@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clean-coords/-/clean-coords-6.5.0.tgz"
    integrity sha512-EMX7gyZz0WTH/ET7xV8MyrExywfm9qUi0/MY89yNffzGIEHuFfqwhcCqZ8O00rZIPZHUTxpmsxQSTfzJJA1CPw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/clone@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clone/-/clone-6.5.0.tgz"
    integrity sha512-mzVtTFj/QycXOn6ig+annKrM6ZlimreKYz6f/GSERytOpgzodbQyOgkfwru100O1KQhhjSudKK4DsQ0oyi9cTw==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/clusters-dbscan@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clusters-dbscan/-/clusters-dbscan-6.5.0.tgz"
    integrity sha512-SxZEE4kADU9DqLRiT53QZBBhu8EP9skviSyl+FGj08Y01xfICM/RR9ACUdM0aEQimhpu+ZpRVcUK+2jtiCGrYQ==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
      density-clustering "1.3.0"
  
  "@turf/clusters-kmeans@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clusters-kmeans/-/clusters-kmeans-6.5.0.tgz"
    integrity sha512-DwacD5+YO8kwDPKaXwT9DV46tMBVNsbi1IzdajZu1JDSWoN7yc7N9Qt88oi+p30583O0UPVkAK+A10WAQv4mUw==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      skmeans "0.9.7"
  
  "@turf/clusters@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/clusters/-/clusters-6.5.0.tgz"
    integrity sha512-Y6gfnTJzQ1hdLfCsyd5zApNbfLIxYEpmDibHUqR5z03Lpe02pa78JtgrgUNt1seeO/aJ4TG1NLN8V5gOrHk04g==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/collect@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/collect/-/collect-6.5.0.tgz"
    integrity sha512-4dN/T6LNnRg099m97BJeOcTA5fSI8cu87Ydgfibewd2KQwBexO69AnjEFqfPX3Wj+Zvisj1uAVIZbPmSSrZkjg==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      rbush "2.x"
  
  "@turf/combine@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/combine/-/combine-6.5.0.tgz"
    integrity sha512-Q8EIC4OtAcHiJB3C4R+FpB4LANiT90t17uOd851qkM2/o6m39bfN5Mv0PWqMZIHWrrosZqRqoY9dJnzz/rJxYQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/concave@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/concave/-/concave-6.5.0.tgz"
    integrity sha512-I/sUmUC8TC5h/E2vPwxVht+nRt+TnXIPRoztDFvS8/Y0+cBDple9inLSo9nnPXMXidrBlGXZ9vQx/BjZUJgsRQ==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/tin" "^6.5.0"
      topojson-client "3.x"
      topojson-server "3.x"
  
  "@turf/convex@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/convex/-/convex-6.5.0.tgz"
    integrity sha512-x7ZwC5z7PJB0SBwNh7JCeCNx7Iu+QSrH7fYgK0RhhNop13TqUlvHMirMLRgf2db1DqUetrAO2qHJeIuasquUWg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
      concaveman "*"
  
  "@turf/destination@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/destination/-/destination-6.5.0.tgz"
    integrity sha512-4cnWQlNC8d1tItOz9B4pmJdWpXqS0vEvv65bI/Pj/genJnsL7evI0/Xw42RvEGROS481MPiU80xzvwxEvhQiMQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/difference@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/difference/-/difference-6.5.0.tgz"
    integrity sha512-l8iR5uJqvI+5Fs6leNbhPY5t/a3vipUF/3AeVLpwPQcgmedNXyheYuy07PcMGH5Jdpi5gItOiTqwiU/bUH4b3A==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/dissolve@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/dissolve/-/dissolve-6.5.0.tgz"
    integrity sha512-WBVbpm9zLTp0Bl9CE35NomTaOL1c4TQCtEoO43YaAhNEWJOOIhZMFJyr8mbvYruKl817KinT3x7aYjjCMjTAsQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/distance-weight@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/distance-weight/-/distance-weight-6.5.0.tgz"
    integrity sha512-a8qBKkgVNvPKBfZfEJZnC3DV7dfIsC3UIdpRci/iap/wZLH41EmS90nM+BokAJflUHYy8PqE44wySGWHN1FXrQ==
    dependencies:
      "@turf/centroid" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/distance@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/distance/-/distance-6.5.0.tgz"
    integrity sha512-xzykSLfoURec5qvQJcfifw/1mJa+5UwByZZ5TZ8iaqjGYN0vomhV9aiSLeYdUGtYRESZ+DYC/OzY+4RclZYgMg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/ellipse@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/ellipse/-/ellipse-6.5.0.tgz"
    integrity sha512-kuXtwFviw/JqnyJXF1mrR/cb496zDTSbGKtSiolWMNImYzGGkbsAsFTjwJYgD7+4FixHjp0uQPzo70KDf3AIBw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
      "@turf/transform-rotate" "^6.5.0"
  
  "@turf/envelope@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/envelope/-/envelope-6.5.0.tgz"
    integrity sha512-9Z+FnBWvOGOU4X+fMZxYFs1HjFlkKqsddLuMknRaqcJd6t+NIv5DWvPtDL8ATD2GEExYDiFLwMdckfr1yqJgHA==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/bbox-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/explode@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/explode/-/explode-6.5.0.tgz"
    integrity sha512-6cSvMrnHm2qAsace6pw9cDmK2buAlw8+tjeJVXMfMyY+w7ZUi1rprWMsY92J7s2Dar63Bv09n56/1V7+tcj52Q==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/flatten@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/flatten/-/flatten-6.5.0.tgz"
    integrity sha512-IBZVwoNLVNT6U/bcUUllubgElzpMsNoCw8tLqBw6dfYg9ObGmpEjf9BIYLr7a2Yn5ZR4l7YIj2T7kD5uJjZADQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/flip@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/flip/-/flip-6.5.0.tgz"
    integrity sha512-oyikJFNjt2LmIXQqgOGLvt70RgE2lyzPMloYWM7OR5oIFGRiBvqVD2hA6MNw6JewIm30fWZ8DQJw1NHXJTJPbg==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/great-circle@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/great-circle/-/great-circle-6.5.0.tgz"
    integrity sha512-7ovyi3HaKOXdFyN7yy1yOMa8IyOvV46RC1QOQTT+RYUN8ke10eyqExwBpL9RFUPvlpoTzoYbM/+lWPogQlFncg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/helpers@6.x", "@turf/helpers@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/helpers/-/helpers-6.5.0.tgz"
    integrity sha512-VbI1dV5bLFzohYYdgqwikdMVpe7pJ9X3E+dlr425wa2/sMJqYDhTO++ec38/pcPvPE6oD9WEEeU3Xu3gza+VPw==
  
  "@turf/helpers@^7.1.0":
    version "7.1.0"
    resolved "https://registry.npmmirror.com/@turf/helpers/-/helpers-7.1.0.tgz"
    integrity sha512-dTeILEUVeNbaEeoZUOhxH5auv7WWlOShbx7QSd4s0T4Z0/iz90z9yaVCtZOLbU89umKotwKaJQltBNO9CzVgaQ==
    dependencies:
      "@types/geojson" "^7946.0.10"
      tslib "^2.6.2"
  
  "@turf/hex-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/hex-grid/-/hex-grid-6.5.0.tgz"
    integrity sha512-Ln3tc2tgZT8etDOldgc6e741Smg1CsMKAz1/Mlel+MEL5Ynv2mhx3m0q4J9IB1F3a4MNjDeVvm8drAaf9SF33g==
    dependencies:
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/intersect" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/interpolate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/interpolate/-/interpolate-6.5.0.tgz"
    integrity sha512-LSH5fMeiGyuDZ4WrDJNgh81d2DnNDUVJtuFryJFup8PV8jbs46lQGfI3r1DJ2p1IlEJIz3pmAZYeTfMMoeeohw==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/centroid" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/hex-grid" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/point-grid" "^6.5.0"
      "@turf/square-grid" "^6.5.0"
      "@turf/triangle-grid" "^6.5.0"
  
  "@turf/intersect@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/intersect/-/intersect-6.5.0.tgz"
    integrity sha512-2legGJeKrfFkzntcd4GouPugoqPUjexPZnOvfez+3SfIMrHvulw8qV8u7pfVyn2Yqs53yoVCEjS5sEpvQ5YRQg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/invariant@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/invariant/-/invariant-6.5.0.tgz"
    integrity sha512-Wv8PRNCtPD31UVbdJE/KVAWKe7l6US+lJItRR/HOEW3eh+U/JwRCSUl/KZ7bmjM/C+zLNoreM2TU6OoLACs4eg==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/isobands@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/isobands/-/isobands-6.5.0.tgz"
    integrity sha512-4h6sjBPhRwMVuFaVBv70YB7eGz+iw0bhPRnp+8JBdX1UPJSXhoi/ZF2rACemRUr0HkdVB/a1r9gC32vn5IAEkw==
    dependencies:
      "@turf/area" "^6.5.0"
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/explode" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      object-assign "*"
  
  "@turf/isolines@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/isolines/-/isolines-6.5.0.tgz"
    integrity sha512-6ElhiLCopxWlv4tPoxiCzASWt/jMRvmp6mRYrpzOm3EUl75OhHKa/Pu6Y9nWtCMmVC/RcWtiiweUocbPLZLm0A==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      object-assign "*"
  
  "@turf/kinks@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/kinks/-/kinks-6.5.0.tgz"
    integrity sha512-ViCngdPt1eEL7hYUHR2eHR662GvCgTc35ZJFaNR6kRtr6D8plLaDju0FILeFFWSc+o8e3fwxZEJKmFj9IzPiIQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/length@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/length/-/length-6.5.0.tgz"
    integrity sha512-5pL5/pnw52fck3oRsHDcSGrj9HibvtlrZ0QNy2OcW8qBFDNgZ4jtl6U7eATVoyWPKBHszW3dWETW+iLV7UARig==
    dependencies:
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/line-arc@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-arc/-/line-arc-6.5.0.tgz"
    integrity sha512-I6c+V6mIyEwbtg9P9zSFF89T7QPe1DPTG3MJJ6Cm1MrAY0MdejwQKOpsvNl8LDU2ekHOlz2kHpPVR7VJsoMllA==
    dependencies:
      "@turf/circle" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/line-chunk@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-chunk/-/line-chunk-6.5.0.tgz"
    integrity sha512-i1FGE6YJaaYa+IJesTfyRRQZP31QouS+wh/pa6O3CC0q4T7LtHigyBSYjrbjSLfn2EVPYGlPCMFEqNWCOkC6zg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/length" "^6.5.0"
      "@turf/line-slice-along" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/line-intersect@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-intersect/-/line-intersect-6.5.0.tgz"
    integrity sha512-CS6R1tZvVQD390G9Ea4pmpM6mJGPWoL82jD46y0q1KSor9s6HupMIo1kY4Ny+AEYQl9jd21V3Scz20eldpbTVA==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/meta" "^6.5.0"
      geojson-rbush "3.x"
  
  "@turf/line-offset@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-offset/-/line-offset-6.5.0.tgz"
    integrity sha512-CEXZbKgyz8r72qRvPchK0dxqsq8IQBdH275FE6o4MrBkzMcoZsfSjghtXzKaz9vvro+HfIXal0sTk2mqV1lQTw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/line-overlap@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-overlap/-/line-overlap-6.5.0.tgz"
    integrity sha512-xHOaWLd0hkaC/1OLcStCpfq55lPHpPNadZySDXYiYjEz5HXr1oKmtMYpn0wGizsLwrOixRdEp+j7bL8dPt4ojQ==
    dependencies:
      "@turf/boolean-point-on-line" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/nearest-point-on-line" "^6.5.0"
      deep-equal "1.x"
      geojson-rbush "3.x"
  
  "@turf/line-segment@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-segment/-/line-segment-6.5.0.tgz"
    integrity sha512-jI625Ho4jSuJESNq66Mmi290ZJ5pPZiQZruPVpmHkUw257Pew0alMmb6YrqYNnLUuiVVONxAAKXUVeeUGtycfw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/line-slice-along@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-slice-along/-/line-slice-along-6.5.0.tgz"
    integrity sha512-KHJRU6KpHrAj+BTgTNqby6VCTnDzG6a1sJx/I3hNvqMBLvWVA2IrkR9L9DtsQsVY63IBwVdQDqiwCuZLDQh4Ng==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/line-slice@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-slice/-/line-slice-6.5.0.tgz"
    integrity sha512-vDqJxve9tBHhOaVVFXqVjF5qDzGtKWviyjbyi2QnSnxyFAmLlLnBfMX8TLQCAf2GxHibB95RO5FBE6I2KVPRuw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/nearest-point-on-line" "^6.5.0"
  
  "@turf/line-split@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-split/-/line-split-6.5.0.tgz"
    integrity sha512-/rwUMVr9OI2ccJjw7/6eTN53URtGThNSD5I0GgxyFXMtxWiloRJ9MTff8jBbtPWrRka/Sh2GkwucVRAEakx9Sw==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/nearest-point-on-line" "^6.5.0"
      "@turf/square" "^6.5.0"
      "@turf/truncate" "^6.5.0"
      geojson-rbush "3.x"
  
  "@turf/line-to-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/line-to-polygon/-/line-to-polygon-6.5.0.tgz"
    integrity sha512-qYBuRCJJL8Gx27OwCD1TMijM/9XjRgXH/m/TyuND4OXedBpIWlK5VbTIO2gJ8OCfznBBddpjiObLBrkuxTpN4Q==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/mask@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/mask/-/mask-6.5.0.tgz"
    integrity sha512-RQha4aU8LpBrmrkH8CPaaoAfk0Egj5OuXtv6HuCQnHeGNOQt3TQVibTA3Sh4iduq4EPxnZfDjgsOeKtrCA19lg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/meta@6.x", "@turf/meta@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/meta/-/meta-6.5.0.tgz"
    integrity sha512-RrArvtsV0vdsCBegoBtOalgdSOfkBrTJ07VkpiCnq/491W67hnMWmDu7e6Ztw0C3WldRYTXkg3SumfdzZxLBHA==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/meta@^7.1.0":
    version "7.1.0"
    resolved "https://registry.npmmirror.com/@turf/meta/-/meta-7.1.0.tgz"
    integrity sha512-ZgGpWWiKz797Fe8lfRj7HKCkGR+nSJ/5aKXMyofCvLSc2PuYJs/qyyifDPWjASQQCzseJ7AlF2Pc/XQ/3XkkuA==
    dependencies:
      "@turf/helpers" "^7.1.0"
      "@types/geojson" "^7946.0.10"
  
  "@turf/midpoint@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/midpoint/-/midpoint-6.5.0.tgz"
    integrity sha512-MyTzV44IwmVI6ec9fB2OgZ53JGNlgOpaYl9ArKoF49rXpL84F9rNATndbe0+MQIhdkw8IlzA6xVP4lZzfMNVCw==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/moran-index@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/moran-index/-/moran-index-6.5.0.tgz"
    integrity sha512-ItsnhrU2XYtTtTudrM8so4afBCYWNaB0Mfy28NZwLjB5jWuAsvyV+YW+J88+neK/ougKMTawkmjQqodNJaBeLQ==
    dependencies:
      "@turf/distance-weight" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/nearest-point-on-line@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/nearest-point-on-line/-/nearest-point-on-line-6.5.0.tgz"
    integrity sha512-WthrvddddvmymnC+Vf7BrkHGbDOUu6Z3/6bFYUGv1kxw8tiZ6n83/VG6kHz4poHOfS0RaNflzXSkmCi64fLBlg==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/nearest-point-to-line@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/nearest-point-to-line/-/nearest-point-to-line-6.5.0.tgz"
    integrity sha512-PXV7cN0BVzUZdjj6oeb/ESnzXSfWmEMrsfZSDRgqyZ9ytdiIj/eRsnOXLR13LkTdXVOJYDBuf7xt1mLhM4p6+Q==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/point-to-line-distance" "^6.5.0"
      object-assign "*"
  
  "@turf/nearest-point@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/nearest-point/-/nearest-point-6.5.0.tgz"
    integrity sha512-fguV09QxilZv/p94s8SMsXILIAMiaXI5PATq9d7YWijLxWUj6Q/r43kxyoi78Zmwwh1Zfqz9w+bCYUAxZ5+euA==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/planepoint@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/planepoint/-/planepoint-6.5.0.tgz"
    integrity sha512-R3AahA6DUvtFbka1kcJHqZ7DMHmPXDEQpbU5WaglNn7NaCQg9HB0XM0ZfqWcd5u92YXV+Gg8QhC8x5XojfcM4Q==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/point-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/point-grid/-/point-grid-6.5.0.tgz"
    integrity sha512-Iq38lFokNNtQJnOj/RBKmyt6dlof0yhaHEDELaWHuECm1lIZLY3ZbVMwbs+nXkwTAHjKfS/OtMheUBkw+ee49w==
    dependencies:
      "@turf/boolean-within" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/point-on-feature@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/point-on-feature/-/point-on-feature-6.5.0.tgz"
    integrity sha512-bDpuIlvugJhfcF/0awAQ+QI6Om1Y1FFYE8Y/YdxGRongivix850dTeXCo0mDylFdWFPGDo7Mmh9Vo4VxNwW/TA==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/center" "^6.5.0"
      "@turf/explode" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/nearest-point" "^6.5.0"
  
  "@turf/point-to-line-distance@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/point-to-line-distance/-/point-to-line-distance-6.5.0.tgz"
    integrity sha512-opHVQ4vjUhNBly1bob6RWy+F+hsZDH9SA0UW36pIRzfpu27qipU18xup0XXEePfY6+wvhF6yL/WgCO2IbrLqEA==
    dependencies:
      "@turf/bearing" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/projection" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
      "@turf/rhumb-distance" "^6.5.0"
  
  "@turf/points-within-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/points-within-polygon/-/points-within-polygon-6.5.0.tgz"
    integrity sha512-YyuheKqjliDsBDt3Ho73QVZk1VXX1+zIA2gwWvuz8bR1HXOkcuwk/1J76HuFMOQI3WK78wyAi+xbkx268PkQzQ==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/polygon-smooth@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/polygon-smooth/-/polygon-smooth-6.5.0.tgz"
    integrity sha512-LO/X/5hfh/Rk4EfkDBpLlVwt3i6IXdtQccDT9rMjXEP32tRgy0VMFmdkNaXoGlSSKf/1mGqLl4y4wHd86DqKbg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/polygon-tangents@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/polygon-tangents/-/polygon-tangents-6.5.0.tgz"
    integrity sha512-sB4/IUqJMYRQH9jVBwqS/XDitkEfbyqRy+EH/cMRJURTg78eHunvJ708x5r6umXsbiUyQU4eqgPzEylWEQiunw==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/boolean-within" "^6.5.0"
      "@turf/explode" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/nearest-point" "^6.5.0"
  
  "@turf/polygon-to-line@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/polygon-to-line/-/polygon-to-line-6.5.0.tgz"
    integrity sha512-5p4n/ij97EIttAq+ewSnKt0ruvuM+LIDzuczSzuHTpq4oS7Oq8yqg5TQ4nzMVuK41r/tALCk7nAoBuw3Su4Gcw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/polygonize@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/polygonize/-/polygonize-6.5.0.tgz"
    integrity sha512-a/3GzHRaCyzg7tVYHo43QUChCspa99oK4yPqooVIwTC61npFzdrmnywMv0S+WZjHZwK37BrFJGFrZGf6ocmY5w==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/envelope" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/projection@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/projection/-/projection-6.5.0.tgz"
    integrity sha512-/Pgh9mDvQWWu8HRxqpM+tKz8OzgauV+DiOcr3FCjD6ubDnrrmMJlsf6fFJmggw93mtVPrZRL6yyi9aYCQBOIvg==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/random@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/random/-/random-6.5.0.tgz"
    integrity sha512-8Q25gQ/XbA7HJAe+eXp4UhcXM9aOOJFaxZ02+XSNwMvY8gtWSCBLVqRcW4OhqilgZ8PeuQDWgBxeo+BIqqFWFQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/rectangle-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rectangle-grid/-/rectangle-grid-6.5.0.tgz"
    integrity sha512-yQZ/1vbW68O2KsSB3OZYK+72aWz/Adnf7m2CMKcC+aq6TwjxZjAvlbCOsNUnMAuldRUVN1ph6RXMG4e9KEvKvg==
    dependencies:
      "@turf/boolean-intersects" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/rewind@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rewind/-/rewind-6.5.0.tgz"
    integrity sha512-IoUAMcHWotBWYwSYuYypw/LlqZmO+wcBpn8ysrBNbazkFNkLf3btSDZMkKJO/bvOzl55imr/Xj4fi3DdsLsbzQ==
    dependencies:
      "@turf/boolean-clockwise" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/rhumb-bearing@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rhumb-bearing/-/rhumb-bearing-6.5.0.tgz"
    integrity sha512-jMyqiMRK4hzREjQmnLXmkJ+VTNTx1ii8vuqRwJPcTlKbNWfjDz/5JqJlb5NaFDcdMpftWovkW5GevfnuzHnOYA==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/rhumb-destination@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rhumb-destination/-/rhumb-destination-6.5.0.tgz"
    integrity sha512-RHNP1Oy+7xTTdRrTt375jOZeHceFbjwohPHlr9Hf68VdHHPMAWgAKqiX2YgSWDcvECVmiGaBKWus1Df+N7eE4Q==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/rhumb-distance@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/rhumb-distance/-/rhumb-distance-6.5.0.tgz"
    integrity sha512-oKp8KFE8E4huC2Z1a1KNcFwjVOqa99isxNOwfo4g3SUABQ6NezjKDDrnvC4yI5YZ3/huDjULLBvhed45xdCrzg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
  
  "@turf/sample@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/sample/-/sample-6.5.0.tgz"
    integrity sha512-kSdCwY7el15xQjnXYW520heKUrHwRvnzx8ka4eYxX9NFeOxaFITLW2G7UtXb6LJK8mmPXI8Aexv23F2ERqzGFg==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/sector@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/sector/-/sector-6.5.0.tgz"
    integrity sha512-cYUOkgCTWqa23SOJBqxoFAc/yGCUsPRdn/ovbRTn1zNTm/Spmk6hVB84LCKOgHqvSF25i0d2kWqpZDzLDdAPbw==
    dependencies:
      "@turf/circle" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/line-arc" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/shortest-path@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/shortest-path/-/shortest-path-6.5.0.tgz"
    integrity sha512-4de5+G7+P4hgSoPwn+SO9QSi9HY5NEV/xRJ+cmoFVRwv2CDsuOPDheHKeuIAhKyeKDvPvPt04XYWbac4insJMg==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/bbox-polygon" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/clean-coords" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/transform-scale" "^6.5.0"
  
  "@turf/simplify@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/simplify/-/simplify-6.5.0.tgz"
    integrity sha512-USas3QqffPHUY184dwQdP8qsvcVH/PWBYdXY5am7YTBACaQOMAlf6AKJs9FT8jiO6fQpxfgxuEtwmox+pBtlOg==
    dependencies:
      "@turf/clean-coords" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/square-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/square-grid/-/square-grid-6.5.0.tgz"
    integrity sha512-mlR0ayUdA+L4c9h7p4k3pX6gPWHNGuZkt2c5II1TJRmhLkW2557d6b/Vjfd1z9OVaajb1HinIs1FMSAPXuuUrA==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/rectangle-grid" "^6.5.0"
  
  "@turf/square@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/square/-/square-6.5.0.tgz"
    integrity sha512-BM2UyWDmiuHCadVhHXKIx5CQQbNCpOxB6S/aCNOCLbhCeypKX5Q0Aosc5YcmCJgkwO5BERCC6Ee7NMbNB2vHmQ==
    dependencies:
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
  
  "@turf/standard-deviational-ellipse@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/standard-deviational-ellipse/-/standard-deviational-ellipse-6.5.0.tgz"
    integrity sha512-02CAlz8POvGPFK2BKK8uHGUk/LXb0MK459JVjKxLC2yJYieOBTqEbjP0qaWhiBhGzIxSMaqe8WxZ0KvqdnstHA==
    dependencies:
      "@turf/center-mean" "^6.5.0"
      "@turf/ellipse" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/points-within-polygon" "^6.5.0"
  
  "@turf/tag@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/tag/-/tag-6.5.0.tgz"
    integrity sha512-XwlBvrOV38CQsrNfrxvBaAPBQgXMljeU0DV8ExOyGM7/hvuGHJw3y8kKnQ4lmEQcmcrycjDQhP7JqoRv8vFssg==
    dependencies:
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/tesselate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/tesselate/-/tesselate-6.5.0.tgz"
    integrity sha512-M1HXuyZFCfEIIKkglh/r5L9H3c5QTEsnMBoZOFQiRnGPGmJWcaBissGb7mTFX2+DKE7FNWXh4TDnZlaLABB0dQ==
    dependencies:
      "@turf/helpers" "^6.5.0"
      earcut "^2.0.0"
  
  "@turf/tin@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/tin/-/tin-6.5.0.tgz"
    integrity sha512-YLYikRzKisfwj7+F+Tmyy/LE3d2H7D4kajajIfc9mlik2+esG7IolsX/+oUz1biguDYsG0DUA8kVYXDkobukfg==
    dependencies:
      "@turf/helpers" "^6.5.0"
  
  "@turf/transform-rotate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/transform-rotate/-/transform-rotate-6.5.0.tgz"
    integrity sha512-A2Ip1v4246ZmpssxpcL0hhiVBEf4L8lGnSPWTgSv5bWBEoya2fa/0SnFX9xJgP40rMP+ZzRaCN37vLHbv1Guag==
    dependencies:
      "@turf/centroid" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
      "@turf/rhumb-distance" "^6.5.0"
  
  "@turf/transform-scale@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/transform-scale/-/transform-scale-6.5.0.tgz"
    integrity sha512-VsATGXC9rYM8qTjbQJ/P7BswKWXHdnSJ35JlV4OsZyHBMxJQHftvmZJsFbOqVtQnIQIzf2OAly6rfzVV9QLr7g==
    dependencies:
      "@turf/bbox" "^6.5.0"
      "@turf/center" "^6.5.0"
      "@turf/centroid" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
      "@turf/rhumb-distance" "^6.5.0"
  
  "@turf/transform-translate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/transform-translate/-/transform-translate-6.5.0.tgz"
    integrity sha512-NABLw5VdtJt/9vSstChp93pc6oel4qXEos56RBMsPlYB8hzNTEKYtC146XJvyF4twJeeYS8RVe1u7KhoFwEM5w==
    dependencies:
      "@turf/clone" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
  
  "@turf/triangle-grid@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/triangle-grid/-/triangle-grid-6.5.0.tgz"
    integrity sha512-2jToUSAS1R1htq4TyLQYPTIsoy6wg3e3BQXjm2rANzw4wPQCXGOxrur1Fy9RtzwqwljlC7DF4tg0OnWr8RjmfA==
    dependencies:
      "@turf/distance" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/intersect" "^6.5.0"
  
  "@turf/truncate@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/truncate/-/truncate-6.5.0.tgz"
    integrity sha512-pFxg71pLk+eJj134Z9yUoRhIi8vqnnKvCYwdT4x/DQl/19RVdq1tV3yqOT3gcTQNfniteylL5qV1uTBDV5sgrg==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
  
  "@turf/turf@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/turf/-/turf-6.5.0.tgz"
    integrity sha512-ipMCPnhu59bh92MNt8+pr1VZQhHVuTMHklciQURo54heoxRzt1neNYZOBR6jdL+hNsbDGAECMuIpAutX+a3Y+w==
    dependencies:
      "@turf/along" "^6.5.0"
      "@turf/angle" "^6.5.0"
      "@turf/area" "^6.5.0"
      "@turf/bbox" "^6.5.0"
      "@turf/bbox-clip" "^6.5.0"
      "@turf/bbox-polygon" "^6.5.0"
      "@turf/bearing" "^6.5.0"
      "@turf/bezier-spline" "^6.5.0"
      "@turf/boolean-clockwise" "^6.5.0"
      "@turf/boolean-contains" "^6.5.0"
      "@turf/boolean-crosses" "^6.5.0"
      "@turf/boolean-disjoint" "^6.5.0"
      "@turf/boolean-equal" "^6.5.0"
      "@turf/boolean-intersects" "^6.5.0"
      "@turf/boolean-overlap" "^6.5.0"
      "@turf/boolean-parallel" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/boolean-point-on-line" "^6.5.0"
      "@turf/boolean-within" "^6.5.0"
      "@turf/buffer" "^6.5.0"
      "@turf/center" "^6.5.0"
      "@turf/center-mean" "^6.5.0"
      "@turf/center-median" "^6.5.0"
      "@turf/center-of-mass" "^6.5.0"
      "@turf/centroid" "^6.5.0"
      "@turf/circle" "^6.5.0"
      "@turf/clean-coords" "^6.5.0"
      "@turf/clone" "^6.5.0"
      "@turf/clusters" "^6.5.0"
      "@turf/clusters-dbscan" "^6.5.0"
      "@turf/clusters-kmeans" "^6.5.0"
      "@turf/collect" "^6.5.0"
      "@turf/combine" "^6.5.0"
      "@turf/concave" "^6.5.0"
      "@turf/convex" "^6.5.0"
      "@turf/destination" "^6.5.0"
      "@turf/difference" "^6.5.0"
      "@turf/dissolve" "^6.5.0"
      "@turf/distance" "^6.5.0"
      "@turf/distance-weight" "^6.5.0"
      "@turf/ellipse" "^6.5.0"
      "@turf/envelope" "^6.5.0"
      "@turf/explode" "^6.5.0"
      "@turf/flatten" "^6.5.0"
      "@turf/flip" "^6.5.0"
      "@turf/great-circle" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/hex-grid" "^6.5.0"
      "@turf/interpolate" "^6.5.0"
      "@turf/intersect" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      "@turf/isobands" "^6.5.0"
      "@turf/isolines" "^6.5.0"
      "@turf/kinks" "^6.5.0"
      "@turf/length" "^6.5.0"
      "@turf/line-arc" "^6.5.0"
      "@turf/line-chunk" "^6.5.0"
      "@turf/line-intersect" "^6.5.0"
      "@turf/line-offset" "^6.5.0"
      "@turf/line-overlap" "^6.5.0"
      "@turf/line-segment" "^6.5.0"
      "@turf/line-slice" "^6.5.0"
      "@turf/line-slice-along" "^6.5.0"
      "@turf/line-split" "^6.5.0"
      "@turf/line-to-polygon" "^6.5.0"
      "@turf/mask" "^6.5.0"
      "@turf/meta" "^6.5.0"
      "@turf/midpoint" "^6.5.0"
      "@turf/moran-index" "^6.5.0"
      "@turf/nearest-point" "^6.5.0"
      "@turf/nearest-point-on-line" "^6.5.0"
      "@turf/nearest-point-to-line" "^6.5.0"
      "@turf/planepoint" "^6.5.0"
      "@turf/point-grid" "^6.5.0"
      "@turf/point-on-feature" "^6.5.0"
      "@turf/point-to-line-distance" "^6.5.0"
      "@turf/points-within-polygon" "^6.5.0"
      "@turf/polygon-smooth" "^6.5.0"
      "@turf/polygon-tangents" "^6.5.0"
      "@turf/polygon-to-line" "^6.5.0"
      "@turf/polygonize" "^6.5.0"
      "@turf/projection" "^6.5.0"
      "@turf/random" "^6.5.0"
      "@turf/rewind" "^6.5.0"
      "@turf/rhumb-bearing" "^6.5.0"
      "@turf/rhumb-destination" "^6.5.0"
      "@turf/rhumb-distance" "^6.5.0"
      "@turf/sample" "^6.5.0"
      "@turf/sector" "^6.5.0"
      "@turf/shortest-path" "^6.5.0"
      "@turf/simplify" "^6.5.0"
      "@turf/square" "^6.5.0"
      "@turf/square-grid" "^6.5.0"
      "@turf/standard-deviational-ellipse" "^6.5.0"
      "@turf/tag" "^6.5.0"
      "@turf/tesselate" "^6.5.0"
      "@turf/tin" "^6.5.0"
      "@turf/transform-rotate" "^6.5.0"
      "@turf/transform-scale" "^6.5.0"
      "@turf/transform-translate" "^6.5.0"
      "@turf/triangle-grid" "^6.5.0"
      "@turf/truncate" "^6.5.0"
      "@turf/union" "^6.5.0"
      "@turf/unkink-polygon" "^6.5.0"
      "@turf/voronoi" "^6.5.0"
  
  "@turf/union@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/union/-/union-6.5.0.tgz"
    integrity sha512-igYWCwP/f0RFHIlC2c0SKDuM/ObBaqSljI3IdV/x71805QbIvY/BYGcJdyNcgEA6cylIGl/0VSlIbpJHZ9ldhw==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      polygon-clipping "^0.15.3"
  
  "@turf/unkink-polygon@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/unkink-polygon/-/unkink-polygon-6.5.0.tgz"
    integrity sha512-8QswkzC0UqKmN1DT6HpA9upfa1HdAA5n6bbuzHy8NJOX8oVizVAqfEPY0wqqTgboDjmBR4yyImsdPGUl3gZ8JQ==
    dependencies:
      "@turf/area" "^6.5.0"
      "@turf/boolean-point-in-polygon" "^6.5.0"
      "@turf/helpers" "^6.5.0"
      "@turf/meta" "^6.5.0"
      rbush "^2.0.1"
  
  "@turf/voronoi@^6.5.0":
    version "6.5.0"
    resolved "https://registry.npmmirror.com/@turf/voronoi/-/voronoi-6.5.0.tgz"
    integrity sha512-C/xUsywYX+7h1UyNqnydHXiun4UPjK88VDghtoRypR9cLlb7qozkiLRphQxxsCM0KxyxpVPHBVQXdAL3+Yurow==
    dependencies:
      "@turf/helpers" "^6.5.0"
      "@turf/invariant" "^6.5.0"
      d3-voronoi "1.1.2"
  
  "@types/geojson@7946.0.8":
    version "7946.0.8"
    resolved "https://registry.npmmirror.com/@types/geojson/-/geojson-7946.0.8.tgz"
    integrity sha512-1rkryxURpr6aWP7R786/UQOkJ3PcpQiWkAXBmdWc7ryFWqN6a4xfK7BtjXvFBKO9LjQ+MWQSWxYeZX1OApnArA==
  
  "@types/geojson@^7946.0.10":
    version "7946.0.14"
    resolved "https://registry.npmmirror.com/@types/geojson/-/geojson-7946.0.14.tgz"
    integrity sha512-WCfD5Ht3ZesJUsONdhvm84dmzWOiOzOAqOncN0++w0lBw1o8OuDNJF2McvvCef/yBqb/HYRahp1BYtODFQ8bRg==
  
  "@types/json-schema@^7.0.12":
    version "7.0.15"
    resolved "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz"
    integrity sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==
  
  "@types/json5@^0.0.29":
    version "0.0.29"
    resolved "https://registry.npmmirror.com/@types/json5/-/json5-0.0.29.tgz"
    integrity sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==
  
  "@types/semver@^7.5.0":
    version "7.5.8"
    resolved "https://registry.npmmirror.com/@types/semver/-/semver-7.5.8.tgz"
    integrity sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==
  
  "@typescript-eslint/eslint-plugin@^6.14.0", "@typescript-eslint/eslint-plugin@^6.7.0":
    version "6.21.0"
    resolved "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-6.21.0.tgz"
    integrity sha512-oy9+hTPCUFpngkEZUSzbf9MxI65wbKFoQYsgPdILTfbUldp5ovUuphZVe4i30emU9M/kP+T64Di0mxl7dSw3MA==
    dependencies:
      "@eslint-community/regexpp" "^4.5.1"
      "@typescript-eslint/scope-manager" "6.21.0"
      "@typescript-eslint/type-utils" "6.21.0"
      "@typescript-eslint/utils" "6.21.0"
      "@typescript-eslint/visitor-keys" "6.21.0"
      debug "^4.3.4"
      graphemer "^1.4.0"
      ignore "^5.2.4"
      natural-compare "^1.4.0"
      semver "^7.5.4"
      ts-api-utils "^1.0.1"
  
  "@typescript-eslint/parser@^6.14.0", "@typescript-eslint/parser@^6.7.0":
    version "6.21.0"
    resolved "https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-6.21.0.tgz"
    integrity sha512-tbsV1jPne5CkFQCgPBcDOt30ItF7aJoZL997JSF7MhGQqOeT3svWRYxiqlfA5RUdlHN6Fi+EI9bxqbdyAUZjYQ==
    dependencies:
      "@typescript-eslint/scope-manager" "6.21.0"
      "@typescript-eslint/types" "6.21.0"
      "@typescript-eslint/typescript-estree" "6.21.0"
      "@typescript-eslint/visitor-keys" "6.21.0"
      debug "^4.3.4"
  
  "@typescript-eslint/scope-manager@6.21.0":
    version "6.21.0"
    resolved "https://registry.npmmirror.com/@typescript-eslint/scope-manager/-/scope-manager-6.21.0.tgz"
    integrity sha512-OwLUIWZJry80O99zvqXVEioyniJMa+d2GrqpUTqi5/v5D5rOrppJVBPa0yKCblcigC0/aYAzxxqQ1B+DS2RYsg==
    dependencies:
      "@typescript-eslint/types" "6.21.0"
      "@typescript-eslint/visitor-keys" "6.21.0"
  
  "@typescript-eslint/type-utils@6.21.0":
    version "6.21.0"
    resolved "https://registry.npmmirror.com/@typescript-eslint/type-utils/-/type-utils-6.21.0.tgz"
    integrity sha512-rZQI7wHfao8qMX3Rd3xqeYSMCL3SoiSQLBATSiVKARdFGCYSRvmViieZjqc58jKgs8Y8i9YvVVhRbHSTA4VBag==
    dependencies:
      "@typescript-eslint/typescript-estree" "6.21.0"
      "@typescript-eslint/utils" "6.21.0"
      debug "^4.3.4"
      ts-api-utils "^1.0.1"
  
  "@typescript-eslint/types@6.21.0":
    version "6.21.0"
    resolved "https://registry.npmmirror.com/@typescript-eslint/types/-/types-6.21.0.tgz"
    integrity sha512-1kFmZ1rOm5epu9NZEZm1kckCDGj5UJEf7P1kliH4LKu/RkwpsfqqGmY2OOcUs18lSlQBKLDYBOGxRVtrMN5lpg==
  
  "@typescript-eslint/typescript-estree@6.21.0":
    version "6.21.0"
    resolved "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-6.21.0.tgz"
    integrity sha512-6npJTkZcO+y2/kr+z0hc4HwNfrrP4kNYh57ek7yCNlrBjWQ1Y0OS7jiZTkgumrvkX5HkEKXFZkkdFNkaW2wmUQ==
    dependencies:
      "@typescript-eslint/types" "6.21.0"
      "@typescript-eslint/visitor-keys" "6.21.0"
      debug "^4.3.4"
      globby "^11.1.0"
      is-glob "^4.0.3"
      minimatch "9.0.3"
      semver "^7.5.4"
      ts-api-utils "^1.0.1"
  
  "@typescript-eslint/utils@6.21.0":
    version "6.21.0"
    resolved "https://registry.npmmirror.com/@typescript-eslint/utils/-/utils-6.21.0.tgz"
    integrity sha512-NfWVaC8HP9T8cbKQxHcsJBY5YE1O33+jpMwN45qzWWaPDZgLIbo12toGMWnmhvCpd3sIxkpDw3Wv1B3dYrbDQQ==
    dependencies:
      "@eslint-community/eslint-utils" "^4.4.0"
      "@types/json-schema" "^7.0.12"
      "@types/semver" "^7.5.0"
      "@typescript-eslint/scope-manager" "6.21.0"
      "@typescript-eslint/types" "6.21.0"
      "@typescript-eslint/typescript-estree" "6.21.0"
      semver "^7.5.4"
  
  "@typescript-eslint/visitor-keys@6.21.0":
    version "6.21.0"
    resolved "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/-/visitor-keys-6.21.0.tgz"
    integrity sha512-JJtkDduxLi9bivAB+cYOVMtbkqdPOhZ+ZI5LC47MIRrDV4Yn2o+ZnW10Nkmr28xRpSpdJ6Sm42Hjf2+REYXm0A==
    dependencies:
      "@typescript-eslint/types" "6.21.0"
      eslint-visitor-keys "^3.4.1"
  
  "@ungap/structured-clone@^1.2.0":
    version "1.2.0"
    resolved "https://registry.npmmirror.com/@ungap/structured-clone/-/structured-clone-1.2.0.tgz"
    integrity sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==
  
  acorn-jsx@^5.3.2:
    version "5.3.2"
    resolved "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
    integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==
  
  acorn@^8.9.0:
    version "8.12.1"
    resolved "https://registry.npmmirror.com/acorn/-/acorn-8.12.1.tgz"
    integrity sha512-tcpGyI9zbizT9JbV6oYE477V6mTlXvvi0T0G3SNIYE2apm/G5huBa1+K89VGeovbg+jycCrfhl3ADxErOuO6Jg==
  
  ajv@^6.12.4:
    version "6.12.6"
    resolved "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz"
    integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
    dependencies:
      fast-deep-equal "^3.1.1"
      fast-json-stable-stringify "^2.0.0"
      json-schema-traverse "^0.4.1"
      uri-js "^4.2.2"
  
  ansi-regex@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz"
    integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==
  
  ansi-styles@^3.2.1:
    version "3.2.1"
    resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz"
    integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
    dependencies:
      color-convert "^1.9.0"
  
  ansi-styles@^4.1.0:
    version "4.3.0"
    resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
    integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
    dependencies:
      color-convert "^2.0.1"
  
  are-docs-informative@^0.0.2:
    version "0.0.2"
    resolved "https://registry.npmmirror.com/are-docs-informative/-/are-docs-informative-0.0.2.tgz"
    integrity sha512-ixiS0nLNNG5jNQzgZJNoUpBKdo9yTYZMGJ+QgT2jmjR7G7+QHRCc4v6LQ3NgE7EBJq+o0ams3waJwkrlBom8Ig==
  
  argparse@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz"
    integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==
  
  aria-query@~5.1.3:
    version "5.1.3"
    resolved "https://registry.npmmirror.com/aria-query/-/aria-query-5.1.3.tgz"
    integrity sha512-R5iJ5lkuHybztUfuOAznmboyjWq8O6sqNqtK7CLOqdydi54VNbORp49mb14KbWgG1QD3JFO9hJdZ+y4KutfdOQ==
    dependencies:
      deep-equal "^2.0.5"
  
  array-buffer-byte-length@^1.0.0, array-buffer-byte-length@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/array-buffer-byte-length/-/array-buffer-byte-length-1.0.1.tgz"
    integrity sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==
    dependencies:
      call-bind "^1.0.5"
      is-array-buffer "^3.0.4"
  
  array-includes@^3.1.6, array-includes@^3.1.7, array-includes@^3.1.8:
    version "3.1.8"
    resolved "https://registry.npmmirror.com/array-includes/-/array-includes-3.1.8.tgz"
    integrity sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-object-atoms "^1.0.0"
      get-intrinsic "^1.2.4"
      is-string "^1.0.7"
  
  array-union@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz"
    integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==
  
  array.prototype.findlast@^1.2.5:
    version "1.2.5"
    resolved "https://registry.npmmirror.com/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz"
    integrity sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-errors "^1.3.0"
      es-object-atoms "^1.0.0"
      es-shim-unscopables "^1.0.2"
  
  array.prototype.findlastindex@^1.2.3:
    version "1.2.5"
    resolved "https://registry.npmmirror.com/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.5.tgz"
    integrity sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-errors "^1.3.0"
      es-object-atoms "^1.0.0"
      es-shim-unscopables "^1.0.2"
  
  array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.2:
    version "1.3.2"
    resolved "https://registry.npmmirror.com/array.prototype.flat/-/array.prototype.flat-1.3.2.tgz"
    integrity sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.2.0"
      es-abstract "^1.22.1"
      es-shim-unscopables "^1.0.0"
  
  array.prototype.flatmap@^1.3.2:
    version "1.3.2"
    resolved "https://registry.npmmirror.com/array.prototype.flatmap/-/array.prototype.flatmap-1.3.2.tgz"
    integrity sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.2.0"
      es-abstract "^1.22.1"
      es-shim-unscopables "^1.0.0"
  
  array.prototype.tosorted@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz"
    integrity sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.3"
      es-errors "^1.3.0"
      es-shim-unscopables "^1.0.2"
  
  arraybuffer.prototype.slice@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.3.tgz"
    integrity sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A==
    dependencies:
      array-buffer-byte-length "^1.0.1"
      call-bind "^1.0.5"
      define-properties "^1.2.1"
      es-abstract "^1.22.3"
      es-errors "^1.2.1"
      get-intrinsic "^1.2.3"
      is-array-buffer "^3.0.4"
      is-shared-array-buffer "^1.0.2"
  
  ast-types-flow@^0.0.8:
    version "0.0.8"
    resolved "https://registry.npmmirror.com/ast-types-flow/-/ast-types-flow-0.0.8.tgz"
    integrity sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==
  
  available-typed-arrays@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmmirror.com/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
    integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
    dependencies:
      possible-typed-array-names "^1.0.0"
  
  axe-core@^4.9.1:
    version "4.10.0"
    resolved "https://registry.npmmirror.com/axe-core/-/axe-core-4.10.0.tgz"
    integrity sha512-Mr2ZakwQ7XUAjp7pAwQWRhhK8mQQ6JAaNWSjmjxil0R8BPioMtQsTLOolGYkji1rcL++3dCqZA3zWqpT+9Ew6g==
  
  axios-miniprogram-adapter@^0.3.2:
    version "0.3.5"
    resolved "https://registry.npmmirror.com/axios-miniprogram-adapter/-/axios-miniprogram-adapter-0.3.5.tgz"
    integrity sha512-ZAudH+aTi0QPIy8IQm26F0nxKoTFa/mNhDfxGkG5ndsQe4RVnyyb6f50tIT13nHtNepAjZo9i+UGmTbke5+lbQ==
    dependencies:
      axios "^0.19.2"
  
  axios@^0.19.2:
    version "0.19.2"
    resolved "https://registry.npmmirror.com/axios/-/axios-0.19.2.tgz"
    integrity sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==
    dependencies:
      follow-redirects "1.5.10"
  
  axios@^0.26.1:
    version "0.26.1"
    resolved "https://registry.npmmirror.com/axios/-/axios-0.26.1.tgz"
    integrity sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==
    dependencies:
      follow-redirects "^1.14.8"
  
  axobject-query@~3.1.1:
    version "3.1.1"
    resolved "https://registry.npmmirror.com/axobject-query/-/axobject-query-3.1.1.tgz"
    integrity sha512-goKlv8DZrK9hUh975fnHzhNIO4jUnFCfv/dszV5VwUGDFjI6vQ2VwoyjYjYNEbBE8AH87TduWP5uyDR1D+Iteg==
    dependencies:
      deep-equal "^2.0.5"
  
  balanced-match@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz"
    integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==
  
  boolbase@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz"
    integrity sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==
  
  brace-expansion@^1.1.7:
    version "1.1.11"
    resolved "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz"
    integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
    dependencies:
      balanced-match "^1.0.0"
      concat-map "0.0.1"
  
  brace-expansion@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz"
    integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
    dependencies:
      balanced-match "^1.0.0"
  
  braces@^3.0.3:
    version "3.0.3"
    resolved "https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz"
    integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
    dependencies:
      fill-range "^7.1.1"
  
  browserslist@^4.23.1:
    version "4.23.3"
    resolved "https://registry.npmmirror.com/browserslist/-/browserslist-4.23.3.tgz"
    integrity sha512-btwCFJVjI4YWDNfau8RhZ+B1Q/VLoUITrm3RlP6y1tYGWIOa+InuYiRGXUBXo8nA1qKmHMyLB/iVQg5TT4eFoA==
    dependencies:
      caniuse-lite "^1.0.30001646"
      electron-to-chromium "^1.5.4"
      node-releases "^2.0.18"
      update-browserslist-db "^1.1.0"
  
  builtin-modules@^3.3.0:
    version "3.3.0"
    resolved "https://registry.npmmirror.com/builtin-modules/-/builtin-modules-3.3.0.tgz"
    integrity sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw==
  
  call-bind@^1.0.2, call-bind@^1.0.5, call-bind@^1.0.6, call-bind@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.7.tgz"
    integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
    dependencies:
      es-define-property "^1.0.0"
      es-errors "^1.3.0"
      function-bind "^1.1.2"
      get-intrinsic "^1.2.4"
      set-function-length "^1.2.1"
  
  callsites@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz"
    integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==
  
  caniuse-lite@^1.0.30001646:
    version "1.0.30001655"
    resolved "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001655.tgz"
    integrity sha512-jRGVy3iSGO5Uutn2owlb5gR6qsGngTw9ZTb4ali9f3glshcNmJ2noam4Mo9zia5P9Dk3jNNydy7vQjuE5dQmfg==
  
  chalk@^2.4.2:
    version "2.4.2"
    resolved "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
    integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
    dependencies:
      ansi-styles "^3.2.1"
      escape-string-regexp "^1.0.5"
      supports-color "^5.3.0"
  
  chalk@^4.0.0:
    version "4.1.2"
    resolved "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
    integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  color-convert@^1.9.0:
    version "1.9.3"
    resolved "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz"
    integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
    dependencies:
      color-name "1.1.3"
  
  color-convert@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
    integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
    dependencies:
      color-name "~1.1.4"
  
  color-name@1.1.3:
    version "1.1.3"
    resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz"
    integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==
  
  color-name@~1.1.4:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
    integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==
  
  commander@2:
    version "2.20.3"
    resolved "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz"
    integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==
  
  comment-parser@1.4.1:
    version "1.4.1"
    resolved "https://registry.npmmirror.com/comment-parser/-/comment-parser-1.4.1.tgz"
    integrity sha512-buhp5kePrmda3vhc5B9t7pUQXAb2Tnd0qgpkIhPhkHXxJpiPJ11H0ZEU0oBpJ2QztSbzG/ZxMj/CHsYJqRHmyg==
  
  concat-map@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz"
    integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==
  
  concaveman@*:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/concaveman/-/concaveman-1.2.1.tgz"
    integrity sha512-PwZYKaM/ckQSa8peP5JpVr7IMJ4Nn/MHIaWUjP4be+KoZ7Botgs8seAZGpmaOM+UZXawcdYRao/px9ycrCihHw==
    dependencies:
      point-in-polygon "^1.1.0"
      rbush "^3.0.1"
      robust-predicates "^2.0.4"
      tinyqueue "^2.0.3"
  
  confusing-browser-globals@^1.0.10:
    version "1.0.11"
    resolved "https://registry.npmmirror.com/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz"
    integrity sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA==
  
  convert-source-map@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz"
    integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==
  
  cross-spawn@^7.0.2:
    version "7.0.3"
    resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz"
    integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
    dependencies:
      path-key "^3.1.0"
      shebang-command "^2.0.0"
      which "^2.0.1"
  
  crypto-js@^4.1.1:
    version "4.2.0"
    resolved "https://registry.npmmirror.com/crypto-js/-/crypto-js-4.2.0.tgz"
    integrity sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==
  
  csscolorparser@~1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/csscolorparser/-/csscolorparser-1.0.3.tgz"
    integrity sha512-umPSgYwZkdFoUrH5hIq5kf0wPSXiro51nPw0j2K/c83KflkPSTBGMz6NJvMB+07VlL0y7VPo6QJcDjcgKTTm3w==
  
  cssesc@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz"
    integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==
  
  d3-array@1:
    version "1.2.4"
    resolved "https://registry.npmmirror.com/d3-array/-/d3-array-1.2.4.tgz"
    integrity sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw==
  
  d3-geo@1.7.1:
    version "1.7.1"
    resolved "https://registry.npmmirror.com/d3-geo/-/d3-geo-1.7.1.tgz"
    integrity sha512-O4AempWAr+P5qbk2bC2FuN/sDW4z+dN2wDf9QV3bxQt4M5HfOEeXLgJ/UKQW0+o1Dj8BE+L5kiDbdWUMjsmQpw==
    dependencies:
      d3-array "1"
  
  d3-voronoi@1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/d3-voronoi/-/d3-voronoi-1.1.2.tgz"
    integrity sha512-RhGS1u2vavcO7ay7ZNAPo4xeDh/VYeGof3x5ZLJBQgYhLegxr3s5IykvWmJ94FTU6mcbtp4sloqZ54mP6R4Utw==
  
  damerau-levenshtein@^1.0.8:
    version "1.0.8"
    resolved "https://registry.npmmirror.com/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"
    integrity sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==
  
  data-view-buffer@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/data-view-buffer/-/data-view-buffer-1.0.1.tgz"
    integrity sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==
    dependencies:
      call-bind "^1.0.6"
      es-errors "^1.3.0"
      is-data-view "^1.0.1"
  
  data-view-byte-length@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/data-view-byte-length/-/data-view-byte-length-1.0.1.tgz"
    integrity sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==
    dependencies:
      call-bind "^1.0.7"
      es-errors "^1.3.0"
      is-data-view "^1.0.1"
  
  data-view-byte-offset@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/data-view-byte-offset/-/data-view-byte-offset-1.0.0.tgz"
    integrity sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==
    dependencies:
      call-bind "^1.0.6"
      es-errors "^1.3.0"
      is-data-view "^1.0.1"
  
  dayjs@^1.11.0:
    version "1.11.13"
    resolved "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.13.tgz"
    integrity sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==
  
  debug@=3.1.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/debug/-/debug-3.1.0.tgz"
    integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
    dependencies:
      ms "2.0.0"
  
  debug@^3.2.7:
    version "3.2.7"
    resolved "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz"
    integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
    dependencies:
      ms "^2.1.1"
  
  debug@^4.1.0, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@^4.3.5:
    version "4.3.6"
    resolved "https://registry.npmmirror.com/debug/-/debug-4.3.6.tgz"
    integrity sha512-O/09Bd4Z1fBrU4VzkhFqVgpPzaGbw6Sm9FEkBT1A/YBXQFGuuSxa1dN2nxgxS34JmKXqYx8CZAwEVoJFImUXIg==
    dependencies:
      ms "2.1.2"
  
  deep-equal@1.x, deep-equal@^1.0.0:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/deep-equal/-/deep-equal-1.1.2.tgz"
    integrity sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==
    dependencies:
      is-arguments "^1.1.1"
      is-date-object "^1.0.5"
      is-regex "^1.1.4"
      object-is "^1.1.5"
      object-keys "^1.1.1"
      regexp.prototype.flags "^1.5.1"
  
  deep-equal@^2.0.5:
    version "2.2.3"
    resolved "https://registry.npmmirror.com/deep-equal/-/deep-equal-2.2.3.tgz"
    integrity sha512-ZIwpnevOurS8bpT4192sqAowWM76JDKSHYzMLty3BZGSswgq6pBaH3DhCSW5xVAZICZyKdOBPjwww5wfgT/6PA==
    dependencies:
      array-buffer-byte-length "^1.0.0"
      call-bind "^1.0.5"
      es-get-iterator "^1.1.3"
      get-intrinsic "^1.2.2"
      is-arguments "^1.1.1"
      is-array-buffer "^3.0.2"
      is-date-object "^1.0.5"
      is-regex "^1.1.4"
      is-shared-array-buffer "^1.0.2"
      isarray "^2.0.5"
      object-is "^1.1.5"
      object-keys "^1.1.1"
      object.assign "^4.1.4"
      regexp.prototype.flags "^1.5.1"
      side-channel "^1.0.4"
      which-boxed-primitive "^1.0.2"
      which-collection "^1.0.1"
      which-typed-array "^1.1.13"
  
  deep-is@^0.1.3:
    version "0.1.4"
    resolved "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz"
    integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==
  
  define-data-property@^1.0.1, define-data-property@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/define-data-property/-/define-data-property-1.1.4.tgz"
    integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
    dependencies:
      es-define-property "^1.0.0"
      es-errors "^1.3.0"
      gopd "^1.0.1"
  
  define-properties@^1.1.3, define-properties@^1.2.0, define-properties@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/define-properties/-/define-properties-1.2.1.tgz"
    integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
    dependencies:
      define-data-property "^1.0.1"
      has-property-descriptors "^1.0.0"
      object-keys "^1.1.1"
  
  density-clustering@1.3.0:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/density-clustering/-/density-clustering-1.3.0.tgz"
    integrity sha512-icpmBubVTwLnsaor9qH/4tG5+7+f61VcqMN3V3pm9sxxSCt2Jcs0zWOgwZW9ARJYaKD3FumIgHiMOcIMRRAzFQ==
  
  dir-glob@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz"
    integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
    dependencies:
      path-type "^4.0.0"
  
  doctrine@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/doctrine/-/doctrine-2.1.0.tgz"
    integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
    dependencies:
      esutils "^2.0.2"
  
  doctrine@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz"
    integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
    dependencies:
      esutils "^2.0.2"
  
  dotenv@^16.0.1:
    version "16.4.5"
    resolved "https://registry.npmmirror.com/dotenv/-/dotenv-16.4.5.tgz"
    integrity sha512-ZmdL2rui+eB2YwhsWzjInR8LldtZHGDoQ1ugH85ppHKwpUHL7j7rN0Ti9NCnGiQbhaZ11FpR+7ao1dNsmduNUg==
  
  earcut@^2.0.0, earcut@^2.2.4:
    version "2.2.4"
    resolved "https://registry.npmmirror.com/earcut/-/earcut-2.2.4.tgz"
    integrity sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ==
  
  electron-to-chromium@^1.5.4:
    version "1.5.13"
    resolved "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.5.13.tgz"
    integrity sha512-lbBcvtIJ4J6sS4tb5TLp1b4LyfCdMkwStzXPyAgVgTRAsep4bvrAGaBOP7ZJtQMNJpSQ9SqG4brWOroNaQtm7Q==
  
  emoji-regex@^9.2.2:
    version "9.2.2"
    resolved "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz"
    integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==
  
  enhanced-resolve@^5.15.0:
    version "5.17.1"
    resolved "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-5.17.1.tgz"
    integrity sha512-LMHl3dXhTcfv8gM4kEzIUeTQ+7fpdA0l2tUf34BddXPkz2A5xJ5L/Pchd5BL6rdccM9QGvu0sWZzK1Z1t4wwyg==
    dependencies:
      graceful-fs "^4.2.4"
      tapable "^2.2.0"
  
  es-abstract@^1.17.5, es-abstract@^1.22.1, es-abstract@^1.22.3, es-abstract@^1.23.0, es-abstract@^1.23.1, es-abstract@^1.23.2, es-abstract@^1.23.3:
    version "1.23.3"
    resolved "https://registry.npmmirror.com/es-abstract/-/es-abstract-1.23.3.tgz"
    integrity sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A==
    dependencies:
      array-buffer-byte-length "^1.0.1"
      arraybuffer.prototype.slice "^1.0.3"
      available-typed-arrays "^1.0.7"
      call-bind "^1.0.7"
      data-view-buffer "^1.0.1"
      data-view-byte-length "^1.0.1"
      data-view-byte-offset "^1.0.0"
      es-define-property "^1.0.0"
      es-errors "^1.3.0"
      es-object-atoms "^1.0.0"
      es-set-tostringtag "^2.0.3"
      es-to-primitive "^1.2.1"
      function.prototype.name "^1.1.6"
      get-intrinsic "^1.2.4"
      get-symbol-description "^1.0.2"
      globalthis "^1.0.3"
      gopd "^1.0.1"
      has-property-descriptors "^1.0.2"
      has-proto "^1.0.3"
      has-symbols "^1.0.3"
      hasown "^2.0.2"
      internal-slot "^1.0.7"
      is-array-buffer "^3.0.4"
      is-callable "^1.2.7"
      is-data-view "^1.0.1"
      is-negative-zero "^2.0.3"
      is-regex "^1.1.4"
      is-shared-array-buffer "^1.0.3"
      is-string "^1.0.7"
      is-typed-array "^1.1.13"
      is-weakref "^1.0.2"
      object-inspect "^1.13.1"
      object-keys "^1.1.1"
      object.assign "^4.1.5"
      regexp.prototype.flags "^1.5.2"
      safe-array-concat "^1.1.2"
      safe-regex-test "^1.0.3"
      string.prototype.trim "^1.2.9"
      string.prototype.trimend "^1.0.8"
      string.prototype.trimstart "^1.0.8"
      typed-array-buffer "^1.0.2"
      typed-array-byte-length "^1.0.1"
      typed-array-byte-offset "^1.0.2"
      typed-array-length "^1.0.6"
      unbox-primitive "^1.0.2"
      which-typed-array "^1.1.15"
  
  es-define-property@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.0.tgz"
    integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
    dependencies:
      get-intrinsic "^1.2.4"
  
  es-errors@^1.2.1, es-errors@^1.3.0:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz"
    integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==
  
  es-get-iterator@^1.1.3:
    version "1.1.3"
    resolved "https://registry.npmmirror.com/es-get-iterator/-/es-get-iterator-1.1.3.tgz"
    integrity sha512-sPZmqHBe6JIiTfN5q2pEi//TwxmAFHwj/XEuYjTuse78i8KxaqMTTzxPoFKuzRpDpTJ+0NAbpfenkmH2rePtuw==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.1.3"
      has-symbols "^1.0.3"
      is-arguments "^1.1.1"
      is-map "^2.0.2"
      is-set "^2.0.2"
      is-string "^1.0.7"
      isarray "^2.0.5"
      stop-iteration-iterator "^1.0.0"
  
  es-iterator-helpers@^1.0.19:
    version "1.0.19"
    resolved "https://registry.npmmirror.com/es-iterator-helpers/-/es-iterator-helpers-1.0.19.tgz"
    integrity sha512-zoMwbCcH5hwUkKJkT8kDIBZSz9I6mVG//+lDCinLCGov4+r7NIy0ld8o03M0cJxl2spVf6ESYVS6/gpIfq1FFw==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.3"
      es-errors "^1.3.0"
      es-set-tostringtag "^2.0.3"
      function-bind "^1.1.2"
      get-intrinsic "^1.2.4"
      globalthis "^1.0.3"
      has-property-descriptors "^1.0.2"
      has-proto "^1.0.3"
      has-symbols "^1.0.3"
      internal-slot "^1.0.7"
      iterator.prototype "^1.1.2"
      safe-array-concat "^1.1.2"
  
  es-object-atoms@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.0.0.tgz"
    integrity sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==
    dependencies:
      es-errors "^1.3.0"
  
  es-set-tostringtag@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/es-set-tostringtag/-/es-set-tostringtag-2.0.3.tgz"
    integrity sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==
    dependencies:
      get-intrinsic "^1.2.4"
      has-tostringtag "^1.0.2"
      hasown "^2.0.1"
  
  es-shim-unscopables@^1.0.0, es-shim-unscopables@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/es-shim-unscopables/-/es-shim-unscopables-1.0.2.tgz"
    integrity sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==
    dependencies:
      hasown "^2.0.0"
  
  es-to-primitive@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
    integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
    dependencies:
      is-callable "^1.1.4"
      is-date-object "^1.0.1"
      is-symbol "^1.0.2"
  
  escalade@^3.1.2:
    version "3.2.0"
    resolved "https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz"
    integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==
  
  escape-string-regexp@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
    integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==
  
  escape-string-regexp@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
    integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==
  
  eslint-config-airbnb-base@^15.0.0:
    version "15.0.0"
    resolved "https://registry.npmmirror.com/eslint-config-airbnb-base/-/eslint-config-airbnb-base-15.0.0.tgz"
    integrity sha512-xaX3z4ZZIcFLvh2oUNvcX5oEofXda7giYmuplVxoOg5A7EXJMrUyqRgR+mhDhPK8LZ4PttFOBvCYDbX3sUoUig==
    dependencies:
      confusing-browser-globals "^1.0.10"
      object.assign "^4.1.2"
      object.entries "^1.1.5"
      semver "^6.3.0"
  
  eslint-config-ali@^14.0.2:
    version "14.2.1"
    resolved "https://registry.npmmirror.com/eslint-config-ali/-/eslint-config-ali-14.2.1.tgz"
    integrity sha512-lg5Yj7rBvR1zOwm4ljuEgTkf2V2V0aytXr92yWFjlBV1tIqWNU+VRejSe808ORVUY8e0XqMZ+L0uikiZ4RTRpA==
    dependencies:
      "@babel/core" "^7.23.6"
      "@babel/eslint-parser" "^7.23.3"
      "@babel/preset-react" "^7.23.3"
      "@typescript-eslint/eslint-plugin" "^6.14.0"
      "@typescript-eslint/parser" "^6.14.0"
      eslint-config-egg "^13.0.0"
      eslint-import-resolver-typescript "^3.6.1"
      eslint-plugin-import "^2.29.1"
      eslint-plugin-react "^7.33.2"
      eslint-plugin-react-hooks "^4.6.0"
  
  eslint-config-egg@^13.0.0:
    version "13.1.0"
    resolved "https://registry.npmmirror.com/eslint-config-egg/-/eslint-config-egg-13.1.0.tgz"
    integrity sha512-Jubl5uvuIbAFrC+lb80hzmcL0o5hwyf4u5puK7d8qEk88FKo/6Xw52VAy58B/glbIMzIPXaXfoYY3AVzAvxRvQ==
    dependencies:
      "@babel/core" "^7.16.0"
      "@babel/eslint-parser" "^7.16.3"
      "@typescript-eslint/eslint-plugin" "^6.7.0"
      "@typescript-eslint/parser" "^6.7.0"
      eslint-plugin-eggache "^2.0.0"
      eslint-plugin-import "^2.29.1"
      eslint-plugin-jsdoc "^46.9.1"
      eslint-plugin-jsx-a11y "^6.8.0"
      eslint-plugin-node "^11.1.0"
      eslint-plugin-react "^7.33.2"
  
  eslint-config-prettier@^8.7.0:
    version "8.10.0"
    resolved "https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.10.0.tgz"
    integrity sha512-SM8AMJdeQqRYT9O9zguiruQZaN7+z+E4eAP9oiLNGKMtomwaB1E9dcgUD6ZAn/eQAb52USbvezbiljfZUhbJcg==
  
  eslint-import-resolver-node@^0.3.9:
    version "0.3.9"
    resolved "https://registry.npmmirror.com/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz"
    integrity sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==
    dependencies:
      debug "^3.2.7"
      is-core-module "^2.13.0"
      resolve "^1.22.4"
  
  eslint-import-resolver-typescript@^3.6.1:
    version "3.6.3"
    resolved "https://registry.npmmirror.com/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.6.3.tgz"
    integrity sha512-ud9aw4szY9cCT1EWWdGv1L1XR6hh2PaRWif0j2QjQ0pgTY/69iw+W0Z4qZv5wHahOl8isEr+k/JnyAqNQkLkIA==
    dependencies:
      "@nolyfill/is-core-module" "1.0.39"
      debug "^4.3.5"
      enhanced-resolve "^5.15.0"
      eslint-module-utils "^2.8.1"
      fast-glob "^3.3.2"
      get-tsconfig "^4.7.5"
      is-bun-module "^1.0.2"
      is-glob "^4.0.3"
  
  eslint-module-utils@^2.8.0, eslint-module-utils@^2.8.1:
    version "2.8.2"
    resolved "https://registry.npmmirror.com/eslint-module-utils/-/eslint-module-utils-2.8.2.tgz"
    integrity sha512-3XnC5fDyc8M4J2E8pt8pmSVRX2M+5yWMCfI/kDZwauQeFgzQOuhcRBFKjTeJagqgk4sFKxe1mvNVnaWwImx/Tg==
    dependencies:
      debug "^3.2.7"
  
  eslint-plugin-eggache@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/eslint-plugin-eggache/-/eslint-plugin-eggache-2.0.0.tgz"
    integrity sha512-IHPrZ8LZgTRJTN5e6bXEgmv4vE0yDTAYFgHyvlf2KED8gRSiZ/kVPur+VHjpV4/8OVGJg2YaNEqsiBHxgMCKyA==
  
  eslint-plugin-es@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/eslint-plugin-es/-/eslint-plugin-es-3.0.1.tgz"
    integrity sha512-GUmAsJaN4Fc7Gbtl8uOBlayo2DqhwWvEzykMHSCZHU3XdJ+NSzzZcVhXh3VxX5icqQ+oQdIEawXX8xkR3mIFmQ==
    dependencies:
      eslint-utils "^2.0.0"
      regexpp "^3.0.0"
  
  eslint-plugin-import@^2.27.5, eslint-plugin-import@^2.29.1:
    version "2.29.1"
    resolved "https://registry.npmmirror.com/eslint-plugin-import/-/eslint-plugin-import-2.29.1.tgz"
    integrity sha512-BbPC0cuExzhiMo4Ff1BTVwHpjjv28C5R+btTOGaCRC7UEz801up0JadwkeSk5Ued6TG34uaczuVuH6qyy5YUxw==
    dependencies:
      array-includes "^3.1.7"
      array.prototype.findlastindex "^1.2.3"
      array.prototype.flat "^1.3.2"
      array.prototype.flatmap "^1.3.2"
      debug "^3.2.7"
      doctrine "^2.1.0"
      eslint-import-resolver-node "^0.3.9"
      eslint-module-utils "^2.8.0"
      hasown "^2.0.0"
      is-core-module "^2.13.1"
      is-glob "^4.0.3"
      minimatch "^3.1.2"
      object.fromentries "^2.0.7"
      object.groupby "^1.0.1"
      object.values "^1.1.7"
      semver "^6.3.1"
      tsconfig-paths "^3.15.0"
  
  eslint-plugin-jsdoc@^46.9.1:
    version "46.10.1"
    resolved "https://registry.npmmirror.com/eslint-plugin-jsdoc/-/eslint-plugin-jsdoc-46.10.1.tgz"
    integrity sha512-x8wxIpv00Y50NyweDUpa+58ffgSAI5sqe+zcZh33xphD0AVh+1kqr1ombaTRb7Fhpove1zfUuujlX9DWWBP5ag==
    dependencies:
      "@es-joy/jsdoccomment" "~0.41.0"
      are-docs-informative "^0.0.2"
      comment-parser "1.4.1"
      debug "^4.3.4"
      escape-string-regexp "^4.0.0"
      esquery "^1.5.0"
      is-builtin-module "^3.2.1"
      semver "^7.5.4"
      spdx-expression-parse "^4.0.0"
  
  eslint-plugin-jsx-a11y@^6.8.0:
    version "6.9.0"
    resolved "https://registry.npmmirror.com/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.9.0.tgz"
    integrity sha512-nOFOCaJG2pYqORjK19lqPqxMO/JpvdCZdPtNdxY3kvom3jTvkAbOvQvD8wuD0G8BYR0IGAGYDlzqWJOh/ybn2g==
    dependencies:
      aria-query "~5.1.3"
      array-includes "^3.1.8"
      array.prototype.flatmap "^1.3.2"
      ast-types-flow "^0.0.8"
      axe-core "^4.9.1"
      axobject-query "~3.1.1"
      damerau-levenshtein "^1.0.8"
      emoji-regex "^9.2.2"
      es-iterator-helpers "^1.0.19"
      hasown "^2.0.2"
      jsx-ast-utils "^3.3.5"
      language-tags "^1.0.9"
      minimatch "^3.1.2"
      object.fromentries "^2.0.8"
      safe-regex-test "^1.0.3"
      string.prototype.includes "^2.0.0"
  
  eslint-plugin-node@^11.1.0:
    version "11.1.0"
    resolved "https://registry.npmmirror.com/eslint-plugin-node/-/eslint-plugin-node-11.1.0.tgz"
    integrity sha512-oUwtPJ1W0SKD0Tr+wqu92c5xuCeQqB3hSCHasn/ZgjFdA9iDGNkNf2Zi9ztY7X+hNuMib23LNGRm6+uN+KLE3g==
    dependencies:
      eslint-plugin-es "^3.0.0"
      eslint-utils "^2.0.0"
      ignore "^5.1.1"
      minimatch "^3.0.4"
      resolve "^1.10.1"
      semver "^6.1.0"
  
  eslint-plugin-prettier@^4.2.1:
    version "4.2.1"
    resolved "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz"
    integrity sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ==
    dependencies:
      prettier-linter-helpers "^1.0.0"
  
  eslint-plugin-react-hooks@^4.6.0:
    version "4.6.2"
    resolved "https://registry.npmmirror.com/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.2.tgz"
    integrity sha512-QzliNJq4GinDBcD8gPB5v0wh6g8q3SUi6EFF0x8N/BL9PoVs0atuGc47ozMRyOWAKdwaZ5OnbOEa3WR+dSGKuQ==
  
  eslint-plugin-react@^7.33.2:
    version "7.35.0"
    resolved "https://registry.npmmirror.com/eslint-plugin-react/-/eslint-plugin-react-7.35.0.tgz"
    integrity sha512-v501SSMOWv8gerHkk+IIQBkcGRGrO2nfybfj5pLxuJNFTPxxA3PSryhXTK+9pNbtkggheDdsC0E9Q8CuPk6JKA==
    dependencies:
      array-includes "^3.1.8"
      array.prototype.findlast "^1.2.5"
      array.prototype.flatmap "^1.3.2"
      array.prototype.tosorted "^1.1.4"
      doctrine "^2.1.0"
      es-iterator-helpers "^1.0.19"
      estraverse "^5.3.0"
      hasown "^2.0.2"
      jsx-ast-utils "^2.4.1 || ^3.0.0"
      minimatch "^3.1.2"
      object.entries "^1.1.8"
      object.fromentries "^2.0.8"
      object.values "^1.2.0"
      prop-types "^15.8.1"
      resolve "^2.0.0-next.5"
      semver "^6.3.1"
      string.prototype.matchall "^4.0.11"
      string.prototype.repeat "^1.0.0"
  
  eslint-plugin-vue@^9.9.0:
    version "9.27.0"
    resolved "https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-9.27.0.tgz"
    integrity sha512-5Dw3yxEyuBSXTzT5/Ge1X5kIkRTQ3nvBn/VwPwInNiZBSJOO/timWMUaflONnFBzU6NhB68lxnCda7ULV5N7LA==
    dependencies:
      "@eslint-community/eslint-utils" "^4.4.0"
      globals "^13.24.0"
      natural-compare "^1.4.0"
      nth-check "^2.1.1"
      postcss-selector-parser "^6.0.15"
      semver "^7.6.0"
      vue-eslint-parser "^9.4.3"
      xml-name-validator "^4.0.0"
  
  eslint-scope@5.1.1:
    version "5.1.1"
    resolved "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz"
    integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
    dependencies:
      esrecurse "^4.3.0"
      estraverse "^4.1.1"
  
  eslint-scope@^7.1.1, eslint-scope@^7.2.2:
    version "7.2.2"
    resolved "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.2.tgz"
    integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
    dependencies:
      esrecurse "^4.3.0"
      estraverse "^5.2.0"
  
  eslint-utils@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-2.1.0.tgz"
    integrity sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==
    dependencies:
      eslint-visitor-keys "^1.1.0"
  
  eslint-visitor-keys@^1.1.0:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
    integrity sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==
  
  eslint-visitor-keys@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
    integrity sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==
  
  eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
    version "3.4.3"
    resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
    integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==
  
  eslint@^8.36.0:
    version "8.57.0"
    resolved "https://registry.npmmirror.com/eslint/-/eslint-8.57.0.tgz"
    integrity sha512-dZ6+mexnaTIbSBZWgou51U6OmzIhYM2VcNdtiTtI7qPNZm35Akpr0f6vtw3w1Kmn5PYo+tZVfh13WrhpS6oLqQ==
    dependencies:
      "@eslint-community/eslint-utils" "^4.2.0"
      "@eslint-community/regexpp" "^4.6.1"
      "@eslint/eslintrc" "^2.1.4"
      "@eslint/js" "8.57.0"
      "@humanwhocodes/config-array" "^0.11.14"
      "@humanwhocodes/module-importer" "^1.0.1"
      "@nodelib/fs.walk" "^1.2.8"
      "@ungap/structured-clone" "^1.2.0"
      ajv "^6.12.4"
      chalk "^4.0.0"
      cross-spawn "^7.0.2"
      debug "^4.3.2"
      doctrine "^3.0.0"
      escape-string-regexp "^4.0.0"
      eslint-scope "^7.2.2"
      eslint-visitor-keys "^3.4.3"
      espree "^9.6.1"
      esquery "^1.4.2"
      esutils "^2.0.2"
      fast-deep-equal "^3.1.3"
      file-entry-cache "^6.0.1"
      find-up "^5.0.0"
      glob-parent "^6.0.2"
      globals "^13.19.0"
      graphemer "^1.4.0"
      ignore "^5.2.0"
      imurmurhash "^0.1.4"
      is-glob "^4.0.0"
      is-path-inside "^3.0.3"
      js-yaml "^4.1.0"
      json-stable-stringify-without-jsonify "^1.0.1"
      levn "^0.4.1"
      lodash.merge "^4.6.2"
      minimatch "^3.1.2"
      natural-compare "^1.4.0"
      optionator "^0.9.3"
      strip-ansi "^6.0.1"
      text-table "^0.2.0"
  
  espree@^9.3.1, espree@^9.6.0, espree@^9.6.1:
    version "9.6.1"
    resolved "https://registry.npmmirror.com/espree/-/espree-9.6.1.tgz"
    integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
    dependencies:
      acorn "^8.9.0"
      acorn-jsx "^5.3.2"
      eslint-visitor-keys "^3.4.1"
  
  esquery@^1.4.0, esquery@^1.4.2, esquery@^1.5.0:
    version "1.6.0"
    resolved "https://registry.npmmirror.com/esquery/-/esquery-1.6.0.tgz"
    integrity sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==
    dependencies:
      estraverse "^5.1.0"
  
  esrecurse@^4.3.0:
    version "4.3.0"
    resolved "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz"
    integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
    dependencies:
      estraverse "^5.2.0"
  
  estraverse@^4.1.1:
    version "4.3.0"
    resolved "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz"
    integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==
  
  estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
    version "5.3.0"
    resolved "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz"
    integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==
  
  esutils@^2.0.2:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz"
    integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==
  
  fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
    version "3.1.3"
    resolved "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
    integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==
  
  fast-diff@^1.1.2:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz"
    integrity sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==
  
  fast-glob@^3.2.9, fast-glob@^3.3.2:
    version "3.3.2"
    resolved "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.2.tgz"
    integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
    dependencies:
      "@nodelib/fs.stat" "^2.0.2"
      "@nodelib/fs.walk" "^1.2.3"
      glob-parent "^5.1.2"
      merge2 "^1.3.0"
      micromatch "^4.0.4"
  
  fast-json-stable-stringify@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
    integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==
  
  fast-levenshtein@^2.0.6:
    version "2.0.6"
    resolved "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
    integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==
  
  fastq@^1.6.0:
    version "1.17.1"
    resolved "https://registry.npmmirror.com/fastq/-/fastq-1.17.1.tgz"
    integrity sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==
    dependencies:
      reusify "^1.0.4"
  
  file-entry-cache@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
    integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
    dependencies:
      flat-cache "^3.0.4"
  
  fill-range@^7.1.1:
    version "7.1.1"
    resolved "https://registry.npmmirror.com/fill-range/-/fill-range-7.1.1.tgz"
    integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
    dependencies:
      to-regex-range "^5.0.1"
  
  find-up@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz"
    integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
    dependencies:
      locate-path "^6.0.0"
      path-exists "^4.0.0"
  
  flat-cache@^3.0.4:
    version "3.2.0"
    resolved "https://registry.npmmirror.com/flat-cache/-/flat-cache-3.2.0.tgz"
    integrity sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
    dependencies:
      flatted "^3.2.9"
      keyv "^4.5.3"
      rimraf "^3.0.2"
  
  flatted@^3.2.9:
    version "3.3.1"
    resolved "https://registry.npmmirror.com/flatted/-/flatted-3.3.1.tgz"
    integrity sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==
  
  follow-redirects@1.5.10:
    version "1.5.10"
    resolved "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.5.10.tgz"
    integrity sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==
    dependencies:
      debug "=3.1.0"
  
  follow-redirects@^1.14.8:
    version "1.15.6"
    resolved "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.6.tgz"
    integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==
  
  for-each@^0.3.3:
    version "0.3.3"
    resolved "https://registry.npmmirror.com/for-each/-/for-each-0.3.3.tgz"
    integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
    dependencies:
      is-callable "^1.1.3"
  
  fs.realpath@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz"
    integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==
  
  function-bind@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz"
    integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==
  
  function.prototype.name@^1.1.6:
    version "1.1.6"
    resolved "https://registry.npmmirror.com/function.prototype.name/-/function.prototype.name-1.1.6.tgz"
    integrity sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.2.0"
      es-abstract "^1.22.1"
      functions-have-names "^1.2.3"
  
  functions-have-names@^1.2.3:
    version "1.2.3"
    resolved "https://registry.npmmirror.com/functions-have-names/-/functions-have-names-1.2.3.tgz"
    integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==
  
  gensync@^1.0.0-beta.2:
    version "1.0.0-beta.2"
    resolved "https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz"
    integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==
  
  geojson-equality@0.1.6:
    version "0.1.6"
    resolved "https://registry.npmmirror.com/geojson-equality/-/geojson-equality-0.1.6.tgz"
    integrity sha512-TqG8YbqizP3EfwP5Uw4aLu6pKkg6JQK9uq/XZ1lXQntvTHD1BBKJWhNpJ2M0ax6TuWMP3oyx6Oq7FCIfznrgpQ==
    dependencies:
      deep-equal "^1.0.0"
  
  geojson-rbush@3.x:
    version "3.2.0"
    resolved "https://registry.npmmirror.com/geojson-rbush/-/geojson-rbush-3.2.0.tgz"
    integrity sha512-oVltQTXolxvsz1sZnutlSuLDEcQAKYC/uXt9zDzJJ6bu0W+baTI8LZBaTup5afzibEH4N3jlq2p+a152wlBJ7w==
    dependencies:
      "@turf/bbox" "*"
      "@turf/helpers" "6.x"
      "@turf/meta" "6.x"
      "@types/geojson" "7946.0.8"
      rbush "^3.0.1"
  
  geojson-vt@^3.2.1:
    version "3.2.1"
    resolved "https://registry.npmmirror.com/geojson-vt/-/geojson-vt-3.2.1.tgz"
    integrity sha512-EvGQQi/zPrDA6zr6BnJD/YhwAkBP8nnJ9emh3EnHQKVMfg/MRVtPbMYdgVy/IaEmn4UfagD2a6fafPDL5hbtwg==
  
  get-intrinsic@^1.1.3, get-intrinsic@^1.2.1, get-intrinsic@^1.2.2, get-intrinsic@^1.2.3, get-intrinsic@^1.2.4:
    version "1.2.4"
    resolved "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
    integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
    dependencies:
      es-errors "^1.3.0"
      function-bind "^1.1.2"
      has-proto "^1.0.1"
      has-symbols "^1.0.3"
      hasown "^2.0.0"
  
  get-stream@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz"
    integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==
  
  get-symbol-description@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/get-symbol-description/-/get-symbol-description-1.0.2.tgz"
    integrity sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg==
    dependencies:
      call-bind "^1.0.5"
      es-errors "^1.3.0"
      get-intrinsic "^1.2.4"
  
  get-tsconfig@^4.7.5:
    version "4.8.0"
    resolved "https://registry.npmmirror.com/get-tsconfig/-/get-tsconfig-4.8.0.tgz"
    integrity sha512-Pgba6TExTZ0FJAn1qkJAjIeKoDJ3CsI2ChuLohJnZl/tTU8MVrq3b+2t5UOPfRa4RMsorClBjJALkJUMjG1PAw==
    dependencies:
      resolve-pkg-maps "^1.0.0"
  
  gl-matrix@^3.4.3:
    version "3.4.3"
    resolved "https://registry.npmmirror.com/gl-matrix/-/gl-matrix-3.4.3.tgz"
    integrity sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA==
  
  glob-parent@^5.1.2:
    version "5.1.2"
    resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
    integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
    dependencies:
      is-glob "^4.0.1"
  
  glob-parent@^6.0.2:
    version "6.0.2"
    resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz"
    integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
    dependencies:
      is-glob "^4.0.3"
  
  glob@^7.1.3:
    version "7.2.3"
    resolved "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz"
    integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^3.1.1"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  globals@^11.1.0:
    version "11.12.0"
    resolved "https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz"
    integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==
  
  globals@^13.19.0, globals@^13.24.0:
    version "13.24.0"
    resolved "https://registry.npmmirror.com/globals/-/globals-13.24.0.tgz"
    integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
    dependencies:
      type-fest "^0.20.2"
  
  globalthis@^1.0.3:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/globalthis/-/globalthis-1.0.4.tgz"
    integrity sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==
    dependencies:
      define-properties "^1.2.1"
      gopd "^1.0.1"
  
  globby@^11.1.0:
    version "11.1.0"
    resolved "https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz"
    integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
    dependencies:
      array-union "^2.1.0"
      dir-glob "^3.0.1"
      fast-glob "^3.2.9"
      ignore "^5.2.0"
      merge2 "^1.4.1"
      slash "^3.0.0"
  
  gopd@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/gopd/-/gopd-1.0.1.tgz"
    integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
    dependencies:
      get-intrinsic "^1.1.3"
  
  graceful-fs@^4.2.4:
    version "4.2.11"
    resolved "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz"
    integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==
  
  graphemer@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmmirror.com/graphemer/-/graphemer-1.4.0.tgz"
    integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==
  
  grid-index@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/grid-index/-/grid-index-1.1.0.tgz"
    integrity sha512-HZRwumpOGUrHyxO5bqKZL0B0GlUpwtCAzZ42sgxUPniu33R1LSFH5yrIcBCHjkctCAh3mtWKcKd9J4vDDdeVHA==
  
  gt-mis-app-components@^1.2.7:
    version "1.2.7"
    resolved "https://registry.npmmirror.com/gt-mis-app-components/-/gt-mis-app-components-1.2.7.tgz"
    integrity sha512-MHGuYLLGRC7y/x7BIT5EzABrkUaxOWwfTBgfs8T9xPJTeFpC71wYtkN70al0QLR1q/9czsTVTkDlHm7h8bAcSQ==
  
  has-bigints@^1.0.1, has-bigints@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/has-bigints/-/has-bigints-1.0.2.tgz"
    integrity sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==
  
  has-flag@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/has-flag/-/has-flag-3.0.0.tgz"
    integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==
  
  has-flag@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"
    integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==
  
  has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
    integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
    dependencies:
      es-define-property "^1.0.0"
  
  has-proto@^1.0.1, has-proto@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/has-proto/-/has-proto-1.0.3.tgz"
    integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==
  
  has-symbols@^1.0.2, has-symbols@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz"
    integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==
  
  has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
    integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
    dependencies:
      has-symbols "^1.0.3"
  
  hasown@^2.0.0, hasown@^2.0.1, hasown@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz"
    integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
    dependencies:
      function-bind "^1.1.2"
  
  ieee754@^1.1.12:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz"
    integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==
  
  ignore@^5.1.1, ignore@^5.2.0, ignore@^5.2.4:
    version "5.3.2"
    resolved "https://registry.npmmirror.com/ignore/-/ignore-5.3.2.tgz"
    integrity sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==
  
  import-fresh@^3.2.1:
    version "3.3.0"
    resolved "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz"
    integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
    dependencies:
      parent-module "^1.0.0"
      resolve-from "^4.0.0"
  
  imurmurhash@^0.1.4:
    version "0.1.4"
    resolved "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz"
    integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==
  
  inflight@^1.0.4:
    version "1.0.6"
    resolved "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz"
    integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
    dependencies:
      once "^1.3.0"
      wrappy "1"
  
  inherits@2:
    version "2.0.4"
    resolved "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz"
    integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==
  
  internal-slot@^1.0.4, internal-slot@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmmirror.com/internal-slot/-/internal-slot-1.0.7.tgz"
    integrity sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==
    dependencies:
      es-errors "^1.3.0"
      hasown "^2.0.0"
      side-channel "^1.0.4"
  
  is-arguments@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/is-arguments/-/is-arguments-1.1.1.tgz"
    integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-array-buffer@^3.0.2, is-array-buffer@^3.0.4:
    version "3.0.4"
    resolved "https://registry.npmmirror.com/is-array-buffer/-/is-array-buffer-3.0.4.tgz"
    integrity sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.2.1"
  
  is-async-function@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/is-async-function/-/is-async-function-2.0.0.tgz"
    integrity sha512-Y1JXKrfykRJGdlDwdKlLpLyMIiWqWvuSd17TvZk68PLAOGOoF4Xyav1z0Xhoi+gCYjZVeC5SI+hYFOfvXmGRCA==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-bigint@^1.0.1:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/is-bigint/-/is-bigint-1.0.4.tgz"
    integrity sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==
    dependencies:
      has-bigints "^1.0.1"
  
  is-boolean-object@^1.1.0:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
    integrity sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-builtin-module@^3.2.1:
    version "3.2.1"
    resolved "https://registry.npmmirror.com/is-builtin-module/-/is-builtin-module-3.2.1.tgz"
    integrity sha512-BSLE3HnV2syZ0FK0iMA/yUGplUeMmNz4AW5fnTunbCIqZi4vG3WjJT9FHMy5D69xmAYBHXQhJdALdpwVxV501A==
    dependencies:
      builtin-modules "^3.3.0"
  
  is-bun-module@^1.0.2:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/is-bun-module/-/is-bun-module-1.1.0.tgz"
    integrity sha512-4mTAVPlrXpaN3jtF0lsnPCMGnq4+qZjVIKq0HCpfcqf8OC1SM5oATCIAPM5V5FN05qp2NNnFndphmdZS9CV3hA==
    dependencies:
      semver "^7.6.3"
  
  is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
    version "1.2.7"
    resolved "https://registry.npmmirror.com/is-callable/-/is-callable-1.2.7.tgz"
    integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==
  
  is-core-module@^2.13.0, is-core-module@^2.13.1:
    version "2.15.1"
    resolved "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.15.1.tgz"
    integrity sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==
    dependencies:
      hasown "^2.0.2"
  
  is-data-view@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/is-data-view/-/is-data-view-1.0.1.tgz"
    integrity sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w==
    dependencies:
      is-typed-array "^1.1.13"
  
  is-date-object@^1.0.1, is-date-object@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmmirror.com/is-date-object/-/is-date-object-1.0.5.tgz"
    integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-extglob@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz"
    integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==
  
  is-finalizationregistry@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/is-finalizationregistry/-/is-finalizationregistry-1.0.2.tgz"
    integrity sha512-0by5vtUJs8iFQb5TYUHHPudOR+qXYIMKtiUzvLIZITZUjknFmziyBJuLhVRc+Ds0dREFlskDNJKYIdIzu/9pfw==
    dependencies:
      call-bind "^1.0.2"
  
  is-generator-function@^1.0.10:
    version "1.0.10"
    resolved "https://registry.npmmirror.com/is-generator-function/-/is-generator-function-1.0.10.tgz"
    integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3:
    version "4.0.3"
    resolved "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz"
    integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
    dependencies:
      is-extglob "^2.1.1"
  
  is-map@^2.0.2, is-map@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/is-map/-/is-map-2.0.3.tgz"
    integrity sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==
  
  is-negative-zero@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/is-negative-zero/-/is-negative-zero-2.0.3.tgz"
    integrity sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==
  
  is-number-object@^1.0.4:
    version "1.0.7"
    resolved "https://registry.npmmirror.com/is-number-object/-/is-number-object-1.0.7.tgz"
    integrity sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-number@^7.0.0:
    version "7.0.0"
    resolved "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz"
    integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==
  
  is-path-inside@^3.0.3:
    version "3.0.3"
    resolved "https://registry.npmmirror.com/is-path-inside/-/is-path-inside-3.0.3.tgz"
    integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==
  
  is-regex@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/is-regex/-/is-regex-1.1.4.tgz"
    integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-set@^2.0.2, is-set@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/is-set/-/is-set-2.0.3.tgz"
    integrity sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==
  
  is-shared-array-buffer@^1.0.2, is-shared-array-buffer@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/is-shared-array-buffer/-/is-shared-array-buffer-1.0.3.tgz"
    integrity sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==
    dependencies:
      call-bind "^1.0.7"
  
  is-string@^1.0.5, is-string@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmmirror.com/is-string/-/is-string-1.0.7.tgz"
    integrity sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-symbol@^1.0.2, is-symbol@^1.0.3:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/is-symbol/-/is-symbol-1.0.4.tgz"
    integrity sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==
    dependencies:
      has-symbols "^1.0.2"
  
  is-typed-array@^1.1.13:
    version "1.1.13"
    resolved "https://registry.npmmirror.com/is-typed-array/-/is-typed-array-1.1.13.tgz"
    integrity sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==
    dependencies:
      which-typed-array "^1.1.14"
  
  is-weakmap@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/is-weakmap/-/is-weakmap-2.0.2.tgz"
    integrity sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==
  
  is-weakref@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/is-weakref/-/is-weakref-1.0.2.tgz"
    integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
    dependencies:
      call-bind "^1.0.2"
  
  is-weakset@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/is-weakset/-/is-weakset-2.0.3.tgz"
    integrity sha512-LvIm3/KWzS9oRFHugab7d+M/GcBXuXX5xZkzPmN+NxihdQlZUQ4dWuSV1xR/sq6upL1TJEDrfBgRepHFdBtSNQ==
    dependencies:
      call-bind "^1.0.7"
      get-intrinsic "^1.2.4"
  
  isarray@^2.0.5:
    version "2.0.5"
    resolved "https://registry.npmmirror.com/isarray/-/isarray-2.0.5.tgz"
    integrity sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==
  
  isexe@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz"
    integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==
  
  iterator.prototype@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/iterator.prototype/-/iterator.prototype-1.1.2.tgz"
    integrity sha512-DR33HMMr8EzwuRL8Y9D3u2BMj8+RqSE850jfGu59kS7tbmPLzGkZmVSfyCFSDxuZiEY6Rzt3T2NA/qU+NwVj1w==
    dependencies:
      define-properties "^1.2.1"
      get-intrinsic "^1.2.1"
      has-symbols "^1.0.3"
      reflect.getprototypeof "^1.0.4"
      set-function-name "^2.0.1"
  
  "js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz"
    integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==
  
  js-yaml@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz"
    integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
    dependencies:
      argparse "^2.0.1"
  
  jsdoc-type-pratt-parser@~4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/jsdoc-type-pratt-parser/-/jsdoc-type-pratt-parser-4.0.0.tgz"
    integrity sha512-YtOli5Cmzy3q4dP26GraSOeAhqecewG04hoO8DY56CH4KJ9Fvv5qKWUCCo3HZob7esJQHCv6/+bnTy72xZZaVQ==
  
  jsesc@^2.5.1:
    version "2.5.2"
    resolved "https://registry.npmmirror.com/jsesc/-/jsesc-2.5.2.tgz"
    integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==
  
  json-buffer@3.0.1:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.1.tgz"
    integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==
  
  json-schema-traverse@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
    integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==
  
  json-stable-stringify-without-jsonify@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
    integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==
  
  json5@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/json5/-/json5-1.0.2.tgz"
    integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
    dependencies:
      minimist "^1.2.0"
  
  json5@^2.2.3:
    version "2.2.3"
    resolved "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz"
    integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==
  
  "jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.5:
    version "3.3.5"
    resolved "https://registry.npmmirror.com/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz"
    integrity sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==
    dependencies:
      array-includes "^3.1.6"
      array.prototype.flat "^1.3.1"
      object.assign "^4.1.4"
      object.values "^1.1.6"
  
  kdbush@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/kdbush/-/kdbush-3.0.0.tgz"
    integrity sha512-hRkd6/XW4HTsA9vjVpY9tuXJYLSlelnkTmVFu4M9/7MIYQtFcHpbugAU7UbOfjOiVSVYl2fqgBuJ32JUmRo5Ew==
  
  keyv@^4.5.3:
    version "4.5.4"
    resolved "https://registry.npmmirror.com/keyv/-/keyv-4.5.4.tgz"
    integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
    dependencies:
      json-buffer "3.0.1"
  
  language-subtag-registry@^0.3.20:
    version "0.3.23"
    resolved "https://registry.npmmirror.com/language-subtag-registry/-/language-subtag-registry-0.3.23.tgz"
    integrity sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==
  
  language-tags@^1.0.9:
    version "1.0.9"
    resolved "https://registry.npmmirror.com/language-tags/-/language-tags-1.0.9.tgz"
    integrity sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==
    dependencies:
      language-subtag-registry "^0.3.20"
  
  levn@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz"
    integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
    dependencies:
      prelude-ls "^1.2.1"
      type-check "~0.4.0"
  
  locate-path@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz"
    integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
    dependencies:
      p-locate "^5.0.0"
  
  lodash.merge@^4.6.2:
    version "4.6.2"
    resolved "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz"
    integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==
  
  lodash@^4.17.21, lodash@~4.17.21:
    version "4.17.21"
    resolved "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
    integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==
  
  loose-envify@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz"
    integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
    dependencies:
      js-tokens "^3.0.0 || ^4.0.0"
  
  lru-cache@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz"
    integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
    dependencies:
      yallist "^3.0.2"
  
  mapbox-gl@2.12.1:
    version "2.12.1"
    resolved "https://registry.npmmirror.com/mapbox-gl/-/mapbox-gl-2.12.1.tgz"
    integrity sha512-45LVQauimFGX/fkCJzK3O2KpQQrIB0fGlg8sERu4NH0xWiBw9JsLOLYD2xAgD5SPramQvsjzM7vYWIkGxpGYNQ==
    dependencies:
      "@mapbox/geojson-rewind" "^0.5.2"
      "@mapbox/jsonlint-lines-primitives" "^2.0.2"
      "@mapbox/mapbox-gl-supported" "^2.0.1"
      "@mapbox/point-geometry" "^0.1.0"
      "@mapbox/tiny-sdf" "^2.0.6"
      "@mapbox/unitbezier" "^0.0.1"
      "@mapbox/vector-tile" "^1.3.1"
      "@mapbox/whoots-js" "^3.1.0"
      csscolorparser "~1.0.3"
      earcut "^2.2.4"
      geojson-vt "^3.2.1"
      gl-matrix "^3.4.3"
      grid-index "^1.1.0"
      murmurhash-js "^1.0.0"
      pbf "^3.2.1"
      potpack "^2.0.0"
      quickselect "^2.0.0"
      rw "^1.3.3"
      supercluster "^7.1.5"
      tinyqueue "^2.0.3"
      vt-pbf "^3.1.3"
  
  merge2@^1.3.0, merge2@^1.4.1:
    version "1.4.1"
    resolved "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz"
    integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==
  
  micromatch@^4.0.4:
    version "4.0.8"
    resolved "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.8.tgz"
    integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
    dependencies:
      braces "^3.0.3"
      picomatch "^2.3.1"
  
  minimatch@9.0.3:
    version "9.0.3"
    resolved "https://registry.npmmirror.com/minimatch/-/minimatch-9.0.3.tgz"
    integrity sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==
    dependencies:
      brace-expansion "^2.0.1"
  
  minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
    version "3.1.2"
    resolved "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz"
    integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
    dependencies:
      brace-expansion "^1.1.7"
  
  minimist@^1.2.0, minimist@^1.2.6:
    version "1.2.8"
    resolved "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz"
    integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==
  
  moment@^2.29.1:
    version "2.30.1"
    resolved "https://registry.npmmirror.com/moment/-/moment-2.30.1.tgz"
    integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==
  
  ms@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz"
    integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==
  
  ms@2.1.2:
    version "2.1.2"
    resolved "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz"
    integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==
  
  ms@^2.1.1:
    version "2.1.3"
    resolved "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz"
    integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==
  
  murmurhash-js@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/murmurhash-js/-/murmurhash-js-1.0.0.tgz"
    integrity sha512-TvmkNhkv8yct0SVBSy+o8wYzXjE4Zz3PCesbfs8HiCXXdcTuocApFv11UWlNFWKYsP2okqrhb7JNlSm9InBhIw==
  
  natural-compare@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz"
    integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==
  
  node-releases@^2.0.18:
    version "2.0.18"
    resolved "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.18.tgz"
    integrity sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==
  
  nth-check@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz"
    integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
    dependencies:
      boolbase "^1.0.0"
  
  object-assign@*, object-assign@^4.1.1:
    version "4.1.1"
    resolved "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz"
    integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==
  
  object-inspect@^1.13.1:
    version "1.13.2"
    resolved "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.2.tgz"
    integrity sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==
  
  object-is@^1.1.5:
    version "1.1.6"
    resolved "https://registry.npmmirror.com/object-is/-/object-is-1.1.6.tgz"
    integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
  
  object-keys@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz"
    integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==
  
  object.assign@^4.1.2, object.assign@^4.1.4, object.assign@^4.1.5:
    version "4.1.5"
    resolved "https://registry.npmmirror.com/object.assign/-/object.assign-4.1.5.tgz"
    integrity sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==
    dependencies:
      call-bind "^1.0.5"
      define-properties "^1.2.1"
      has-symbols "^1.0.3"
      object-keys "^1.1.1"
  
  object.entries@^1.1.5, object.entries@^1.1.8:
    version "1.1.8"
    resolved "https://registry.npmmirror.com/object.entries/-/object.entries-1.1.8.tgz"
    integrity sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-object-atoms "^1.0.0"
  
  object.fromentries@^2.0.7, object.fromentries@^2.0.8:
    version "2.0.8"
    resolved "https://registry.npmmirror.com/object.fromentries/-/object.fromentries-2.0.8.tgz"
    integrity sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-object-atoms "^1.0.0"
  
  object.groupby@^1.0.1:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/object.groupby/-/object.groupby-1.0.3.tgz"
    integrity sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
  
  object.values@^1.1.6, object.values@^1.1.7, object.values@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/object.values/-/object.values-1.2.0.tgz"
    integrity sha512-yBYjY9QX2hnRmZHAjG/f13MzmBzxzYgQhFrke06TTyKY5zSTEqkOeukBzIdVA3j3ulu8Qa3MbVFShV7T2RmGtQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-object-atoms "^1.0.0"
  
  omit-deep-lodash@^1.1.7:
    version "1.1.7"
    resolved "https://registry.npmmirror.com/omit-deep-lodash/-/omit-deep-lodash-1.1.7.tgz"
    integrity sha512-9m9gleSMoxq3YO8aCq5pGUrqG9rKF0w/P70JHQ1ymjUQA/3+fVa2Stju9XORJKLmyLYEO3zzX40MJYaYl5Og4w==
    dependencies:
      lodash "~4.17.21"
  
  once@^1.3.0:
    version "1.4.0"
    resolved "https://registry.npmmirror.com/once/-/once-1.4.0.tgz"
    integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
    dependencies:
      wrappy "1"
  
  optionator@^0.9.3:
    version "0.9.4"
    resolved "https://registry.npmmirror.com/optionator/-/optionator-0.9.4.tgz"
    integrity sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==
    dependencies:
      deep-is "^0.1.3"
      fast-levenshtein "^2.0.6"
      levn "^0.4.1"
      prelude-ls "^1.2.1"
      type-check "^0.4.0"
      word-wrap "^1.2.5"
  
  p-limit@^3.0.2:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz"
    integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
    dependencies:
      yocto-queue "^0.1.0"
  
  p-locate@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz"
    integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
    dependencies:
      p-limit "^3.0.2"
  
  parent-module@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz"
    integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
    dependencies:
      callsites "^3.0.0"
  
  path-exists@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz"
    integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==
  
  path-is-absolute@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
    integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==
  
  path-key@^3.1.0:
    version "3.1.1"
    resolved "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
    integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==
  
  path-parse@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz"
    integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==
  
  path-type@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz"
    integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==
  
  pbf@^3.2.1:
    version "3.3.0"
    resolved "https://registry.npmmirror.com/pbf/-/pbf-3.3.0.tgz"
    integrity sha512-XDF38WCH3z5OV/OVa8GKUNtLAyneuzbCisx7QUCF8Q6Nutx0WnJrQe5O+kOtBlLfRNUws98Y58Lblp+NJG5T4Q==
    dependencies:
      ieee754 "^1.1.12"
      resolve-protobuf-schema "^2.1.0"
  
  picocolors@^1.0.0, picocolors@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.1.tgz"
    integrity sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==
  
  picomatch@^2.3.1:
    version "2.3.1"
    resolved "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz"
    integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==
  
  point-in-polygon@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/point-in-polygon/-/point-in-polygon-1.1.0.tgz"
    integrity sha512-3ojrFwjnnw8Q9242TzgXuTD+eKiutbzyslcq1ydfu82Db2y+Ogbmyrkpv0Hgj31qwT3lbS9+QAAO/pIQM35XRw==
  
  polygon-clipping@^0.15.3:
    version "0.15.7"
    resolved "https://registry.npmmirror.com/polygon-clipping/-/polygon-clipping-0.15.7.tgz"
    integrity sha512-nhfdr83ECBg6xtqOAJab1tbksbBAOMUltN60bU+llHVOL0e5Onm1WpAXXWXVB39L8AJFssoIhEVuy/S90MmotA==
    dependencies:
      robust-predicates "^3.0.2"
      splaytree "^3.1.0"
  
  possible-typed-array-names@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz"
    integrity sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==
  
  postcss-selector-parser@^6.0.15:
    version "6.1.2"
    resolved "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
    integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
    dependencies:
      cssesc "^3.0.0"
      util-deprecate "^1.0.2"
  
  potpack@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/potpack/-/potpack-2.0.0.tgz"
    integrity sha512-Q+/tYsFU9r7xoOJ+y/ZTtdVQwTWfzjbiXBDMM/JKUux3+QPP02iUuIoeBQ+Ot6oEDlC+/PGjB/5A3K7KKb7hcw==
  
  prelude-ls@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz"
    integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==
  
  prettier-linter-helpers@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
    integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
    dependencies:
      fast-diff "^1.1.2"
  
  prettier@^2.8.4:
    version "2.8.8"
    resolved "https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz"
    integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==
  
  prop-types@^15.8.1:
    version "15.8.1"
    resolved "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz"
    integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
    dependencies:
      loose-envify "^1.4.0"
      object-assign "^4.1.1"
      react-is "^16.13.1"
  
  protocol-buffers-schema@^3.3.1:
    version "3.6.0"
    resolved "https://registry.npmmirror.com/protocol-buffers-schema/-/protocol-buffers-schema-3.6.0.tgz"
    integrity sha512-TdDRD+/QNdrCGCE7v8340QyuXd4kIWIgapsE2+n/SaGiSSbomYl4TjHlvIoCWRpE7wFt02EpB35VVA2ImcBVqw==
  
  punycode@^2.1.0:
    version "2.3.1"
    resolved "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz"
    integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==
  
  queue-microtask@^1.2.2:
    version "1.2.3"
    resolved "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz"
    integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==
  
  quickselect@^1.0.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/quickselect/-/quickselect-1.1.1.tgz"
    integrity sha512-qN0Gqdw4c4KGPsBOQafj6yj/PA6c/L63f6CaZ/DCF/xF4Esu3jVmKLUDYxghFx8Kb/O7y9tI7x2RjTSXwdK1iQ==
  
  quickselect@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/quickselect/-/quickselect-2.0.0.tgz"
    integrity sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw==
  
  rbush@2.x, rbush@^2.0.1:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/rbush/-/rbush-2.0.2.tgz"
    integrity sha512-XBOuALcTm+O/H8G90b6pzu6nX6v2zCKiFG4BJho8a+bY6AER6t8uQUZdi5bomQc0AprCWhEGa7ncAbbRap0bRA==
    dependencies:
      quickselect "^1.0.1"
  
  rbush@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/rbush/-/rbush-3.0.1.tgz"
    integrity sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w==
    dependencies:
      quickselect "^2.0.0"
  
  react-is@^16.13.1:
    version "16.13.1"
    resolved "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz"
    integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==
  
  reflect.getprototypeof@^1.0.4:
    version "1.0.6"
    resolved "https://registry.npmmirror.com/reflect.getprototypeof/-/reflect.getprototypeof-1.0.6.tgz"
    integrity sha512-fmfw4XgoDke3kdI6h4xcUz1dG8uaiv5q9gcEwLS4Pnth2kxT+GZ7YehS1JTMGBQmtV7Y4GFGbs2re2NqhdozUg==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.1"
      es-errors "^1.3.0"
      get-intrinsic "^1.2.4"
      globalthis "^1.0.3"
      which-builtin-type "^1.1.3"
  
  regexp.prototype.flags@^1.5.1, regexp.prototype.flags@^1.5.2:
    version "1.5.2"
    resolved "https://registry.npmmirror.com/regexp.prototype.flags/-/regexp.prototype.flags-1.5.2.tgz"
    integrity sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==
    dependencies:
      call-bind "^1.0.6"
      define-properties "^1.2.1"
      es-errors "^1.3.0"
      set-function-name "^2.0.1"
  
  regexpp@^3.0.0:
    version "3.2.0"
    resolved "https://registry.npmmirror.com/regexpp/-/regexpp-3.2.0.tgz"
    integrity sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==
  
  resolve-from@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz"
    integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==
  
  resolve-pkg-maps@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz"
    integrity sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==
  
  resolve-protobuf-schema@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/resolve-protobuf-schema/-/resolve-protobuf-schema-2.1.0.tgz"
    integrity sha512-kI5ffTiZWmJaS/huM8wZfEMer1eRd7oJQhDuxeCLe3t7N7mX3z94CN0xPxBQxFYQTSNz9T0i+v6inKqSdK8xrQ==
    dependencies:
      protocol-buffers-schema "^3.3.1"
  
  resolve@^1.10.1, resolve@^1.22.4:
    version "1.22.8"
    resolved "https://registry.npmmirror.com/resolve/-/resolve-1.22.8.tgz"
    integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
    dependencies:
      is-core-module "^2.13.0"
      path-parse "^1.0.7"
      supports-preserve-symlinks-flag "^1.0.0"
  
  resolve@^2.0.0-next.5:
    version "2.0.0-next.5"
    resolved "https://registry.npmmirror.com/resolve/-/resolve-2.0.0-next.5.tgz"
    integrity sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==
    dependencies:
      is-core-module "^2.13.0"
      path-parse "^1.0.7"
      supports-preserve-symlinks-flag "^1.0.0"
  
  reusify@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz"
    integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==
  
  rimraf@^3.0.2:
    version "3.0.2"
    resolved "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz"
    integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
    dependencies:
      glob "^7.1.3"
  
  robust-predicates@^2.0.4:
    version "2.0.4"
    resolved "https://registry.npmmirror.com/robust-predicates/-/robust-predicates-2.0.4.tgz"
    integrity sha512-l4NwboJM74Ilm4VKfbAtFeGq7aEjWL+5kVFcmgFA2MrdnQWx9iE/tUGvxY5HyMI7o/WpSIUFLbC5fbeaHgSCYg==
  
  robust-predicates@^3.0.2:
    version "3.0.2"
    resolved "https://registry.npmmirror.com/robust-predicates/-/robust-predicates-3.0.2.tgz"
    integrity sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==
  
  run-parallel@^1.1.9:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz"
    integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
    dependencies:
      queue-microtask "^1.2.2"
  
  rw@^1.3.3:
    version "1.3.3"
    resolved "https://registry.npmmirror.com/rw/-/rw-1.3.3.tgz"
    integrity sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==
  
  safe-array-concat@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/safe-array-concat/-/safe-array-concat-1.1.2.tgz"
    integrity sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q==
    dependencies:
      call-bind "^1.0.7"
      get-intrinsic "^1.2.4"
      has-symbols "^1.0.3"
      isarray "^2.0.5"
  
  safe-regex-test@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/safe-regex-test/-/safe-regex-test-1.0.3.tgz"
    integrity sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw==
    dependencies:
      call-bind "^1.0.6"
      es-errors "^1.3.0"
      is-regex "^1.1.4"
  
  semver@^6.1.0, semver@^6.3.0, semver@^6.3.1:
    version "6.3.1"
    resolved "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz"
    integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==
  
  semver@^7.3.6, semver@^7.5.4, semver@^7.6.0, semver@^7.6.3:
    version "7.6.3"
    resolved "https://registry.npmmirror.com/semver/-/semver-7.6.3.tgz"
    integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==
  
  set-function-length@^1.2.1:
    version "1.2.2"
    resolved "https://registry.npmmirror.com/set-function-length/-/set-function-length-1.2.2.tgz"
    integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
    dependencies:
      define-data-property "^1.1.4"
      es-errors "^1.3.0"
      function-bind "^1.1.2"
      get-intrinsic "^1.2.4"
      gopd "^1.0.1"
      has-property-descriptors "^1.0.2"
  
  set-function-name@^2.0.1, set-function-name@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/set-function-name/-/set-function-name-2.0.2.tgz"
    integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
    dependencies:
      define-data-property "^1.1.4"
      es-errors "^1.3.0"
      functions-have-names "^1.2.3"
      has-property-descriptors "^1.0.2"
  
  shebang-command@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz"
    integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
    dependencies:
      shebang-regex "^3.0.0"
  
  shebang-regex@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz"
    integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==
  
  side-channel@^1.0.4, side-channel@^1.0.6:
    version "1.0.6"
    resolved "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.6.tgz"
    integrity sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==
    dependencies:
      call-bind "^1.0.7"
      es-errors "^1.3.0"
      get-intrinsic "^1.2.4"
      object-inspect "^1.13.1"
  
  skmeans@0.9.7:
    version "0.9.7"
    resolved "https://registry.npmmirror.com/skmeans/-/skmeans-0.9.7.tgz"
    integrity sha512-hNj1/oZ7ygsfmPZ7ZfN5MUBRoGg1gtpnImuJBgLO0ljQ67DtJuiQaiYdS4lUA6s0KCwnPhGivtC/WRwIZLkHyg==
  
  slash@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz"
    integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==
  
  spdx-exceptions@^2.1.0:
    version "2.5.0"
    resolved "https://registry.npmmirror.com/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz"
    integrity sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==
  
  spdx-expression-parse@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/spdx-expression-parse/-/spdx-expression-parse-4.0.0.tgz"
    integrity sha512-Clya5JIij/7C6bRR22+tnGXbc4VKlibKSVj2iHvVeX5iMW7s1SIQlqu699JkODJJIhh/pUu8L0/VLh8xflD+LQ==
    dependencies:
      spdx-exceptions "^2.1.0"
      spdx-license-ids "^3.0.0"
  
  spdx-license-ids@^3.0.0:
    version "3.0.20"
    resolved "https://registry.npmmirror.com/spdx-license-ids/-/spdx-license-ids-3.0.20.tgz"
    integrity sha512-jg25NiDV/1fLtSgEgyvVyDunvaNHbuwF9lfNV17gSmPFAlYzdfNBlLtLzXTevwkPj7DhGbmN9VnmJIgLnhvaBw==
  
  splaytree@^3.1.0:
    version "3.1.2"
    resolved "https://registry.npmmirror.com/splaytree/-/splaytree-3.1.2.tgz"
    integrity sha512-4OM2BJgC5UzrhVnnJA4BkHKGtjXNzzUfpQjCO8I05xYPsfS/VuQDwjCGGMi8rYQilHEV4j8NBqTFbls/PZEE7A==
  
  stop-iteration-iterator@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/stop-iteration-iterator/-/stop-iteration-iterator-1.0.0.tgz"
    integrity sha512-iCGQj+0l0HOdZ2AEeBADlsRC+vsnDsZsbdSiH1yNSjcfKM7fdpCMfqAL/dwF5BLiw/XhRft/Wax6zQbhq2BcjQ==
    dependencies:
      internal-slot "^1.0.4"
  
  string.prototype.includes@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/string.prototype.includes/-/string.prototype.includes-2.0.0.tgz"
    integrity sha512-E34CkBgyeqNDcrbU76cDjL5JLcVrtSdYq0MEh/B10r17pRP4ciHLwTgnuLV8Ay6cgEMLkcBkFCKyFZ43YldYzg==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.5"
  
  string.prototype.matchall@^4.0.11:
    version "4.0.11"
    resolved "https://registry.npmmirror.com/string.prototype.matchall/-/string.prototype.matchall-4.0.11.tgz"
    integrity sha512-NUdh0aDavY2og7IbBPenWqR9exH+E26Sv8e0/eTe1tltDGZL+GtBkDAnnyBtmekfK6/Dq3MkcGtzXFEd1LQrtg==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-errors "^1.3.0"
      es-object-atoms "^1.0.0"
      get-intrinsic "^1.2.4"
      gopd "^1.0.1"
      has-symbols "^1.0.3"
      internal-slot "^1.0.7"
      regexp.prototype.flags "^1.5.2"
      set-function-name "^2.0.2"
      side-channel "^1.0.6"
  
  string.prototype.repeat@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz"
    integrity sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.5"
  
  string.prototype.trim@^1.2.9:
    version "1.2.9"
    resolved "https://registry.npmmirror.com/string.prototype.trim/-/string.prototype.trim-1.2.9.tgz"
    integrity sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.0"
      es-object-atoms "^1.0.0"
  
  string.prototype.trimend@^1.0.8:
    version "1.0.8"
    resolved "https://registry.npmmirror.com/string.prototype.trimend/-/string.prototype.trimend-1.0.8.tgz"
    integrity sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-object-atoms "^1.0.0"
  
  string.prototype.trimstart@^1.0.8:
    version "1.0.8"
    resolved "https://registry.npmmirror.com/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz"
    integrity sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-object-atoms "^1.0.0"
  
  strip-ansi@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz"
    integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
    dependencies:
      ansi-regex "^5.0.1"
  
  strip-bom@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/strip-bom/-/strip-bom-3.0.0.tgz"
    integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==
  
  strip-json-comments@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
    integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==
  
  supercluster@^7.1.5:
    version "7.1.5"
    resolved "https://registry.npmmirror.com/supercluster/-/supercluster-7.1.5.tgz"
    integrity sha512-EulshI3pGUM66o6ZdH3ReiFcvHpM3vAigyK+vcxdjpJyEbIIrtbmBdY23mGgnI24uXiGFvrGq9Gkum/8U7vJWg==
    dependencies:
      kdbush "^3.0.0"
  
  supports-color@^5.3.0:
    version "5.5.0"
    resolved "https://registry.npmmirror.com/supports-color/-/supports-color-5.5.0.tgz"
    integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
    dependencies:
      has-flag "^3.0.0"
  
  supports-color@^7.1.0:
    version "7.2.0"
    resolved "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz"
    integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
    dependencies:
      has-flag "^4.0.0"
  
  supports-preserve-symlinks-flag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
    integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==
  
  tapable@^2.2.0:
    version "2.2.1"
    resolved "https://registry.npmmirror.com/tapable/-/tapable-2.2.1.tgz"
    integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==
  
  text-table@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz"
    integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==
  
  tinyqueue@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/tinyqueue/-/tinyqueue-2.0.3.tgz"
    integrity sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA==
  
  to-fast-properties@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
    integrity sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==
  
  to-regex-range@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz"
    integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
    dependencies:
      is-number "^7.0.0"
  
  topojson-client@3.x:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/topojson-client/-/topojson-client-3.1.0.tgz"
    integrity sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==
    dependencies:
      commander "2"
  
  topojson-server@3.x:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/topojson-server/-/topojson-server-3.0.1.tgz"
    integrity sha512-/VS9j/ffKr2XAOjlZ9CgyyeLmgJ9dMwq6Y0YEON8O7p/tGGk+dCWnrE03zEdu7i4L7YsFZLEPZPzCvcB7lEEXw==
    dependencies:
      commander "2"
  
  ts-api-utils@^1.0.1:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/ts-api-utils/-/ts-api-utils-1.3.0.tgz"
    integrity sha512-UQMIo7pb8WRomKR1/+MFVLTroIvDVtMX3K6OUir8ynLyzB8Jeriont2bTAtmNPa1ekAgN7YPDyf6V+ygrdU+eQ==
  
  tsconfig-paths@^3.15.0:
    version "3.15.0"
    resolved "https://registry.npmmirror.com/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz"
    integrity sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==
    dependencies:
      "@types/json5" "^0.0.29"
      json5 "^1.0.2"
      minimist "^1.2.6"
      strip-bom "^3.0.0"
  
  tslib@^2.6.2:
    version "2.7.0"
    resolved "https://registry.npmmirror.com/tslib/-/tslib-2.7.0.tgz"
    integrity sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==
  
  turf-jsts@*:
    version "1.2.3"
    resolved "https://registry.npmmirror.com/turf-jsts/-/turf-jsts-1.2.3.tgz"
    integrity sha512-Ja03QIJlPuHt4IQ2FfGex4F4JAr8m3jpaHbFbQrgwr7s7L6U8ocrHiF3J1+wf9jzhGKxvDeaCAnGDot8OjGFyA==
  
  type-check@^0.4.0, type-check@~0.4.0:
    version "0.4.0"
    resolved "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz"
    integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
    dependencies:
      prelude-ls "^1.2.1"
  
  type-fest@^0.20.2:
    version "0.20.2"
    resolved "https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz"
    integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==
  
  typed-array-buffer@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/typed-array-buffer/-/typed-array-buffer-1.0.2.tgz"
    integrity sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ==
    dependencies:
      call-bind "^1.0.7"
      es-errors "^1.3.0"
      is-typed-array "^1.1.13"
  
  typed-array-byte-length@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/typed-array-byte-length/-/typed-array-byte-length-1.0.1.tgz"
    integrity sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw==
    dependencies:
      call-bind "^1.0.7"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-proto "^1.0.3"
      is-typed-array "^1.1.13"
  
  typed-array-byte-offset@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/typed-array-byte-offset/-/typed-array-byte-offset-1.0.2.tgz"
    integrity sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA==
    dependencies:
      available-typed-arrays "^1.0.7"
      call-bind "^1.0.7"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-proto "^1.0.3"
      is-typed-array "^1.1.13"
  
  typed-array-length@^1.0.6:
    version "1.0.6"
    resolved "https://registry.npmmirror.com/typed-array-length/-/typed-array-length-1.0.6.tgz"
    integrity sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g==
    dependencies:
      call-bind "^1.0.7"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-proto "^1.0.3"
      is-typed-array "^1.1.13"
      possible-typed-array-names "^1.0.0"
  
  uid@^2.0.0:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/uid/-/uid-2.0.2.tgz"
    integrity sha512-u3xV3X7uzvi5b1MncmZo3i2Aw222Zk1keqLA1YkHldREkAhAqi65wuPfe7lHx8H/Wzy+8CE7S7uS3jekIM5s8g==
    dependencies:
      "@lukeed/csprng" "^1.0.0"
  
  unbox-primitive@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
    integrity sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==
    dependencies:
      call-bind "^1.0.2"
      has-bigints "^1.0.2"
      has-symbols "^1.0.3"
      which-boxed-primitive "^1.0.2"
  
  update-browserslist-db@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.1.0.tgz"
    integrity sha512-EdRAaAyk2cUE1wOf2DkEhzxqOQvFOoRJFNS6NeyJ01Gp2beMRpBAINjM2iDXE3KCuKhwnvHIQCJm6ThL2Z+HzQ==
    dependencies:
      escalade "^3.1.2"
      picocolors "^1.0.1"
  
  uri-js@^4.2.2:
    version "4.4.1"
    resolved "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz"
    integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
    dependencies:
      punycode "^2.1.0"
  
  util-deprecate@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz"
    integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==
  
  uview-ui@^2.0.35:
    version "2.0.37"
    resolved "https://registry.npmmirror.com/uview-ui/-/uview-ui-2.0.37.tgz"
    integrity sha512-iBcWNmQa01Wr+z004G6XIVPDctOrJIAx7LObQceUAPxZh6kJYjIOAMp5JE1K4VpoMV5bKYDpCd0gmX+M4nTEQQ==
  
  vt-pbf@^3.1.3:
    version "3.1.3"
    resolved "https://registry.npmmirror.com/vt-pbf/-/vt-pbf-3.1.3.tgz"
    integrity sha512-2LzDFzt0mZKZ9IpVF2r69G9bXaP2Q2sArJCmcCgvfTdCCZzSyz4aCLoQyUilu37Ll56tCblIZrXFIjNUpGIlmA==
    dependencies:
      "@mapbox/point-geometry" "0.1.0"
      "@mapbox/vector-tile" "^1.3.1"
      pbf "^3.2.1"
  
  vue-eslint-parser@^9.1.0, vue-eslint-parser@^9.4.3:
    version "9.4.3"
    resolved "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.4.3.tgz"
    integrity sha512-2rYRLWlIpaiN8xbPiDyXZXRgLGOtWxERV7ND5fFAv5qo1D2N9Fu9MNajBNc6o13lZ+24DAWCkQCvj4klgmcITg==
    dependencies:
      debug "^4.3.4"
      eslint-scope "^7.1.1"
      eslint-visitor-keys "^3.3.0"
      espree "^9.3.1"
      esquery "^1.4.0"
      lodash "^4.17.21"
      semver "^7.3.6"
  
  which-boxed-primitive@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
    integrity sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==
    dependencies:
      is-bigint "^1.0.1"
      is-boolean-object "^1.1.0"
      is-number-object "^1.0.4"
      is-string "^1.0.5"
      is-symbol "^1.0.3"
  
  which-builtin-type@^1.1.3:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/which-builtin-type/-/which-builtin-type-1.1.4.tgz"
    integrity sha512-bppkmBSsHFmIMSl8BO9TbsyzsvGjVoppt8xUiGzwiu/bhDCGxnpOKCxgqj6GuyHE0mINMDecBFPlOm2hzY084w==
    dependencies:
      function.prototype.name "^1.1.6"
      has-tostringtag "^1.0.2"
      is-async-function "^2.0.0"
      is-date-object "^1.0.5"
      is-finalizationregistry "^1.0.2"
      is-generator-function "^1.0.10"
      is-regex "^1.1.4"
      is-weakref "^1.0.2"
      isarray "^2.0.5"
      which-boxed-primitive "^1.0.2"
      which-collection "^1.0.2"
      which-typed-array "^1.1.15"
  
  which-collection@^1.0.1, which-collection@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/which-collection/-/which-collection-1.0.2.tgz"
    integrity sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==
    dependencies:
      is-map "^2.0.3"
      is-set "^2.0.3"
      is-weakmap "^2.0.2"
      is-weakset "^2.0.3"
  
  which-typed-array@^1.1.13, which-typed-array@^1.1.14, which-typed-array@^1.1.15:
    version "1.1.15"
    resolved "https://registry.npmmirror.com/which-typed-array/-/which-typed-array-1.1.15.tgz"
    integrity sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==
    dependencies:
      available-typed-arrays "^1.0.7"
      call-bind "^1.0.7"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-tostringtag "^1.0.2"
  
  which@^2.0.1:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
    integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
    dependencies:
      isexe "^2.0.0"
  
  word-wrap@^1.2.5:
    version "1.2.5"
    resolved "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.5.tgz"
    integrity sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==
  
  wrappy@1:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz"
    integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==
  
  xml-name-validator@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz"
    integrity sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==
  
  yallist@^3.0.2:
    version "3.1.1"
    resolved "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz"
    integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==
  
  yocto-queue@^0.1.0:
    version "0.1.0"
    resolved "https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz"
    integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==
