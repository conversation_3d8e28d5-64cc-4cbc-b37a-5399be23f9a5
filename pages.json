{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [
		{
			"path": "pages/Index/index"
		},
		{
			"path": "pages/User/index"
		},
		{
			"path": "pages/Workspace/index"
		},
		{
			"path": "pages/NewWorkspace/index"
		},
		{
			"path": "pages/PointListPage/index"
		},
		{
			"path": "pages/ChangePswPage/index"
		},
		{
			"path": "pages/FormList/index"
		},
		{
			"path": "pages/FormPage/index"
		},
		{
			"path": "pages/Login/index"
		},
		{
			"path": "pages/ViewPage/index"
		},
		{
			"path": "pages/MapFormListPage/index"
		},
		{
			"path": "pages/WildPlantPage/index"
		},
		{
			"path": "pages/WildPlantPage/taskList"
		},
		{
			"path": "components/form/field/SubForm/SubFormPage/index"
		},
		{
			"path": "app/pages/PlantsPointsList/index"
		},
		{
			"path": "app/pages/PlantsPointsFormList/index"
		},
		{
			"path": "app/pages/PlantsProgress/index"
		},
		{
			"path": "app/pages/PlantsFormTypes/index"
		},
		{
			"path": "app/pages/PlantsFormPage/index"
		},
		{
			"path": "app/pages/PlantsAudit/index"
		},
		{
			"path": "app/pages/PlantsAuditTabs/index"
		},
		{
			"path": "app/pages/PlantsProgressPointDetail/index"
		},
		{
			"path": "app/pages/UpdatePage/index"
		}
	],
	"tabBar": {
		"selectedColor": "#00a88a",
		"list": [
			{
				"selectedIconPath": "static/images/user-active.png",
				"iconPath": "static/images/user.png",
				"pagePath": "pages/Index/index",
				"text": "首页"
			},
			{
				"selectedIconPath": "static/images/user-active.png",
				"iconPath": "static/images/user.png",
				"pagePath": "pages/User/index",
				"text": "我的"
			}
		]
	},
	"globalStyle": {
		"navigationStyle": "custom",
		"navigationBarTextStyle": "white",
		"navigationBarBackgroundColor": "#ffffff",
		"backgroundColor": "#ffffff",
		"app-plus": {
			"bounce": "none",
			"scrollIndicator": "none" //全局 在APP页面都不显示滚动条
		}
	}
}