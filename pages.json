{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [
		{
			"path": "pages/Workspace/index"
		},
		{
			"path": "pages/User/index"
		},
		{
			"path": "pages/FormList/index"
		},
		{
			"path": "pages/FormPage/index"
		},
		{
			"path": "pages/Login/index"
		},
		{
			"path": "pages/ViewPage/index"
		},
		{
			"path": "components/common/SubFormPage/index"
		}
	],
	"tabBar": {
		"selectedColor": "#00C56E",
		"list": [
			{
				"selectedIconPath": "static/images/workspace-active.png",
				"iconPath": "static/images/workspace.png",
				"pagePath": "pages/Workspace/index",
				"text": "工作台"
			},
			{
				"selectedIconPath": "static/images/user-active.png",
				"iconPath": "static/images/user.png",
				"pagePath": "pages/User/index",
				"text": "我的"
			}
		]
	},
	"globalStyle": {
		"navigationStyle": "custom",
		"navigationBarTextStyle": "white",
		"navigationBarBackgroundColor": "#ffffff",
		"backgroundColor": "#ffffff",
		"app-plus": {
			"bounce": "none",
			"scrollIndicator": "none" //全局 在APP页面都不显示滚动条
		}
	}
}