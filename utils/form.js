/**
 * 将表单接口标准的表单数据结构生成GTForm填报数据
 * BackendFormData -> FrontFormData
 */

import formData from "../apis/formData"
import dictUtils from '@/utils/dict.js'

const SUB_FORM_DATA_KEY = "_mis_sub_items"

const toFrontFormData = function(data) {
	let obj = {}
	Object.keys(data).forEach(key => {
		if (key == SUB_FORM_DATA_KEY) {
			data[SUB_FORM_DATA_KEY].forEach(k => {
				obj[k.form] = {
					form: k.form,
					data: k.data.map(item => {
						return toFrontFormData(item)
					})
				}
			})
		} else {
			obj[key] = data[key]
		}
	})
	return obj
}
/**
 * 校验表单数据
 * 校验表单数据是否与表单字典表定义一致
 * @method data
 * @param {Object} data 需要校验的数据
 * @param {Object} formDef 表单定义，主要用layout与dicts
 * @return  {Object}  返回校验处理后的数据
 */
const checkFormData = async (data, formDef) => {
	try {
		const { layout, dicts } = formDef;

		// 遍历数据键值对，进行验证
		for (const key of Object.keys(data)) {
			const layoutItem = findLayoutItem(layout, key);
			// 如果存在字典表选择，则处理
			if (layoutItem && layoutItem.options.dictRef) {
				const dictDef = dicts.find(item => item.name === layoutItem.options.dictRef)
				const filter = layoutItem.options.filter || null;
				const result = await valueDictValidator(dictDef, data, data[key], filter);
				data[key] = result;
			}
		}

		return data;
	} catch (err) {
		console.error("Error checking form data:", err);
		throw err; // 重新抛出错误，以便调用者可以处理
	}
}

// 查找key是否在layout中存在
const findLayoutItem = (layout, key) => {
	let obj = null
	layout.forEach(item => {
		if (item.options.field === key) {
			obj = item
		} else if (item.options.children) {
			let info = findLayoutItem(item.options.children, key)
			if (info) obj = info
		}
	})
	return obj
}

// 值是否在字典表范围内判断
const valueDictValidator = async (dict, data, value, filter) => {
	// 获取字典数据
	// 判断是否需要过滤值
	let filterParams = null
	if (filter && filter[0] === "=") {
		// 解构过滤条件
		const [operator, filterField, focusField] = filter;
		const filterValue = data[focusField]; // 从数据中获取依赖的字段值
		filterParams = [operator, filterField, filterValue]
	}
	const dictData = await dictUtils.parse(dict, filterParams)

	// 在字典数据查找值是否存在并且过滤掉无效值
	if (Array.isArray(value)) {
		return value.filter(item => dictData.some(dictItem => dictItem.value === item));
	} else {
		const foundItem = dictData.find(item => item.value === value);
		return foundItem ? foundItem.value : undefined;
	}
}


const toApiFormData = function(formData) {
	if (!formData) return
	let apiData = {}
	let subForms = []
	Object.keys(formData).forEach((field) => {
		const value = formData[field]
		if (isSubForm(value)) {
			// 对表单数据进行递归处理
			value.data = value.data.map((v) => {
				return toApiFormData(v)
			})
			subForms.push(value)
		} else {
			apiData[field] = value
		}
	})
	if (subForms.length > 0) {
		apiData[SUB_FORM_DATA_KEY] = subForms
	}
	return apiData
}

function isSubForm(obj) {
	return (
		obj &&
		typeof obj === "object" &&
		!Array.isArray(obj) &&
		obj.form &&
		obj.data &&
		Array.isArray(obj.data)
	)
}

export default {
	toFrontFormData,
	toApiFormData,
	checkFormData
}