import formData from "@/apis/formData";

const DICT_TYPES = {
  ENUM: "enum",
  INLINE: "inline",
  TABLE: "table",
};

const parse = async (dictDef, filter) => {
  if (!dictDef || !dictDef.type) return;
  switch (dictDef.type) {
    case DICT_TYPES.ENUM:
      return parseEnumDict(dictDef);
    case DICT_TYPES.INLINE:
      return parseInlineDict(dictDef);
    case DICT_TYPES.TABLE:
      return await parseTableDict(dictDef, filter);
  }
};

/**
 * 解析enum类型字典
 * @param {*} dictDef
 * @returns
 */
const parseEnumDict = (dictDef) => {
  if (!dictDef.items || !Array.isArray(dictDef.items)) return;
  return dictDef.items.map((item) => {
    return {
      label: item,
      value: item,
    };
  });
};

/**
 * 解析inline类型字典
 * @param {*} dictDef
 * @returns
 */
const parseInlineDict = (dictDef) => {
  if (!dictDef.items || !Array.isArray(dictDef.items)) return;
  return dictDef.items;
};

/**
 * 解析table类型字典
 * @param {*} dictDef
 */
const parseTableDict = async (dictDef, theFilter) => {
  const { name, labelField, valueField, fields, filter } = dictDef;
  if (!name || !labelField || !valueField) return;
  if (fields && !Array.isArray(fields)) return;
  // 获取outFields参数
  let outFields = !fields ? [labelField, valueField] : fields;
  outFields = Array.from(new Set(outFields));
	// 组合过滤条件
	let filters = ['and']
	if(filter) filters.push(filter)
	if(theFilter) filters.push(theFilter)
	if(filters.length === 1) filters = undefined
	if(filters && filters.length === 2) filters = filters[1]
  // 获取字典数据
	const resolveSubItems = false
  const result = await formData.getFormRecords(name, { 
		outFields, 
		filter: filters, 
		resolveSubItems 
	});
  const list = result.list;
  if (!list || !Array.isArray(list)) return;
  // 组装字典
  return list.map((item) => {
    return {
      label: item[labelField],
      value: item[valueField],
      data: item,
    };
  });
};

export default{ parse };
