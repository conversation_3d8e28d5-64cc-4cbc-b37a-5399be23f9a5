function getLocation() {
  return new Promise((resolve, reject) => {
    // setTimeout(() => {
    //   resolve({
    //     longitude: 90,
    //     latitude: 27,
    //   });
    // });
      uni.getLocation({
        // 取wgs84坐标速度快，gcj02坐标速度慢
        type: "wgs84",
        success: (res) => {
          console.log(res);
          resolve(res);
        },
        fail: (e) => {
          console.log(e);
          reject(e);
        },
      });
  });
}

export default { getLocation };
