<template>
  <view class="gt-form-wrapper">
    <u-form
      ref="form"
      :model="formData"
      labelWidth="auto"
      :labelPosition="labelPosition">
      <view
        v-for="(component, index) in layout"
        :key="index">
      	<template v-if="component.options && component.options.label">
      		<component
      		  :is="component.type"
      		  :options="component.options"
      			v-if="component.options.visible !== false"
      		/>
      	</template>
        <template v-else>
        	<component
        	  :is="component.type"
        	  :options="component.options"
        		v-show="component.options.visible !== false"
        	/>
        </template>
      </view>
    </u-form>
  </view>
</template>

<script>

import Vue from 'vue'
import ValidatorFactory from './validator/Factory'

export default {
	
  name: 'GTForm',

  // 向子孙组件注入事件总线，实现模块内部消息通信
  provide() {
    return {
      bus: this.bus,
			parentFormData: this.parentFormData
    }
  },

  props: {
    // 参数
    options: {
      type: Object
    },
    labelPosition:{
      type:String,
			default:'top'
    }
  },

  data() {
    return {
      // 表单定义，通过MIS API获取
      formDef: null,
      // 表单数据
      formRecordData: null,
			// 父表单数据
			parentFormData: null,
      // 字典
      dicts: {},
      // 校验规则
      rules: {},
      // 布局
      layout: [],
      // 表单数据，通过MIS API获取
      formData: {},
			// 子组件引用
			fieldRefs: {},
			// 表单级时间总线
      bus: new Vue()
    }
  },

  toJSON() {},

  watch: {
    options: {
      handler() {
        if (!this.options) return
        const { formDef, formRecordData, parentFormData } = this.options

        //formDef不能为空
        this.formDef = formDef

        if (!this.formDef) return

        this.formRecordData = formRecordData
				
				this.parentFormData = parentFormData

        // 表单数据初始化
        if (this.formRecordData) {
          Object.keys(this.formRecordData).forEach(field => {
            this.$set(this.formData, field, this.formRecordData[field])
          })
        }
        // 获取字段
        this.fields = this.$utils.array.toMap(this.formDef?.fields, 'name')

        // 获取字典
        this.dicts = this.$utils.array.toMap(this.formDef?.dicts, 'name')
        // 生成校验规则
        this.rules = this.genRules()
				this.$nextTick(() => {
					// 移动端必须setRules
					this.$refs.form.setRules(this.rules)
				})
        // 生成表单级校验器
        this.subFormRules = this.genSubFormRules()

        // 获取布局
        this.layout = this.genLayout()
      },
      immediate: true,
      // deep:true
    }
  },
	
	created(){
		// 监听子组件初始化完成事件
		this.bus.$on(this.$events.form.FIELD_INITIALIZED, this.fieldInitHandler)
	},

  mounted() {
		// 监听表单数据变化事件
    this.bus.$on(this.$events.form.FIELD_CHANGE, this.fieldChangeHandler)
		// 派发完成初始化事件
		this.$emit(this.$events.form.INITIALIZED, this.formData)
					this.$refs.form.setRules(this.rules)
  },

  methods: {
    toJSON() {},
    // 生成校验规则
    genRules() {
      let rules = {}
      // 字段校验器
      const fieldValidators = this.formDef?.validators?.fieldValidators
      if (fieldValidators && Array.isArray(fieldValidators)) {
        fieldValidators.forEach(({ field, label, validators }) => {
          rules[field] = this._genRules(label, validators)
        })
      }
      // 子表单校验器
      const subFormValidators = this.formDef?.validators?.subFormValidators
      if (subFormValidators && Array.isArray(subFormValidators)) {
        subFormValidators.forEach(({ field, label, validators }) => {
          rules[field] = this._genRules(label, validators)
        })
      }
      return rules
    },

    _genRules(fieldLabel, validators) {
      let rules = []
      if (validators && Array.isArray(validators)) {
        validators.forEach(validator => {
          const vd = ValidatorFactory.createValidator(
            fieldLabel,
            validator,
            this.formData
          )
          if (vd) {
            rules.push(vd.getRule())
          }
        })
      }
      return rules
    },

    // 表单级校验器
    genSubFormRules() {
      const validators = this.formDef?.validators?.formValidators
      if (!validators || !Array.isArray(validators)) return
      let vds = []
      validators.forEach(validator => {
        const vd = ValidatorFactory.createValidator(
          '表单数据',
          validator,
          this.formData
        )
        if (vd) {
          vds.push(vd.getRule())
        }
      })
      return vds
    },

    // 生成布局，在表单定义中layout的基础上，补齐相应的内容，如字典
    genLayout() {
      let layout = this.formDef.layout
      if (!layout || !Array.isArray(layout)) return []
      layout = this.$utils.object.deepClone(layout)
      layout.forEach(component => {
        this._handleComponent(component)
      })
      return layout
    },

    _handleComponent(component) {
      const options = component.options ? component.options : component
      // 处理字典
      if (options.dictRef) {
        options.dict = this.dicts[options.dictRef]
      }
      // 设置只读
      if (this.options.readonly) {
        options.readonly = true
      }
      // 添加formData，有label属性被认为是字段组件
      if (options.label || options.depends) {
        options.formData = this.formData
      }
      if (options.children && Array.isArray(options.children)) {
        options.children.forEach(comp => {
          this._handleComponent(comp)
        })
      }
    },

		getFormData(){
			return this.formData
		},

    /**
     * 该方法由父组件调用，对表单进行校验并提交表单数据
     */
    async submit() {
			let subFormValid1 = await this.submitSubForms()
			let subFormValid2 = this.validateSubForms()
      // 校验
			try{
				const valid = await this.$refs.form.validate()
				const result = valid && subFormValid1 && subFormValid2
				if(result){
					// 提交表单
					this.$emit(this.$events.form.SUBMIT, this.formData)
				}
				return result
      }catch(errors){
				uni.$u.toast(errors[0].message)
			}
      return false
    },

    validateSubForms() {
      if (!this.subFormRules || this.subFormRules.length === 0) return true
      for (let rule of this.subFormRules) {
        if (!rule.validator()) {
          uni.$u.toast(rule.message)
          return false
        }
      }
      return true
    },

    fieldChangeHandler({ field, value }) {
			// 为字段赋值
      this.$set(this.formData, field, value)
			// 派发字段值变化事件
      this.$emit(this.$events.form.FIELD_CHANGE, {
				field,
				value,
				formData: this.formData
			})
    },
		
		fieldInitHandler({ field, fieldRef }) {
			if(!field || !fieldRef) return
			this.fieldRefs[field] = fieldRef
		},
		
		// 对外提供接口，用于定制化需求对字段组件赋值
		setFieldValue(field, value) {
			const targetField = this.fieldRefs[field];
			if (!targetField) return;
			targetField.setValue(value, false);
		},
		
		/**
		 * 支持子组件定制的接口，子组件按需实现submit接口，将子组件数据写入formData
		 */
		async submitSubForms(){
			let validList = []
			const fields = Object.keys(this.fieldRefs)
			for(let field of fields){
				const submitFun = this.fieldRefs[field].submit
				if(submitFun){
					validList.push(await submitFun())
				}
			}
			return validList.every(v => v)
		}

  }
}
</script>

<style scoped lang="less">
.gt-form-wrapper {
  /deep/.ant-form-explain {
    margin-top: -2px;
  }
}
</style>
