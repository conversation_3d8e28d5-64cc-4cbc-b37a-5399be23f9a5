import EqualsComparator from "./Equals";
import UnEqualsComparator from "./UnEquals";
import ContainsComparator from "./Contains";
import InComparator from "./In";
import OutComparator from "./Out";
import AnyComparator from "./Any";
import AllComparator from "./All";
import ContainsAnyComparator from "./ContainsAny"

const COMPARATOR_TYPES = {
  EQUALS: "==",
  UNEQUALS: "!=",
  CONTAINS: "contains",
  IN: "in",
  OUT: "out",
  All: "all",
  ANY: "any",
  CONTAINS_ANY: "contains-any",

};

const createComparator = (expression, formData, parentFormData) => {
  if (
    !expression ||
    !Array.isArray(expression) ||
    expression.length < 3 ||
    !formData
  )
    return;
  const type = expression[0];

  switch (type) {
    case COMPARATOR_TYPES.EQUALS:
      return new EqualsComparator(expression, formData, parentFormData);
    case COMPARATOR_TYPES.UNEQUALS:
      return new UnEqualsComparator(expression, formData, parentFormData);
    case COMPARATOR_TYPES.CONTAINS:
      return new ContainsComparator(expression, formData, parentFormData);
    case COMPARATOR_TYPES.IN:
      return new InComparator(expression, formData, parentFormData);
    case COMPARATOR_TYPES.OUT:
      return new OutComparator(expression, formData, parentFormData);
    case COMPARATOR_TYPES.All:
      return new AllComparator(expression, formData, parentFormData);
    case COMPARATOR_TYPES.ANY:
      return new AnyComparator(expression, formData, parentFormData);
    case COMPARATOR_TYPES.CONTAINS_ANY:
      return new ContainsAnyComparator(expression, formData, parentFormData);

  }
};

export default { createComparator };
