import Calculator from "./Calculator"
export default class GetCalculator extends Calculator {
  constructor( expression, formData, fieldData ) {
    super( expression, formData, fieldData )
  }

  /**
   * 根据表达式计算
   */
  calc () {
    const key = this.expression[1];
    if (!this.fieldData || !key) return;
    if (Array.isArray(this.fieldData)) {
      return this.fieldData.map(item => item[key])
    } else {
      return this.fieldData[key];
    }
  }

  getFields () {
    if ( !this.expression || !Array.isArray( this.expression ) ) return
    return this.expression.slice(2);
  }
}