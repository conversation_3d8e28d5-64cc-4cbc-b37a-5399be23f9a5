import Calculator from "./Calculator"
export default class ConcatCalculator extends Calculator {
  constructor( expression, formData, fieldData ) {
    super( expression, formData, fieldData )
  }

  /**
   * 根据表达式计算
   */
  calc () {
    // 获取所有参与计算的字段名称
    const fields = this.expression.slice( 2 )
    // 分隔符
    const separator = this.expression[1]
    // 从formData中获取字段值
    const values = fields.map( ( v ) => {
      let match = v.match( /\{(.+?)\}/g )
      if ( match ) {
        return match[0].substr( 1, match[0].length - 2 )
      }
      if ( typeof this.formData[v] === "object" && this.formData[v]) {
        // return this.formData[v].join('')
        return this.formData[v][0]
      }
      return this.formData[v]
    } )
    // 字段值加和
    return values.join( separator )
  }

  getFields () {
    if ( !this.expression || !Array.isArray( this.expression ) ) return
    return this.expression.slice( 2 )
  }
}
