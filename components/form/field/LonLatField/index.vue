<template>
  <view class="gt-lonlat-field">
    <view class="label-wrapper">
      <text class="label-prefix" v-if="options.required">*</text>
      <text class="label">{{ options.label }}</text>
    </view>
    <view class="grid-chunk">
      <u-form-item
        :prop="options.lonField.field"
        :required="options.lonField.required"
        labelWidth="auto"
      >
        <u-input
          v-model="lon"
          placeholder="请输入经度"
          @change="changeHandler('lon')"
          :disabled="readonly || loading"
          clearable
          border="surround"
        >
        </u-input>
      </u-form-item>
      <u-form-item
        :prop="options.latField.field"
        :required="options.latField.required"
        labelWidth="auto"
      >
        <u-input
          v-model="lat"
          placeholder="请输入纬度"
          @change="changeHandler('lat')"
          :disabled="readonly || loading"
          clearable
          border="surround"
        >
        </u-input>
      </u-form-item>
      <u-button
        type="primary"
        :disabled="readonly || loading"
        @click="getMapLocation"
        class="btn-location"
        :loading="loading"
        loadingMode="circle"
      >
        <u-icon
          labelColor="#fff"
          name="map-fill"
          color="#fff"
          size="18"
          v-if="!loading"
        ></u-icon>
      </u-button>
    </view>
  </view>
</template>

<script>
	
const NUMBER_REGEX = /^(-?)(\d+)?(\.?)(\d+)?$/
import BaseField from '@/components/form/BaseField'

const LON = 'lon'
const LAT = 'lat'

export default {

  name: 'LonLatField',

  mixins: [BaseField],

  props: {
    options: {
      type: Object
    }
  },

  data() {
    return {
      lon: null,
      lat: null,
      lonValue: null,
      latValue: null,
      locationInfo: null,
      altitude: null,
      loading: false
    }
  },

  computed: {
    precision() {
      return this.options.precision
    },
    lonField() {
      return this.options.lonField.field
    },
    latField() {
      return this.options.latField.field
    }
  },

  mounted() {
    if (this.formData && this.lonField && this.latField) {
      // 如果formData有field对应的值则对组件进行初始化
      if (this.lonField in this.formData || this.latField in this.formData) {
        this.setValue(this.formData[this.lonField], LON)
        this.setValue(this.formData[this.latField], LAT)
      }
      // 否则赋空值
      else {
        this.$set(this.formData, this.lonField, undefined)
        this.$set(this.formData, this.latField, undefined)
      }
    }
    // 监听整个表单字段值变化事件
    this.bus.$on(this.$events.form.FIELD_CHANGE, this.formChangeHandler)
  },

  _invisibleHandler() {
    delete this.formData[this.lonField]
    delete this.formData[this.latField]
  },

  methods: {
    getMapLocation() {
      this.loading = true
      uni.getLocation({
        // 取wgs84坐标速度快，gcj02坐标速度慢
        type: 'wgs84',
        altitude: true,
        success: async res => {
          this.lon = this.lonValue = parseFloat(
            res.longitude.toFixed(this.precision)
          )
          this.lat = this.latValue = parseFloat(
            res.latitude.toFixed(this.precision)
          )
          if (!isNaN(res.altitude)) {
            this.altitude = parseFloat(res.altitude.toFixed(2))
          }
          // 坐标转gcj02，获取地址
          const lonlat = this.$utils.proj.wgs84togcj02(
            res.longitude,
            res.latitude
          )
          let location = await this.$apis.amap.getLocation(lonlat[0], lonlat[1])
          location.altitude = this.altitude
          this.locationInfo = location

          // TODO: 如果高程未获取，通过接口获取高程
          this.updataFormDataByLonlat(LON)
          this.updataFormDataByLonlat(LAT)
        },
        fail: () => {
          this.showError(this.$constants.MSG.LOCATION_FAIL)
        },
        complete: () => {
          this.loading = false
        }
      })
    },
    setValue(value, param) {
      if (param == LON) {
        this.lon = value
        this.lonValue = value
      }
      if (param == LAT) {
        this.lat = value
        this.latValue = value
      }
    },
    getValueByLonlat(value) {
      return isNaN(value) || value === '' ? undefined : Number(value)
    },
    changeHandler(field) {
      if (!this[field].replace) return
      // 替换非法字符
      this[field] = this[field].replace(/[^\-?\d.]/g, '').replace(/-\./g, '-')
      // 判断是否符合数值形式
      if (!NUMBER_REGEX.test(this[field])) {
        this[field] = this[field + 'Value']
      } else {
        this[field + 'Value'] = this[field]
      }
      // 小数位数不设置则不限制
      if (isNaN(this.precision)) {
        this.updataFormDataByLonlat(field)
        return
      }
      // 计算小数位数
      const digital = this[field].match(/\.(.*)/)
      if (!digital) {
        this.updataFormDataByLonlat(field)
        return
      }
      let decimals = digital[1].length
      if (decimals === 0) {
        this.updataFormDataByLonlat(field)
        return
      }
      // 小数位数超出上限则截断
      if (decimals > this.precision) {
        this.$nextTick(() => {
          this[field + 'Value'] = this[field] = Number(
            Number(this[field]).toFixed(this.precision)
          )
        })
      }
      this.updataFormDataByLonlat(field)
    },
    blurHandler(field) {
      if (NUMBER_REGEX.test(this[field + 'Value'])) {
        this[field + 'Value'] = this[field] = String(Number(this[field]))
      }
    },
    updataFormDataByLonlat(field) {
      let valueField
      if (field == 'lon') {
        valueField = this.lonField
      }
      if (field == 'lat') {
        valueField = this.latField
      }

      // 派发字段值变化事件

      this.bus.$emit(
        this.$events.form.FIELD_CHANGE,
        {
          field: valueField,
          value: this.getValueByLonlat(this[field + 'Value']),
          data: this.locationInfo
        },
      )
    }
  }
}
</script>

<style scoped lang="less">
.gt-lonlat-field {
  padding: 10px 0 0 0;

  .label-wrapper {
    display: flex;
    align-items: center;

    .label-prefix {
      position: relative;
      top: 1px;
      color: #f56c6c;
      font-size: 16px;
      padding-right: 2px;
      line-height: 14px;
    }

    .label {
      color: #303133;
      font-size: 15px;
    }
  }

  /deep/ .u-form-item__body__left {
    display: none;
  }
}
.grid-chunk {
  padding-top: 10rpx;
  display: grid;
  grid-template-columns: 3fr 3fr 1fr;
  grid-gap: 12px;
  /deep/.u-form-item__body {
    padding: 0 0 8px 0;
  }
}
.btn-location,
.gt-lonlat-field /deep/.u-button--normal {
  height: 36px;
  margin: 0;
}
</style>