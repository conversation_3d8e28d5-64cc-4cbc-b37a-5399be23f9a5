<template>
  <view class="gt-cascader-field">
    <u-form-item
      :label="label"
      :prop="field"
      :required="required"
      labelWidth="auto"
      @click="handleFocus"
    >
      <u-input
        border="surround"
        :placeholder="placeholder"
        v-model="inputValue"
        disabled
        :disabledColor="readonly ? '#f5f7fa' : '#ffffff'"
      >
        <u-icon slot="suffix" name="arrow-down"></u-icon
      ></u-input>
    </u-form-item>

    <u-picker
      ref="uPicker"
      :show="show"
      :showToolbar="true"
      :title="label"
      :columns="columns"
      keyName="label"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      @change="changeHandler"
      :defaultIndex="defaultIndex"
    ></u-picker>
  </view>
</template>

<script>
import BaseField from '@/components/form/BaseField'

export default {
  name: 'CascaderField',

  mixins: [BaseField],

  data() {
    return {
      value: undefined,
      items: [],
      show: false,
      defaultIndex: [0, 0],
      valueIndex: null,
      columns: [],
      inputValue: ''
    }
  },

  computed: {
    placeholder() {
      return `请选择${this.label}`
    },

    columnsData() {
      let columns = []
      columns.push(this.items)
      if (this.valueIndex) {
        this.valueIndex.forEach((a, i) => {
          if (i < this.valueIndex.length - 1) {
            if (columns[i] && columns[i][a] && columns[i][a].children) {
              columns.push(columns[i][a].children)
            }
          }
        })
      }
      return columns
    }
  },

  async mounted() {
    this.init()
  },

  methods: {
    handleFocus() {
      if (this.readonly) return
      this.show = true
    },

    changeHandler(e) {
      const {
        columnIndex,
        index,
        indexs,
        // 微信小程序无法将picker实例传出来，只能通过ref操作
        picker = this.$refs.uPicker
      } = e

      if (columnIndex !== this.valueIndex.length - 1) {
        indexs.forEach((item, i) => {
          if (i > columnIndex) {
            this.$set(this.valueIndex, i, 0)
            if (this.columnsData[i]) {
              picker.setColumnValues(i, this.columnsData[i])
            } else {
              picker.setColumnValues(i, [])
            }
          } else {
            this.$set(this.valueIndex, i, item)
          }
        })
      } else {
        this.valueIndex = indexs
      }
    },
    handleCancel() {
      this.show = false
    },

    handleConfirm(e) {
      this.show = false
      this.value = e.value
        .filter(item => item)
        .map(item => {
          return item.value
        })
      // this.inputValue = e.value
      //   .filter(item => item)
      //   .map(i => i.label)
      //   .join('/')
      const list = e.value.filter(item => item)
      this.inputValue = list[list.length - 1].label
      this.updateFormData()
    },

    async init() {
      this.items = await this.$utils.dict.parse(this.options.dict)
      let cont = [0]
      function recursion(list) {
        if (list && list[0].children) {
          cont.push(0)
          recursion(list[0].children)
        }
      }
      recursion(this.options.dict?.items)
      if (!this.valueIndex) {
        this.valueIndex = cont
        this.defaultIndex = cont
      }

      let columns = []
      columns.push(this.items)
      this.valueIndex.forEach((a, i) => {
        if (i < this.valueIndex.length - 1) {
          if (columns[i][a] && columns[i][a].children) {
            columns.push(columns[i][a].children)
            // this.$refs.uPicker.setColumnValues(i, columns[i][a].children)
          } else {
            columns.push([])
          }
        }
      })
      this.columns = columns
    },
    setValue(value, silently = true) {
      if (!value) {
        return
      }
      this.value = value
      let list = []
      list.push({
        index: this.options.dict.items.findIndex(i => i.value === value[0]),
        value: this.options.dict.items.find(i => i.value === value[0])
      })

      value.forEach((a, index) => {
        if (index < value.length - 1) {
          list.push({
            index: list[index].value.children.findIndex(
              i => i.value === value[index + 1]
            ),
            value: list[index].value.children.find(
              i => i.value === value[index + 1]
            )
          })
        }
      })
      let cont = 0
      function recursion(list) {
        if (list[0].children) {
          cont += 1
          recursion(list[0].children)
        }
      }
      recursion(this.options.dict.items)
      let data = list.map(item => {
        return item.index
      })
      for (let i = data.length; i <= cont; i++) {
        data.push(0)
      }
      this.valueIndex = data
      // this.inputValue = list
      //   .map(item => {
      //     return item.value.label
      //   })
      //   .join('/')
      this.inputValue = list[list.length - 1].value.label
      this.defaultIndex = data

			if(!silently) this.updateFormData()
    },
    //更新表单值
    updateFormData() {
      const value = this.getValue()
      this.formData[this.field] = this.getValue();
      let data = [];
      if (Array.isArray(value)) {
        const itemsMap = this.toMapItems(this.items);
        data = this.value.map(
          (item) => itemsMap[item]?.data || itemsMap[item]
        );
      }
			// 派发字段值变化事件
			this.bus.$emit(
				this.$events.form.FIELD_CHANGE,
				{
					field: this.field,
					value,
          data
				}
			)
    },
    toMapItems(items) {
      let itemsMap = this.$utils.array.toMap(items, "value");
      if (Array.isArray(items) && items.length > 0) {
        for (let i = 0; i < items.length; i++) {
          const { children } = items[i];
          if (children) {
            itemsMap = { ...itemsMap, ...this.toMapItems(children) };
          }
        }
      }
      return itemsMap;
    },
  }
}
</script>

<style scoped lang="scss">

	@import '@/static/styles/common.scss';

/deep/.uni-input-placeholder {
  padding: 6px 9px 10px;
}

.picker-chunk,
.picker-content {
  width: 100%;
}

/deep/ .u-input__content__subfix-icon{
	.u-icon__icon{
		color: $gt-border-color !important;
	}
}

/deep/.uni-input-input {
  padding: 0;
}
</style>
