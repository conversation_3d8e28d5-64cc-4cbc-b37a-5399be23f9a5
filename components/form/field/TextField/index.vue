<template>
  <view class="gt-text-field" v-show="!hide">
    <u-form-item
      :label="label"
      :prop="field"
      :required="required"
      labelWidth="auto"
    >
      <u--input
        v-model="value"
        :placeholder="placeholder"
        :suffixIcon="unit"
        border="surround"
        clearable
				@change="changeHandler"
        :disabled="readonly"
				suffixIconStyle="fontSize: 26rpx"
      ></u--input>
    </u-form-item>
  </view>
</template>

<script>
	
import BaseField from '@/components/form/BaseField'

export default {
	
  name: 'TextField',

  mixins: [BaseField],
	
	computed: {
		hide(){
			return this.options.hide === true
		}
	},

  methods: {
		
		setValue(value, silently = true) {
			this.value = this.genPlaceholderValue(value)
			if(!silently) this.updateFormData()
		},
		
    changeHandler() {
      this.updateFormData()
    }
  }
}
</script>

<style lang="less" scoped>
.gt-text-field {
  width: 100%;
}
</style>