<template>
	<view class="gt-date-field">
		<u-form-item :label="label" :prop="field" :required="required" labelWidth="auto" @click="focusHandler">
			<u-input border="surround" :placeholder="placeholder" v-model="textValue" disabled
				:disabledColor="readonly ? '#f5f7fa' : '#ffffff'">
				<u-icon slot="suffix" size="22" color="#ccc" name="calendar"></u-icon>
			</u-input>
		</u-form-item>
		<u-calendar mode="single" :show="show" :show-confirm="true" :color="primaryColor" @confirm="confirmHandler"
			@close="closeHandler" :minDate="0"></u-calendar>
	</view>
</template>

<script>
	import moment from 'moment'
	import BaseField from '@/components/form/BaseField'

	export default {

		name: 'DateField',

		mixins: [BaseField],

		data() {
			return {
				value: undefined,
				textValue: undefined,
				show: false,
				items: []
			}
		},

		methods: {

			focusHandler() {
				if (this.readonly) return
				this.show = true
			},

			closeHandler() {
				this.show = false
			},

			setValue(value) {
				this.value = this.genPlaceholderValue(value)
				if (this.value) {
					this.value = moment(this.value).valueOf()
					this.textValue = moment(this.value).format('YYYY-MM-DD')
				}
			},

			confirmHandler(e) {
				this.show = false
				const date = moment(e[0], 'YYYY-MM-DD')
				this.value = date.valueOf()
				this.textValue = e[0]
				this.updateFormData()
			}
		}
	}
</script>
