<template>
  <view class="gt-number-with-unit-field">
    <u-form-item
      :label="label"
      :prop="field"
      :required="required"
      labelWidth="auto"
    >
      <u-input
        v-model="inputValue"
        :placeholder="placeholder"
        @blur="blurHandler"
        @change="changeHandler"
        border="surround"
        clearable
        type="digit"
        :disabled="readonly">
				<view slot="suffix" class="unit-wrapper" @click="handleShowSheet">
				  <text class="unit-text">{{ unitValue }}</text>
				  <u-icon name="arrow-down" size="12"></u-icon>
				</view>
      </u-input>
    </u-form-item>

    <u-action-sheet
      :show="showSheet"
      :actions="items"
      :safeAreaInsetBottom="true"
      title="请选择单位"
      @close="handleClose"
      @select="unitChange"
    >
    </u-action-sheet>
  </view>
</template>

<script>
	
import BaseField from '@/components/form/BaseField'

const NUMBER_REGEX = /^(-?)(\d+)?(\.?)(\d+)?$/

export default {
	
  name: 'NumberWithUnitField',

  mixins: [BaseField],

  computed: {
    precision() {
      return this.options.field.precision
    },
    unitField() {
      return this.options.unitField.field
    },
    field() {
      return this.options.field.field
    }
  },

  data() {
    return {
      value: undefined,
      unitValue: undefined,
      items: [],
      inputValue: undefined,
      showSheet: false
    }
  },

  watch: {
    initValue: {
      handler() {
        this.value = this.inputValue = this.initValue
      },
      immediate: true
    }
  },

  mounted() {
    this.init()
  },

  methods: {
    handleShowSheet() {
      this.showSheet = true
    },
    handleClose() {
      this.showSheet = false
    },
    init() {
      if (this.options.dict.type == 'enum') {
        this.items = this.options.dict.items.map(item => {
          return {
            label: item,
            value: item,
            name: item
          }
        })
      }
      if (this.options.dict.type == 'inline') {
        this.items = this.options.dict.items
      }
      if (this.unitField in this.formData && this.formData[this.unitField]) {
        this.unitValue = this.formData[this.unitField]
      } else {
        this.unitValue = this.items[0].value
      }
      this.unitChange()
    },
    changeHandler() {
      // 替换非法字符
      // this.inputValue = this.inputValue
      //   .replace(/[^\-?\d.]/g, '')
      //   .replace(/-\./g, '-')
      // 判断是否符合数值形式
      if (!NUMBER_REGEX.test(this.inputValue)) {
        this.inputValue = this.value
      } else {
        this.value = this.inputValue
      }
      // 小数位数不设置则不限制
      if (isNaN(this.precision)) {
        this.updateFormData()
        return
      }
      // 计算小数位数
      const digital = this.inputValue.match(/\.(.*)/)
      if (!digital) {
        this.updateFormData()
        return
      }
      let decimals = digital[1].length
      if (decimals === 0) {
        this.updateFormData()
        return
      }
      // 小数位数超出上限则截断
      if (decimals > this.precision) {
        this.$nextTick(() => {
          this.value = this.inputValue = Number(
            Number(this.inputValue).toFixed(this.precision)
          )
        })
      }
      this.updateFormData()
    },

    unitChange(item) {
      if (item) this.unitValue = item.value
      // 派发字段值变化事件
      this.bus.$emit(this.$events.form.FIELD_CHANGE, {
        field: this.unitField,
        value: this.unitValue
      })
    },

    blurHandler() {
      if (this.value !== '' && NUMBER_REGEX.test(this.value)) {
        this.value = this.inputValue = String(Number(this.value))
      }
      this.updateFormData()
    },

    getValue() {
      return isNaN(this.value) || this.value === ''
        ? undefined
        : Number(this.value)
    },

    setValue(value) {
      if (isNaN(value)) return
      this.inputValue = this.value = value
    },
		
		_invisibleHandler(){
			delete this.formData[this.unitField]
			delete this.formData[this.field]
		},

    // 重置
    reset() {
      this.inputValue = this.value = undefined
    }
  }
}
</script>

<style lang="scss" scoped>
	
	@import '@/static/styles/common.scss';
	
.gt-number-with-unit-field {
  width: 100%;
	
	/deep/ .u-input{
		padding: 4px 8px !important;
	}
}

.unit-wrapper {
  display: flex;
  padding: 4px 8px;
  border: 1px solid $gt-border-color;
  border-radius: 4px;
	background-color: $gt-primary-color;
	color: $gt-primary-font-color;
	
  .unit-text {
    margin-right: 6px;
		font-size: 14px;
  }
	
	/deep/ .u-icon__icon{
		color: $gt-primary-font-color !important;
	}
}
</style>
