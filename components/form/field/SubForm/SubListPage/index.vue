<template>
  <view class="gt-sub-field">
    <cu-custom bgColor="theme-bg" :isBack="true">
      <block slot="content" class="cf">
        <text class="cf" v-if="options">{{ options.label }}列表</text>
      </block>
    </cu-custom>
    <!-- <view >  -->
    <view class="list" v-if="list.length">
      <view v-for="(item, index) in list" :key="item.uid" class="list-item">
        <!-- <ItemCard
          :title="item[titleField.name]"
          :columns="columns"
          :data="item"
          :operate="readonly ? options2 : options1"
          @operateHandle="actionClick"
        ></ItemCard> -->
      </view>
    </view>
    <!-- </view> -->

    <view class="empty-tip" v-else> <u-empty mode="list"> </u-empty></view>
    <view class="list-button" v-if="!childNum">
      <view class="button" @click="addClick">添加{{ label }}</view>
    </view>
  </view>
</template>

<script>
// import ItemCard from '@/components/common/widget/ItemCard'
import { mapActions, mapState } from 'vuex'
import _ from 'lodash'
export default {
  name: 'SubForm',
  options: { styleIsolation: 'shared' }, // 解决微信小程序样式不生效问题
  // components: { ItemCard },
  computed: {
    formValue() {
      if (this.childNum) {
        let value = _.cloneDeep(this.list).filter(item => item['res_uid'])
        if (value.length !== this.childNum) {
          return undefined
        } else {
          return {
            form: this.formUid,
            data: value.map(item => {
              return _.omit(item, ['uid', 'res_uid'])
            })
          }
        }
      }
      if (this.list.length == 0) {
        return undefined
      } else {
        let value = _.cloneDeep(this.list)
        return {
          form: this.formUid,
          data: value.map(item => {
            return _.omit(item, ['uid', 'res_uid'])
          })
        }
      }
    },
    height() {
      return `calc(100vh - ${this.CustomBar - this.StatusBar + 'px'})`
    },

    formUid() {
      return this.options?.formUid
    },

    field() {
      return this.options?.field
    },

    childNum() {
      return this.options?.childNum
    },

    relevantData() {
      return this.options?.relevantData
    },

    readonly() {
      return this.options?.readonly
    },
    label() {
      return this.options?.label
    },
    ...mapState({
      moduleData: state => state.moduleData.moduleData
    })
  },

  data() {
    return {
      // showPopup: false,
      subFormOptions: {
        formDef: null,
        formUid: this.formUid,
        formRecordData: null
      },
      formData: null,
      formDef: null,
      list: [],
      titleField: null,
      dictData: null,
      options: null,
      columns: [],
      options1: [
        {
          text: '编辑',
          name: 'edit',
          style: {
            'background-color': '#14a261',
            color: '#fff'
          }
        },
        {
          text: '删除',
          name: 'del',
          style: {
            'background-color': '#f56c6c',
            color: '#fff'
          }
        }
      ],
      options2: [
        {
          text: '查看',
          name: 'read',
          style: {
            color: '#1677FF'
          }
        }
      ]
    }
  },
  watch: {
    list() {
      console.log('list发生了改变')
    }
  },

  onLoad(option) {
    const eventChannel = this.getOpenerEventChannel()
    // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
    eventChannel.on('acceptDataFromOpenerPage', async res => {
      this.subFormOptions = res.subFormOptions
      this.list = res.list
      this.options = res.options
      this.titleField = res.titleField
      this.formData = res.formData
      this.setColumn(res.subFormOptions.formDef.table.tableFields)
    })
  },
  mounted() {},
  methods: {
    ...mapActions('moduleData', {
      setModuleData: 'SET_MODULE_DATA'
    }),
    setColumn(e) {
      e.forEach(item => {
        if (
          (item.type == 'text' || item.type == 'numeric') &&
          item.name !== this.titleField.name
        ) {
          this.columns.push({ title: item.label, dataIndex: item.name })
        }
      })
    },
    setTitleStyle(item) {
      if (this.titleField && this.titleField.name) {
        if (!item[this.titleField.name] && !item.res_uid) {
          return { color: 'rgb(192, 196, 204)' }
        }
      }
    },
    setTitleField(item, index) {
      if (!this.titleField) {
        return item.uid
      }
      if (this.titleField && this.titleField.name) {
        if (!item[this.titleField.name]) {
          if (!item.res_uid) {
            return `填写${this.label}信息`
          } else {
            return `${this.label}-${index + 1}`
          }
        }
      }
      if (this.titleField.type && this.titleField.type == 'date') {
        return this.$utils.date.format(item[this.titleField.name])
      }
      if (item[this.titleField.name]) {
        return item[this.titleField.name]
      } else {
        return false
      }
    },
    delClick(record) {
      // this.list = this.list.filter(item => {
      //   return item.uid != record.uid
      // })

      let pages = getCurrentPages()
      let prevPage = pages[pages.length - 2]
      prevPage.thisSubFormField.delClick(record)
    },
    resetClick(item) {
      let index = this.list.findIndex(item => {
        return item.uid == item.uid
      })
      this.$set(this.list, index, { uid: item.uid })
    },
    editClick(record) {
      // 将当前页面绑定到当前页面栈
      let pages = getCurrentPages()
      let thisPage = pages[pages.length - 1]
      thisPage.thisSubFormField = this

      this.editing = true
      if (this.options.inherit && this.options.inherit.length) {
        this.options.inherit.forEach(item => {
          record[item] = this.formData[item]
        })
      }
      let options = _.cloneDeep(this.subFormOptions)
      options.formRecordData = record
      // uni.navigateTo({
      //   url: `/components/form/Field/SubForm/SubFormPage/index`,
      //   success: res => {
      //     // 通过eventChannel向被打开页面传送数据
      //     res.eventChannel.emit('acceptDataFromOpenerPage', {
      //       data: options,
      //       label: this.label
      //     })
      //   }
      })
    },
    readClick(record) {
      // 将当前页面绑定到当前页面栈
      let pages = getCurrentPages()
      let thisPage = pages[pages.length - 1]
      thisPage.thisSubFormField = this

      this.editing = true
      if (this.options.inherit && this.options.inherit.length) {
        this.options.inherit.forEach(item => {
          record[item] = this.formData[item]
        })
      }
      this.subFormOptions.formRecordData = record
      this.subFormOptions.readonly = true
      // uni.navigateTo({
      //   url: `/components/form/Field/SubForm/SubFormPage/index`,
      //   success: res => {
      //     // 通过eventChannel向被打开页面传送数据
      //     res.eventChannel.emit('acceptDataFromOpenerPage', {
      //       data: this.subFormOptions,
      //       label: this.label
      //     })
      //   }
      // })
    },

    actionClick(e, item) {
      if (e == 'edit') {
        this.editClick(item)
      }
      if (e == 'del') {
        this.delClick(item)
      }
      if (e == 'read') {
        this.readClick(item)
      }
    },

    addClick() {
      // 将当前组件绑定到当前页面栈
      let pages = getCurrentPages()
      let thisPage = pages[pages.length - 1]
      thisPage.thisSubFormField = this

      if (this.options.inherit && this.options.inherit.length) {
        let obj = {}
        this.options.inherit.forEach(item => {
          obj[item] = this.formData[item]
        })
        this.subFormOptions.formRecordData = obj
      }
      // uni.navigateTo({
      //   url: `/components/form/Field/SubForm/SubFormPage/index`,
      //   success: res => {
      //     // 通过eventChannel向被打开页面传送数据
      //     res.eventChannel.emit('acceptDataFromOpenerPage', {
      //       data: this.subFormOptions,
      //       label: this.label
      //     })
      //   }
      // })
    },

    submitSubData() {
      this.$refs.gtForm.submit()
    },

    closePopup() {
      this.showPopup = false
    },

    submit(e) {
      this.editing = false
      this.subFormOptions.formRecordData = null

      let pages = getCurrentPages()
      let prevPage = pages[pages.length - 3]
      prevPage.thisSubFormField.submit(e)

      this.list = this.list
    }
  }
}
</script>

<style lang="less" scoped>
.gt-sub-field {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #fff;
  .list {
    flex: 1;
    padding: 30rpx;
    padding-bottom: 100rpx;
  }
  .list-item{
    margin-bottom: 24rpx;
  }
  .empty-tip {
    background-color: #fff;
    padding-top: 100px;
  }
}
.list-button {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  position: fixed;
  z-index: 999;
  bottom: 30rpx;
  padding: 0 30rpx;
  // background-color: #fcfcfc;
  box-shadow: none;
  .button {
    width: 100%;
    background-color: #14a261;
    text-align: center;
    line-height: 74rpx;
    border-radius: 20rpx;
    color: #fff;
    height: 74rpx;
  }
}
.btn-list.one {
  grid-template-columns: 1fr;
  grid-gap: 0;
}
</style>
