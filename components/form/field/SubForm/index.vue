<template>
  <view class="gt-subform-field">
    <u-form-item
      :label="label"
      :prop="field"
      :required="required"
      labelWidth="auto"
    >
      <u-cell-group v-if="list.length">
        <u-cell
          v-for="(item, index) in list"
          :title="setTitleField(item, index)"
          :titleStyle="setTitleStyle(item)"
          :key="item.uid"
        >
          <template v-if="readonly">
            <a
              class="operate"
              slot="value"
              @click.stop="editClick(item)"
              :style="{ color: $constants.COLOR.PRIMARY_COLOR }"
            >
              查看
            </a>
          </template>
          <template v-else>
            <a class="operate" slot="value" @click.stop="editClick(item)">
              <u-icon
                name="edit-pen-fill"
                :color="$constants.COLOR.PRIMARY_COLOR"
                size="20"
              ></u-icon>
            </a>
            <a
              v-if="!childNum && (minChildNum ? index + 1 > minChildNum : true)"
              class="operate"
              slot="value"
              @click.stop="delClick(item)"
            >
              <u-icon
                name="trash-fill"
                :color="$constants.COLOR.ERROR_COLOR"
                size="20"
              ></u-icon>
            </a>
          </template>
        </u-cell>
      </u-cell-group>
    </u-form-item>

    <view
      v-if="showAddBtn"
      class="list-button"
      @click="addClick"
      >+ 添加{{ options.label }}</view
    >
    <!-- <view class="list-button list-page-btn" v-if="listpage" @click="btnClick"
      >填写{{ label }}{{ list.length ? `（${list.length}）` : '' }}</view
    > -->
    <u-modal
      :show="modalShow"
      :title="modal.title"
      :content="modal.content"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="modalCancel"
      @confirm="modalConfirm"
    ></u-modal>
  </view>
</template>

<script>

import BaseField from '@/components/form/BaseField'
import Storage from '@/tools/Storage'
import Share from '@/components/form/GTForm/share/Share'

const _SUBFORM_RECORD_INDEX_ID_ = '_subform_record_index_id_'
import _ from 'lodash'
export default {
  name: 'SubForm',
  mixins: [BaseField],
  options: { styleIsolation: 'shared' }, // 解决微信小程序样式不生效问题
  computed: {
    showAddBtn() {
      if (this.childNum) return false
      if (this.maxChildNum && this.list.length >= this.maxChildNum) return false
      return true
    },

    height() {
      return `calc(100vh - ${this.CustomBar - this.StatusBar + 'px'})`
    },

    formUid() {
      return this.options.formUid
    },

    field() {
      return this.options.field
    },

    childNum() {
      return this.options.size
    },

    minChildNum() {
      return this.options.min
    },

    maxChildNum() {
      return this.options.max
    },

    listpage() {
      return this.options.listpage
    }
  },

  data() {
    return {
      showPopup: false,
      subFormOptions: {
        formDef: null,
        formUid: this.formUid,
        formRecordData: null
      },
      formDef: null,
      list: [],
      titleField: null,
      dictData: null,
      modalShow: false,
      modal: {
        title: '删除确认',
        content: '是否删除该条记录？'
      },
      delItem: null
    }
  },

  mounted() {
    this.init()
    if (this.readonly) {
      this.$set(this.subFormOptions, 'readonly', true)
    }
  },

  methods: {
    setTitleStyle(item) {
      if (!item.res_uid) {
        return { color: 'rgb(192, 196, 204)' }
      }
    },
    setTitleField(item, index) {
      if (!item.res_uid) {
        return `填写${this.label}信息`
      }
      if (!this.titleField) {
        // return item.uid
        return `${this.label}-${index + 1}`
      }

      if (this.titleField && this.titleField.name) {
        if (!item[this.titleField.name]) {
          if (this.options.itemLabel) {
            return `${this.options.itemLabel}-${index + 1}`
          }
          return `${this.label}-${index + 1}`
        }
        return item[this.titleField.name]
      }
      if (this.titleField.type && this.titleField.type == 'date') {
        return this.$utils.date.format(item[this.titleField.name])
      }
      return `${this.label}-${index + 1}`
    },
    async init() {
      if (this.formData && this.field) {
        const field = this.field
        if (field in this.formData && this.formData[field]) {
          this.setValue(this.formData[field])
        } else if (this.childNum || this.minChildNum) {
          this.list = new Array(this.childNum || this.minChildNum)
            .fill()
            .map((item, index) => {
              return {
                uid: this.$utils.uid.genUID(),
                [_SUBFORM_RECORD_INDEX_ID_]: `${this.label}-${index + 1}`
              }
            })
        }
      }

      let res = await this.$apis.formDef.getFormDef(this.formUid)
      this.subFormOptions.formDef = res

      this.subFormOptions.formDef = res

      if (res.table && res.table.titleField) {
        this.titleField = res.table.titleField
      }

      this.subFormOptions.formUid = this.formUid
    },

    delClick(record) {
      this.modalShow = true
      this.delItem = record
    },
    modalConfirm() {
      let record = this.delItem
      let index = this.list.findIndex(item => {
        return item.uid == record.uid
      })
      this.list.splice(index, 1)
      this.updateFormData()

      this.delItem = null
      this.modalShow = false
    },
    modalCancel() {
      this.delItem = null
      this.modalShow = false
    },
    resetClick(record) {
      let index = this.list.findIndex(item => {
        return record.uid == item.uid
      })
      this.$set(this.list, index, { uid: record.uid })
    },
    editClick(record) {
      // 将当前页面绑定到当前页面栈
      let pages = getCurrentPages()
      let thisPage = pages[pages.length - 1]
      thisPage.thisSubFormField = this
      if (this.options.inherit && this.options.inherit.length) {
        this.options.inherit.forEach(item => {
          record[item] = this.formData[item]
        })
      }
      this.subFormOptions.formRecordData = record
      Storage.saveFormData(record.uid, record)
      // 保存父表单数据
      Storage.saveParentFormData(this.formData)
      const readonly = this.readonly ? 1 : 0
      const pageUrl = `${this.$constants.PAGE.SUBFORM_PAGE_URL}?formUid=${this.formUid}&formRecordUid=${record.uid}&label=${this.label}&readonly=${readonly}`

      this.navigateTo(pageUrl)
    },

    addClick() {
      // 将当前组件绑定到当前页面栈
      let pages = getCurrentPages()
      let thisPage = pages[pages.length - 1]
      thisPage.thisSubFormField = this
      if (this.options.inherit && this.options.inherit.length) {
        let obj = {}
        this.options.inherit.forEach(item => {
          obj[item] = this.formData[item]
        })
        this.subFormOptions.formRecordData = obj
        // 保存父表单数据
        Storage.saveParentFormData(this.formData)
      }
      const pageUrl = `${this.$constants.PAGE.SUBFORM_PAGE_URL}?formUid=${this.formUid}&label=${this.label}`
      this.navigateTo(pageUrl)
    },

    closePopup() {
      this.showPopup = false
    },

    submitData(e) {
      let data = _.cloneDeep(e)
      data.res_uid = this.$utils.uid.genUID()
      if (data.uid) {
        let index = this.list.findIndex(item => {
          return item.uid == data.uid
        })
        this.$set(this.list, index, data)
      } else {
        data.uid = this.$utils.uid.genUID()
        this.list.push(data)
      }
      this.showPopup = false
      this.subFormOptions.formRecordData = null

      this.updateFormData()
    },
    setValue(value) {
      if (!value) return
      if (this.childNum) {
        this.list = new Array(this.childNum).fill().map((item, index) => {
          if (value.data[index]) {
            let listItem = value.data[index]
            listItem.uid = this.$utils.uid.genUID()
            listItem.res_uid = this.$utils.uid.genUID()
            return listItem
          } else {
            return { uid: this.$utils.uid.genUID() }
          }
        })
      } else {
        this.list =
          value.data.map(item => {
            item.res_uid = this.$utils.uid.genUID()
            if (item.uid) return item
            item.uid = this.$utils.uid.genUID()
            return item
          }) || []
      }
			//
			this.updateFormData()
    },
		
		getValue() {
		  if (this.list.length == 0) {
		    return undefined
		  } else {
		    let value = _.cloneDeep(this.list)
		    return {
		      form: this.formUid,
		      data: value
		        .filter(i => {
		          return i.res_uid
		        })
		        .map(item => {
		          return _.omit(item, ['uid', 'res_uid'])
		        })
		    }
		  }
		},
		
		writeShareData(){
			if(!this.share) return
			const share = new Share(this.share)
			if(share){
				const value = this.getValue()
				share.write(value?.data)
			}
		},
		
    // 跳转页面
    navigateTo(url) {
      uni.navigateTo({
        url,
        success(e) {
          console.log(e)
        },
        fail(e) {
          console.log(e)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/static/styles/common.scss';

.gt-subform-field {
  padding: 10px 0;

  .u-slot-value {
    line-height: 17px;
    text-align: center;
    font-size: 10px;
    padding: 0 5px;
    height: 17px;
    color: #ffffff;
    border-radius: 100px;
    background-color: #f56c6c;
  }
  .operate {
    margin-left: 40rpx;
  }
  .sub-content {
    overflow-y: scroll;
    width: 100vw;
    box-sizing: border-box;
    padding: 0 15px;
    padding-top: 24rpx;
    padding-bottom: 30rpx;
  }
  .list-button {
    box-sizing: border-box;
    width: 100%;
    height: 38px;
    color: #ffffff;
    border-radius: 4px;
    background-color: $gt-primary-color;
    text-align: center;
    line-height: 36px;
  }
  .list-page-btn {
    font-size: 30rpx;
    color: #bbbbbb;
  }
  .btn-list {
    margin: 20px 20px;
    .btn {
      margin-bottom: 20px;
    }
  }
}
/deep/.u-popup__content__close {
  top: 24rpx !important;
  right: 50rpx !important;
}
/deep/ .gt-upload-field .u-form-item__body {
  flex-direction: column-reverse !important;
  .u-form-item__body__left {
    width: 160rpx !important;
    margin: 0 !important;
    .u-form-item__body__left__content__label {
      justify-content: center !important;
    }
  }
  .u-upload__button {
    width: 160rpx !important;
    height: 160rpx !important;
    margin: 0;
    margin-bottom: 16rpx;
  }
}

.btn-list {
  box-sizing: border-box;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 40rpx;
  /deep/.u-button--normal {
    margin: 20rpx 0;
    margin-bottom: 20px;
  }
}
.popup-wrapper {
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: #fff;
  z-index: 109088888;
  .popup-title {
    width: 100%;
    line-height: 45px;
    font-size: 32rpx;
    height: 45px;
    box-sizing: border-box;
    color: #fff;
    text-align: center;
    background-color: #14a261;
  }
  /deep/.uicon-close {
    color: #fff !important;
  }
}
.btn-list.one {
  grid-template-columns: 1fr;
  grid-gap: 0;
}
</style>
