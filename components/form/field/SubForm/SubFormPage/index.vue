<template>
  <view class="gt-sub-page">
    <u-navbar
      :title="label"
      placeholder
      autoBack
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
    ></u-navbar>
    <view class="sub-content">
      <GTForm
        v-if="subFormOptions"
        ref="gtForm"
        :options="subFormOptions"
        @submit="submit"
      />
    </view>
    <view class="btn-list" v-if="subFormOptions&&subFormOptions.readonly">
      <u-button
        class="btn cancel-btn"
        text="返回"
        type="primary"
        @click="closePopup"
      ></u-button>
    </view>
    <view class="btn-list" v-else>
      <u-button type="primary" color="#ccc" @click="closePopup">取消</u-button>
      <u-button type="primary" @click="submitSubData">确定</u-button>
      </u-button>
    </view>
  </view>
</template>

<script>
import _ from 'lodash'
import GTForm from '@/components/form/GTForm/index'
import Storage from '@/tools/Storage'

export default {
  name: 'SubForm',
  components: { GTForm },
  computed: {},

  data() {
    return {
      formUid: null,
      showPopup: false,
      subFormOptions: undefined,
      formDef: null,
      list: [],
      titleField: null,
      dictData: null,
      label: 'xxx',
      readonly: null
    }
  },
  onLoad(option) {
    this.formUid = option.formUid
    this.formRecordUid = option.formRecordUid
    this.label = option.label 
    this.readonly = option.readonly && (option.readonly == 1 ? true : false)
    this.initForm()
  },

  methods: {
    async initForm() {
      let options = {}
      if (this.formUid) {
        // 获取表单定义
        options.formUid = this.formUid
        options.formDef = await this.$apis.formDef.getFormDef(this.formUid)
      }
      if (this.formRecordUid) {
        // 获取表单数据
        options.formRecordUid = this.formRecordUid
        options.formRecordData = Storage.getFormData(this.formRecordUid)
				options.parentFormData = Storage.getParentFormData()
      }
      if (this.readonly) {
        options.readonly = true
      }

      this.subFormOptions = options
    },

    submitSubData() {
      this.$refs.gtForm.submit()
    },

    closePopup() {
      uni.navigateBack({
        delta: 1,
        success: res => {
          console.log(res)
        },
        file: err => {
          console.log(err)
        }
      })
    },

    submit(e) {
      let pages = getCurrentPages()
      let prevPage = pages[pages.length - 2]
      prevPage.thisSubFormField.submitData(e)

      uni.navigateBack({
        delta: 1,
        success: res => {
          console.log(res)
        },
        file: err => {
          console.log(err)
        }
      })
    },
    setValue(value) {
      if (!value) return
      this.list =
        value.data.map(item => {
          if (item.uid) return item
          item.uid = this.$utils.uid.genUID()
          item.res_uid = this.$utils.uid.genUID()
          return item
        }) || []
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/static/styles/common.scss';
/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
  }
}
.gt-sub-page {
  .sub-content {
    padding: 0 15px;
    padding-bottom: 150rpx;
  }
}
.btn-list {
  box-sizing: border-box;
  z-index: 999;
  display: flex;
  columns: 2;
  gap: 24rpx;
  padding: 24rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
}
</style>
