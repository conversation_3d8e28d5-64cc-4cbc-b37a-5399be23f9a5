<template>
  <view class="gt-number-field">
    <u-form-item :label="label" :prop="field" :required="required">
      <u-input
        v-model="inputValue"
        :placeholder="placeholder"
        @blur="blurHandler"
        @input="changeHandler"
        clearable
        :disabled="readonly"
        :suffixIcon="unit"
        type="digit"
        suffixIconStyle="fontSize: 26rpx"
      >
      </u-input>
    </u-form-item>
  </view>
</template>

<script>
	
import BaseField from '@/components/form/BaseField'

const NUMBER_REGEX = /^(-?)(\d+)?(\.?)(\d+)?$/

export default {
	
  name: 'NumberField',

  mixins: [BaseField],

  computed: {
    precision() {
      return this.options.precision
    }
  },

  created() {},

  data() {
    return {
      value: undefined,
      inputValue: undefined
    }
  },

  watch: {
    initValue: {
      handler() {
        this.value = this.inputValue = this.initValue
      },
      immediate: true
    }
  },

  methods: {
    changeHandler() {
      if (!this.inputValue || typeof this.inputValue === 'number') return
      // 替换非法字符
      this.inputValue = this.inputValue
        .replace(/[^\-?\d.]/g, '')
        .replace(/-\./g, '-')
      // 判断是否符合数值形式
      if (!NUMBER_REGEX.test(this.inputValue)) {
        this.inputValue = this.value
      } else {
        this.value = this.inputValue
      }
      // 小数位数不设置则不限制
      if (isNaN(this.precision)) {
        this.updateFormData()
        return
      }
      // 计算小数位数
      const digital = this.inputValue.match(/\.(.*)/)
      if (!digital) {
        this.updateFormData()
        return
      }
      let decimals = digital[1].length
      if (decimals === 0) {
        this.updateFormData()
        return
      }
      // 小数位数超出上限则截断
      if (decimals > this.precision) {
        this.$nextTick(() => {
          this.value = this.inputValue = Number(
            Number(this.inputValue).toFixed(this.precision)
          )
        })
      }
      this.updateFormData()
    },

    blurHandler() {
      if (this.value !== '' && NUMBER_REGEX.test(this.value)) {
        this.value = this.inputValue = String(Number(this.value))
      }
      this.updateFormData()
    },

    getValue() {
      return isNaN(this.value) || this.value === ''
        ? undefined
        : Number(this.value)
    },

    setValue(value, silently = true) {
      this.inputValue = this.value = value
      if(!silently) this.updateFormData()
    },

    // 重置
    reset() {
      this.inputValue = this.value = undefined
    }
  }
}
</script>
<style lang="less" scoped>
.gt-number-field {
  width: 100%;
}
</style>
