<template>
  <view class="gt-date-field">
    <u-form-item
      :label="label"
      :prop="field"
      :required="required"
      labelWidth="auto"
      @click="focusHandler"
    >
      <u-input
        border="surround"
        :placeholder="placeholder"
        v-model="textValue"
        disabled
        :disabledColor="readonly ? '#f5f7fa' : '#ffffff'"
      >
        <u-icon slot="suffix" size="22" color="#ccc" name="calendar"></u-icon>
      </u-input>
    </u-form-item>
    <u-datetime-picker
      v-if="pickerCreated"
      v-model="value"
      :mode="mode"
      :show="show"
      :minDate="minDate"
      :maxDate="maxDate"
      :show-confirm="true"
      :color="primaryColor"
      :confirmColor="primaryColor"
      :cancelColor="primaryColor"
      closeOnClickOverlay
      @confirm="confirmHandler"
      @cancel="closeHandler"
      @close="closeHandler"
    ></u-datetime-picker>
  </view>
</template>

<script>

import moment from 'moment'
import BaseField from '@/components/form/BaseField'

const MODE_LIST = ['date', 'datetime', 'year-month']
const DEFAULT_MODE = 'date'
// 默认往前10年
const DEFAULT_MONTH_OFFSET = [ 3, 0 ]
// 格式
const FORMATTERS = {
	'date': 'YYYY-MM-DD',
	'datetime': 'YYYY-MM-DD HH:mm',
	'year-month': 'YYYY-MM'
}

export default {

	name: 'DateTimeField',

	mixins: [BaseField],
	props:{
		options:{
			type:Object,
			default:{}
		}
	},

	data() {
		return {
			value: Number(new Date()),
			textValue: undefined,
			show: false,
			pickerCreated: false
		}
	},

	computed: {

		mode(){
			const mode = this.options.mode
			if(!mode) return DEFAULT_MODE
			return MODE_LIST.indexOf(mode) >= 0 ? mode : DEFAULT_MODE
		},

		monthOffset(){
			let monthOffset = this.options.monthOffset
			if(!monthOffset || !Array.isArray(monthOffset) || monthOffset.length !== 2){
				monthOffset = DEFAULT_MONTH_OFFSET
			}
			return monthOffset
		},

		minDate(){
			const offset = this.monthOffset[0]
			return moment(new Date()).add(-offset, 'months').valueOf()
		},

		maxDate(){
			const offset = this.monthOffset[1]
			return moment(new Date()).add(offset, 'months').valueOf()
		}
	},

	methods: {

		focusHandler() {
			if (this.readonly) return
			this.show = true
			this.pickerCreated = true
		},

		closeHandler() {
			this.show = false
		},

		setValue(value, silently=true) {
			this.value = this.genPlaceholderValue(value)
			if (this.value) {
				this.value = moment(this.value).valueOf()
				this.textValue = moment(this.value).format(FORMATTERS[this.mode])
				if(!silently) this.updateFormData()
			}
		},

		confirmHandler(e) {
			this.show = false
			this.value = e.value
			this.textValue = moment(e.value).format(FORMATTERS[e.mode])
			this.updateFormData()
		}
	}
}
</script>
