import ComparatorFactory from "./GTForm/comparator/Factory";
import CalculatorFactory from "./GTForm/calculator/Factory";
import Share from "./GTForm/share/Share";

export default {
	// 注入表单级EventBus
	inject: ["bus", "parentFormData"],

	props: {
		options: {
			type: Object,
		},
	},
	data() {
		return {
			value: undefined,
			customUnit: undefined,
			visible: undefined,
			shareDataPool: {},
		};
	},

	computed: {
		// 是否必填
		required() {
			return this.options.required === true;
		},

		// 字段名称
		field() {
			return this.options.field;
		},

		// 标注
		label() {
			return this.options.label;
		},

		// 单位
		unit() {
			if (!this.options.unit) return;
			// return this.customUnit || this.options.unit;
			const match = this.options.unit.match(/\{(.+?)\}/g);
			// 如果没有匹配{xxx}则返回原始内容作为标题
			if (match) {
				const field = `${match[0]}`.substr(1, match[0].length - 2);
				return this.formData[field] ?
					this.options.unit.replace(match[0], this.formData[field]) :
					"";
			}
			return this.customUnit || this.options.unit;
		},

		// 填报占位符
		placeholder() {
			return this.options.placeholder || `请输入${this.options.label}`;
		},

		// 是否只读
		readonly() {
			return this.options.readonly;
		},

		// 依赖
		depends() {
			return this.options.depends;
		},

		// 计算器
		calculator() {
			return this.options.calculator;
		},
		// 标题帮助
		help() {
			return this.options.help;
		},
		// 是否隐藏组件
		hide() {
			return !!this.options.hide;
		},
		// 表单数据
		formData() {
			return this.options.formData;
		},
		// 默认值
		default () {
			return this.options.default;
		},
		// 字典
		dict() {
			return this.options.dict;
		},
		share() {
			return this.options.share;
		},
	},

	mounted() {
		if (this.depends) {
			this.updateVisibility();
		}
		if (this.formData && this.field) {
			// 如果formData有field对应的值则对组件进行初始化
			if (this.field in this.formData) {
				this.setValue(this.formData[this.field]);
			} else if (this.default) {
				const defaultValue = this.genPlaceholderValue(this.default);
				this.setValue(defaultValue);
				this.$set(this.formData, this.field, defaultValue);
			} else {
				// 否则赋空值
				this.$set(this.formData, this.field, undefined);
			}
		}
		// 监听整个表单字段值变化事件
		this.bus.$on(this.$events.form.FIELD_CHANGE, this.formChangeHandler);
		// 派发已完成初始化的事件
		this.bus.$emit(this.$events.form.FIELD_INITIALIZED, {
			field: this.field,
			fieldRef: this,
		});
		// 处理共享数据
		if (this.share) {
			this.readShareData();
			this.writeShareData();
		}
	},

	beforeDestroy() {
		// 兼容普通使用组件
		if (this.formData) {
			// delete this.formData[this.field];
			// 去除formData字段并派发更新
			this._beforeDestroy();
		}
		// 删除共享数据
		if (this.share) {
			this.clearShareData();
		}
	},

	methods: {
		_beforeDestroy() {
			const value = null;
			this.formData[this.field] = value;
			// 派发字段值变化事件
			this.bus.$emit(this.$events.form.FIELD_CHANGE, {
				field: this.field,
				value,
			});
		},
		// 更新表单值
		updateFormData() {
			const value = this.getValue();
			this.formData[this.field] = value;
			// 派发字段值变化事件
			this.bus.$emit(this.$events.form.FIELD_CHANGE, {
				field: this.field,
				value,
			});
			// 处理共享数据
			if (this.share) {
				this.writeShareData();
			}
		},

		//
		formChangeHandler(payload) {
			const { field, data } = payload;
			// 处理deponds逻辑
			if (this.depends && this.fieldInDepands(field, this.depends)) {
				this.updateVisibility();
			}

			if (this.calculator) {
				this.calcValue(field, data);
			}

			this._formChangeHandler(payload);
		},

		_formChangeHandler() {
			// 子类按需实现其他对表单数据变化的响应，比如SelectField中对数据进行过滤
		},

		// TODO: 待重构
		fieldInDepands(field, depends) {
			let res = false;
			for (let i = 0; i < depends.length; i++) {
				if (Array.isArray(depends[i])) {
					// 判断是否为多维数组
					res = this.fieldInDepands(field, depends[i]);
					if (res) return true;
				} else if (depends[i] === field) return true;
			}
			return res;
		},

		// 自动显示或隐藏
		updateVisibility() {
			const comparator = ComparatorFactory.createComparator(
				this.depends,
				this.formData,
				this.parentFormData
			);
			this.visible = comparator.compare();
			this.$set(this.options, "visible", this.visible);
		},

		// 自动计算
		calcValue(field, fieldData) {
			// 处理自动计算的逻辑
			const calc = CalculatorFactory.createCalculator(
				this.calculator,
				this.formData,
				fieldData
			);
			if (!calc) return;
			const fields = calc.getFields();
			if (!fields) return;
			// 判断field是否在计算器表达式内
			if (fields.indexOf(field) >= 0) {
				this.setValue(calc.calc(), false);
			}
		},

		// 获取值
		getValue() {
			// 不给formData赋undefined，向后端发请求时会被删除
			return this.value === undefined ? null : this.value;
		},

		// 设置值
		setValue(value, silently = true) {
			// null会被认为是有效值，在form表单中造成显示困扰，比如没有值也不显示占位符
			this.value = value === null ? undefined : value;
			if (!silently) this.updateFormData();
		},

		// 设置单位
		setUnit(unit) {
			this.customUnit = unit;
		},

		// 重置
		reset() {
			this.value = undefined;
		},
		// 增加获取最外层GTForm的方法
		getRootFormData() {
			let formData = {};
			let parent = this.$parent;
			let parentName = parent.$options.name;
			while (parentName !== "App") {
				if (parentName === "GTForm") {
					formData = parent.formData;
				}
				parent = parent.$parent;
				parentName = parent.$options.name;
			}
			return formData;
		},

		// 生成默认值
		genPlaceholderValue(placeholder) {
			let value = placeholder;
			const VALUE_PLACEHOLDER = {
				DATE_NOW: "now",
				USER_CONTACT: "contact",
				USER_NICKNAME: "nickname",
				USER_COMPANY: "company",
				USER_PNAME: "pname",
				USER_PCODE: "pcode",
				USER_CNAME: "cname",
				USER_CCODE: "ccode",
				USER_FNAME: "fname",
				USER_FCODE: "fcode",
			};
			const userInfo = JSON.parse(uni.getStorageSync('userInfo'))
			const VALUE_PLACEHOLDER_REGEXP = /\{.+?\}/g;
			if (typeof placeholder === "undefined" || placeholder === null) return;
			const items = `${placeholder}`.match(VALUE_PLACEHOLDER_REGEXP);
			if (items === null) return placeholder;
			const key = items[0].substr(1, items[0].length - 2).replace("user.", "");
			switch (key) {
				case VALUE_PLACEHOLDER.DATE_NOW:
					value = new Date().getTime();
					break;
				case VALUE_PLACEHOLDER.USER_CONTACT:
					value = userInfo.phone;
					break;
				case VALUE_PLACEHOLDER.USER_NICKNAME:
				case VALUE_PLACEHOLDER.USER_COMPANY:
				case VALUE_PLACEHOLDER.USER_PNAME:
				case VALUE_PLACEHOLDER.USER_PCODE:
				case VALUE_PLACEHOLDER.USER_CNAME:
				case VALUE_PLACEHOLDER.USER_CCODE:
				case VALUE_PLACEHOLDER.USER_FNAME:
				case VALUE_PLACEHOLDER.USER_FCODE:
					value = [null, ""].includes(userInfo[key]) ?
						undefined :
						userInfo[key];
					break;
			}
			return value;
		},
		writeShareData() {
			if (!this.share) return;
			const share = new Share(this.share);
			if (share) {
				share.write(this.getValue());
			}
		},

		readShareData() {
			if (!this.share) return;
			const share = new Share(this.share);
			if (share) {
				share.read(this.shareDataPool);
			}
		},

		clearShareData() {
			if (!this.share) return;
			const share = new Share(this.share);
			if (share) {
				share.clear();
			}
		},
	},
};