<template>
  <view class="data-wrapper">
    <view v-if="!data || data.length === 0">
      <u-empty text="没有数据" icon="/static/images/empty.png"></u-empty>
    </view>
    <view
      class="data-item-wrapper"
      v-for="(item, index) of data"
      :key="index"
      @click="clickHandler(data)"
    >
    <view class="data-item-fields">
        <view class="data-item-title">
          <view class="data-item-title-info">
            <u-icon :name="showIconType ? 'account-fill':'map-fill'" color="#00a88a" :size="'50rpx'"></u-icon>
            <view v-if="getItemKey(item)" class="data-item-title-key"
              >{{ `${getItemKey(item)}:` }}
            </view>
            <view class="data-item-title-label">
              {{ getItemKey(item) ? `${getItemLabel(item) || ""}` : `${item.市名称 || ""}${item.县名称 || getUserAdmin()|| ""}` }}</view
            >
          </view>
          <view class="data-item-title-right" v-if="navigateMap(item)">
            <u-button  @click="navigateHandler(item)" type="primary" plain>到这去</u-button>
          </view>
        </view>
        <view class="data-item-body">
          <view
            class="data-item-field"
            v-for="(field, index) of getFields(item)"
            :key="index"
          >
            <view class="field-title">{{ field.label }}：</view>
            <view
              class="field-value"
              :class="{
                'title-label-error':
                  setValue(item, field) === '已退回' &&
                  field.label === '审核状态',
                'title-label-primary':
                  setValue(item, field) !== '已退回' &&
                  field.label === '审核状态',
                'title-label-doing':
                  setValue(item, field) === '填报中' &&
                  field.label === '审核状态',
              }"
              >{{ setValue(item, field) }}</view
            >
          </view>
        </view>
      </view>
      <view class="data-item-operations">
        <view
            class="btn-wrapper"
            v-for="(operation, index) of operations"
            v-if="operationShow(operation, item)"
            :key="index"
            @click.stop="operationBtnClickHandler(operation.key, item)"
          >
          <u-button
          :iconColor="operation.type === 'error' ? '#ff0000' : $constants.COLOR.PRIMARY_COLOR"
          :class="{'error-btn': operation.type === 'error', 'primary-btn': operation.type !== 'error'}"
                :icon="operation.icon || ''"
                :type="operation.type || 'primary'"
                :text="operation.label"
                shape="circle"
                plain
              ></u-button>
          </view>
      </view>
    </view>
  </view>
</template>

<script>

const POINT_LON_FIELD = "经度";
const POINT_LAT_FIELD = "纬度";
const POINT_DATA_LON_FIELD = "地块中心经度";
const POINT_DATA_LAT_FIELD = "地块中心纬度";

import ComparatorFactory from '@/components/form/GTForm/comparator/Factory.js'
import Navigation from "@/tools/Navigation";

export default {
  props: {
    // 数据
    data: {
      type: Array,
    },
    // 显示字段
    fields: {
      type: Array,
    },
    // 操作按钮
    operations: {
      type: Array,
    },
    cache: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {};
  },
  mounted() {
    console.log(this.data)
    
  },

  computed: {
    showIconType () {
      if (this.fields.length > 0) {
        return this.fields.map(item => item.name).includes("农户姓名")  || false
      }
    }
  },

  methods: {
    operationShow(operation, item) {
      let visible = true;
      if (operation.depends && Array.isArray(operation.depends) && operation.depends.length > 0) {
      const comparator = ComparatorFactory.createComparator(operation.depends, item, null)
      visible = comparator.compare()
      }
      return visible;
    },
    setValue(item, field) {
      let value;
      if (this.cache) {
        value = item.content[field.name];
      } else {
        value = item[field.name];
      }

      switch (field.type) {
        case "text":
          return value;
        case "date":
          return this.$utils.date.format(value);
        default:
          return value;
          break;
      }
    },
    notNullFields (field) {
      if (this.cache) {
        return this.fields?.filter((f) => ![null, undefined, ""].includes(field.content[f.name])) || []
      } else {
        return this.fields?.filter((f) => ![null, undefined, ""].includes(field[f.name])) || []
      }
      
    },
    getFields(item) {
      return this.notNullFields(item).filter(f => !f.uniqueKey)
    },

    getItemKey(item) {
      const itemField = this.notNullFields(item).filter(f => f.uniqueKey)
      if (Array.isArray(itemField) && itemField.length > 0) {
        return itemField[0].label
      }
      return ""
    },
    getItemLabel(item) {
      const itemField = this.notNullFields(item).filter(f => f.uniqueKey)
      if (Array.isArray(itemField) && itemField.length > 0) {
        return this.cache ? item.content[itemField[0].name] : item[itemField[0].name]
      }
      return ""
    },

    operationBtnClickHandler(key, item) {
      this.$emit(key, item);
    },
    clickHandler(item) {
      this.$emit(this.$events.list.ITEM_CLICK, item);
    },
    getUserAdmin() {
      let userInfo = JSON.parse(uni.getStorageSync("userInfo"));
      return `${userInfo.cname}${userInfo.fname}`
    },
    navigateHandler(data) {
      let lon = data[POINT_LON_FIELD] || data[POINT_DATA_LON_FIELD];
      let lat = data[POINT_LAT_FIELD] || data[POINT_DATA_LAT_FIELD];
      if (isNaN(lon) || isNaN(lat)) return;
      Navigation.navigateTo(lon, lat);
    },
    navigateMap(data) {
      let lon = data[POINT_LON_FIELD] || data[POINT_DATA_LON_FIELD];
      let lat = data[POINT_LAT_FIELD] || data[POINT_DATA_LAT_FIELD];
      return !(isNaN(lon) || isNaN(lat));
    }
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

.data-wrapper {
  padding-bottom: 200rpx;
  .data-item-wrapper {
    margin-top: 20rpx;
    border: 1px solid $gt-border-color;
    border-radius: 16rpx;
    background-color: #fff;
    box-shadow: 0 0 4rpx 4rpx #eee;
    padding: 8rpx;

    .data-item-fields {
      .data-item-title {
        display: flex;
        flex-direction: row;
        box-sizing: border-box;
        justify-content: space-between;
        padding: 15rpx;
        width: 100%;
        height: 70rpx;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: $gt-primary-color;
        line-height: 45rpx;
        background: linear-gradient(180deg, rgba(0,168,139,0.08) 0%, rgba(0,168,139,0) 100%);
        border-radius: 16rpx;
        .data-item-title-info {
          display: flex;
          justify-content: flex-start;

          .data-item-title-key {
            padding: 0 20rpx;
            margin: auto;
            text-align: center;
          }
          .data-item-title-label {
            margin: auto;
            text-align: center;
            font-weight: bold;
            font-size: 32rpx;
            display: flex;
            max-width: 10em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .data-item-title-right {
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 28rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          line-height: 40rpx;
          margin-right: 15rpx;

          .u-button {
            width: 132rpx;
            height: 56rpx;
            border-radius: 32rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            line-height: 30rpx;
            color: $gt-primary-color !important;
            background: rgba(0, 168, 138, 0.05) !important;
          }
        }
      }
      .data-item-body {
        display: flex;
        flex-wrap: wrap;
        .data-item-field {
          display: flex;
          box-sizing: border-box;
          padding: 20rpx;
          width: 50%;
          font-size: 24rpx;

          &:nth-last-child(n + 2) {
            padding-bottom: 0;
          }
          .field-title {
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #010101;
            line-height: 40rpx;
            text-align: center;
            min-width: 3em;
            white-space: nowrap;
          }
          .field-value {
            margin: 0 10rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #010101;
            line-height: 40rpx;
            text-align: center;
          }
        }
      }
    }


    .data-item-operations {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-start;
      align-items: center;
      height: 64rpx;
      margin: 8rpx 0rpx 15rpx 0rpx;

      .btn-wrapper {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        line-height: 40rpx;
        margin-right: 20rpx;
        height: 100%;
        .u-button {
          width: 124rpx;
          height: 100%;
        }
      }
    }
  }
}

.title-label-error {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #d0021b !important;
  line-height: 40rpx;
}

.title-label-primary {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  line-height: 40rpx;
  color: $gt-primary-color !important;
}

.title-label-doing {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  line-height: 40rpx;
  color: #007cff !important;
}

.error-btn {
  color: #f00 !important
}

.primary-btn {
  color: $gt-primary-color !important;
  background: rgba(0, 168, 138, 0.05) !important;
}
</style>
