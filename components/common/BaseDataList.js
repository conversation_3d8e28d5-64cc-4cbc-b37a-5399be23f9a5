
/**
 * 数据列表基类，实现数据列表基础功能
 */

const TOAST_DURATION = 2000

export default {

	methods: {
		// 提示成功
		showSuccess(msg) {
			uni.showToast({
				title: msg,
				icon: 'success',
				duration: TOAST_DURATION
			})
		},

		// 提示失败
		showError(msg) {
			uni.showToast({
				title: msg,
				icon: 'error',
				duration: TOAST_DURATION
			})
		},

		// 显示LOADING
		showLoading(msg) {
			uni.showLoading({
				title: msg,
				mask: true
			})
		},
		
		// 隐藏LOADING
		hideLoading(){
			uni.hideLoading()
		},
		
		// 跳转页面
		navigateTo(url){
			uni.navigateTo({
				url
			})
		}
		
	}
}
