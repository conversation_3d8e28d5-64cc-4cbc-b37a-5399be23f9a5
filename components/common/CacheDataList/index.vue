<template>
  <view class="scroll-wrap">
    <scroll-view scroll-y class="scroll-content">
      <DataList
        :data="dataList"
        :fields="fields"
        :operations="operations"
        :cache="true"
        @edit="editHandler"
        @delete="deleteHandler"
        @view="viewHandler"
      >
      </DataList>
    </scroll-view>
    <u-modal
      :show="modalShow"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_DELETE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="deleteCancelHandler"
      @confirm="deleteConfirmHandler"
    >
    </u-modal>
  </view>
</template>

<script>
import BaseDataList from '@/components/common/BaseDataList'
import DataList from '@/components/common/DataList'
import Storage from '@/tools/Storage'

const OPERATIONS = [
  {
    key: 'edit',
    label: '编辑'
  },
  {
    key: 'delete',
    label: '删除',
    type: 'error'
  }
]

export default {
  mixins: [BaseDataList],

  components: {
    DataList
  },

  props: {
    formUid: {
      type: String
    },

    formPageUrl: {
      type: String
    },
    customOptions: {
      type: Object
    }
  },

  data() {
    return {
      dataList: [],
      fields: [],
      operations: OPERATIONS,
      modalShow: false,
      delItemId: undefined
    }
  },

  async mounted() {
    await this.getFormDesign()
    await this.loadData()
  },

  methods: {
    async getFormDesign() {
      let res = await this.$apis.formDef.getFormDef(this.formUid)
      this.fields = res.list.listFields
    },

    async loadData() {
      let res = await this.$apis.formDataLocal.getCacheRecords(
        this.formUid
      ) || []
      const { 任务_uid, 监测点_uid } = this.customOptions
      this.dataList = res.filter(item => 监测点_uid ? item.content.任务_uid === 任务_uid && item.content.监测点_uid === 监测点_uid : item.content.任务_uid === 任务_uid)
    },

    editHandler(data) {
      const cacheRecordId = data.id
      if (!cacheRecordId) return
      Storage.saveFormData(cacheRecordId, data.content)
      if (this.formPageUrl) {
        const pageUrl = `${this.$constants.PAGE[this.formPageUrl]}?formUid=${
          this.formUid
        }&cacheRecordId=${cacheRecordId}`
        const genUrl = this.getGenPageUrl(pageUrl)
        this.navigateTo(genUrl)
        return
      }
      const pageUrl = `${this.$constants.PAGE.FORM_PAGE_URL}?formUid=${this.formUid}&cacheRecordId=${cacheRecordId}`
      const genUrl = this.getGenPageUrl(pageUrl)
      this.navigateTo(genUrl)
    },

    deleteHandler(data) {
      this.delItemId = data.id
      if (!this.delItemId) return
      this.modalShow = true
    },

    async deleteConfirmHandler() {
      try {
        await this.$apis.formDataLocal.delCacheRecord(
          this.formUid,
          this.delItemId
        )
        await this.loadData()
      } catch (e) {
        this.showError(this.$constants.MSG.DELETE_FAIL)
      }
      this.delItemId = undefined
      this.modalShow = false
    },

    deleteCancelHandler() {
      this.delItemId = undefined
      this.modalShow = false
    },

    viewHandler(data) {
      const pageUrl = `${this.$constants.PAGE.VIEW_PAGE_URL}?formUid=${this.formUid}`
      const genUrl = this.getGenPageUrl(pageUrl)
      this.navigateTo(genUrl)
    },
    getGenPageUrl (url) {
      Object.keys(this.customOptions).forEach(item => {
        const itemUrl = `&${item}=${this.customOptions[item]}`
        url = url.concat(itemUrl)
      })
      return url
    }
  }
}
</script>

<style lang="less" scoped>
.scroll-wrap {
  height: 100%;
  .scroll-content {
    height: 100%;
  }
}
</style>
