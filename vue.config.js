const dotenv = require("dotenv");

module.exports = {
  runtimeCompiler: true,

  productionSourceMap: false,

  devServer: {
    proxy: "http://39.104.87.15/",
    // proxy: "http://39.104.24.246/",
    // proxy: "http://36.136.118.48/",
  },

  chainWebpack: (config) => {
    config.plugin("define").tap((args) => {
      const config = getEnvsByDot();
      Object.keys(config).forEach((key) => {
        if (typeof config[key] == "string") {
          config[key] = '"' + config[key] + '"';
        }
        args[0]["process.env"][key] = config[key];
      });
      return args;
    });
  },
};

function getEnvsByDot() {
  let dotEnvs = {};
  // 加载通用环境变量
  const envPath0 = __dirname + "/.env";
  const dotEnvsConfig0 = dotenv.config({ path: envPath0 });
  if (!dotEnvsConfig0.error) {
    Object.keys(dotEnvsConfig0.parsed).forEach((key) => {
      dotEnvs[key] = dotEnvsConfig0.parsed[key];
    });
  }
  // 加载专属环境变量
  let env = "test";
  console.log(dotEnvs.NODE_ENV);
  if (process.env.VUE_APP_PLATFORM === "h5") {
    env = "h5";
  } else if (dotEnvs.NODE_ENV === "test") {
    env = "test";
  } else if (dotEnvs.NODE_ENV === "production" || dotEnvs.NODE_ENV === "prod") {
    env = "prod";
  }
  const envPath = __dirname + "/.env." + env;
  const dotEnvsConfig = dotenv.config({ path: envPath });
  if (!dotEnvsConfig.error) {
    Object.keys(dotEnvsConfig.parsed).forEach((key) => {
      dotEnvs[key] = dotEnvsConfig.parsed[key];
    });
  }
  return dotEnvs;
}
