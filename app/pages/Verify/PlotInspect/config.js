export const cropNameList = [{
		name: "水稻",
	},
	{
		name: "小麦",
	},
	{
		name: "玉米",
	},
	{
		name: "花生",
	},
	{
		name: "蔬菜",
	},
	{
		name: "大豆",
	},
	{
		name: "甘蔗",
	},
	{
		name: "其他",
	},
];

export const rules = {
	crop: {
		type: "string",
		required: true,
		message: "请选择种植作物",
		trigger: ["change", "blur"],
	},
	remark: {
		type: "string",
		required: true,
		message: "请填写备注",
		trigger: ["change", "blur"],
	},
	inspector: {
		type: "string",
		required: true,
		message: "请填写核查人员",
		trigger: ["change", "blur"],
	},
};

export const MAP_CONFIG = {
	layers: [{
			label: "WQQDW",
			key: ["point-uncertain", "point-uncertain-check"],
			type: "table",
			open: true,
			filter: true,
			icon: '/static/images/point-orange.png'
		}, {
			label: "QQDW",
			key: ["point-certain", "point-certain-check"],
			type: "table",
			open: true,
			filter: true,
			icon: '/static/images/point-blue.png'
		}, {
			label: "QQDK",
			key: "dk",
			type: "table",
			open: true,
			filter: true,
			icon: '/static/images/legend1.png'
		},
		{
			label: "LBHF",
			key: "dttz",
			type: "table",
			open: true,
			filter: true,
			icon: '/static/images/legend2.png'
		},
		{
			label: "LSSCGNQ",
			key: "lscq",
			type: "table",
			open: false,
			filter: false,
			icon: '/static/images/legend3.png'
		},
		{
			label: "TLZSCBHQ",
			key: "tlcq",
			type: "table",
			open: false,
			filter: false,
			icon: '/static/images/legend4.png'
		},
		{
			label: "GBZNT",
			key: "gbznt",
			type: "table",
			open: false,
			filter: false,
			icon: '/static/images/legend5.png'
		},
	],
	point: [{
			filter: [
				{ key: '地块状态', value: "已核查" },
				{ key: '地块类型', value: "确权地块" }
			],
			point: 'point-certain-check',
			key: 'certain_check_geojson',
			icon: 'green'
		},
		{
			filter: [
				{ key: '地块状态', value: "已核查" },
				{ key: '地块类型', value: "未确权地块" }
			],
			point: 'point-uncertain-check',
			key: 'uncertain_check_geojson',
			icon: 'green'
		},
		{
			filter: [
				{ key: '地块状态', value: "未核查" },
				{ key: '地块类型', value: "确权地块" }
			],
			point: 'point-certain',
			key: 'certain_geojson',
			icon: 'blue'
		},
		{
			filter: [
				{ key: '地块状态', value: "未核查" },
				{ key: '地块类型', value: "未确权地块" }
			],
			point: 'point-uncertain',
			key: 'uncertain_geojson',
			icon: 'orange'
		},
	],
	sources: (ccode, fcode) => {
		console.log("读取配置", MAP_CONFIG.layers);
		let tileset = [{
				code: 451025,
				name: '靖西市',
				data: ['plowland_data_apply_dk_451025', 'plowland_data_apply_dk_451025_1', 'plowland_data_apply_dk_451025_2', 'plowland_data_apply_dk_451025_3']
			},
			{
				code: 450222,
				name: '柳城县',
				data: ['plowland_data_apply_dk_450222', 'plowland_data_apply_dk_450222_1', 'plowland_data_apply_dk_450222_2', 'plowland_data_apply_dk_450222_3']
			}
		]
		const sources = [
			{ id: 'gbznt', name: `plowland_data_apply_gbznt_${ccode}` },
			{ id: 'lscq', name: `plowland_data_apply_lscq_${ccode}` },
			{ id: 'tlcq', name: `plowland_data_apply_tlcq_${ccode}` },
			{ id: 'dttz', name: `plowland_data_apply_dttz_${fcode}` }
		];
		let large = tileset.find(item => Number(item.code) == Number(fcode))
		if (large) {
			large.data.forEach(item => {
				sources.push({ id: item, name: item })
			})
			MAP_CONFIG.layers.map(item => {
				if (item.label == 'QQDK') item.key = large.data
				return item
			})
		} else {
			sources.push({ id: 'dk', name: `plowland_data_apply_dk_${fcode}` })
		}
		return sources
	}
}