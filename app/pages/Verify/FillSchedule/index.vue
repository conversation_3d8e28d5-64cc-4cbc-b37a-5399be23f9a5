<template>
	<view class="content">
		<u-navbar title="填报进度" :bgColor="$constants.COLOR.PRIMARY_COLOR" :placeholder="true" @leftClick="navigateBack">
			<view slot="right" class="area-text" :style="{ fontSize: areaValue.length > 5 ? '24rpx' : '28rpx' }" @click="districtSelectShow = true">切换[{{ areaValue }}]</view>
		</u-navbar>
		<view class="content-top">
			<picker mode="selector" :range="taskList" range-key="task_name" @change="selectFilter">
				<view class="filter">
					<view class="text">{{ taskName }}</view>
					<u-icon name="arrow-down"></u-icon>
				</view>
			</picker>
			<view class="tab-list">
				<u-tabs :list="tabs" @click="changeTab" :scrollable="false" lineWidth="60" :lineColor="$constants.COLOR.PRIMARY_COLOR" :activeStyle="{
						color: $constants.COLOR.PRIMARY_COLOR,
						fontWeight: 'bold',
					}"></u-tabs>
			</view>
		</view>

		<view class="data-content">
			<scroll-view :scroll-y="true" class="scroll-content" :style="contentStyle">
				<view class="empty-wrapper" v-if="dataList.length === 0">
					<u-empty text="暂无数据" icon="/static/images/empty.png" marginTop="200"></u-empty>
				</view>
				<view class="data-list">
					<view class="data-item" v-for="(item, index) in dataList" :key="index">
						<view class="item-name">{{ item.name }}</view>
						<view class="line-container">
							<view><u-line-progress :percentage="item.percentage" :showText="false" activeColor="#1677FF" height="10"></u-line-progress></view>
							<view class="total-text">{{ item.finish }}/{{ item.total }}</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<AreaPicker v-model="districtSelectShow" @confirm="confirDistrictSelct"></AreaPicker>
	</view>
</template>

<script>
	import AreaPicker from "@/app/components/AreaPicker/index";
	const FORM_NAME = "进度汇交表";
	const TASK_FORM_NAME = "任务表";
	import _ from 'lodash'
	export default {
		components: {
			AreaPicker,
		},
		data() {
			return {
				userInfo: {},
				currentTaskId: null,
				districtSelectShow: false,
				areaValue: '',
				areaFilter: null,
				showLevel: "tname",
				dataList: [],
				originData: [],
				taskList: [],
				taskName: "",
				pointType: "全部",
				tabs: [{
						name: "全部",
					},
					{
						name: "确权地块",
					},
					{
						name: "未确权地块",
					},
				],
				needUpdate: false,
				fcode: null,
			};
		},
		computed: {
			contentStyle() {
				let system = uni.getSystemInfoSync()
				return {
					height: `${system.windowHeight-system.statusBarHeight-44-80-50}px`
				}
			},
		},
		onShow() {
			if (this.needUpdate) {
				this.getDataList();
				this.needUpdate = false;
			}
		},
		async created() {
			uni.$on("refresh-point", id => {
				this.needUpdate = true;
			});
			this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
			this.fcode = this.userInfo.fcode;
			this.areaValue = this.userInfo.fname ? this.userInfo.fname : "";
			await this.getCurrentTask();
			this.getDataList();
		},
		methods: {
			selectFilter(e) {
				let index = e.detail.value
				this.currentTaskId = this.taskList[index]._id
				this.taskName = this.taskList[index].task_name
				this.getDataList()
			},
			navigateBack() {
				uni.navigateBack()
			},
			//  行政区划切换
			confirDistrictSelct(e) {
				// 判断编码并且查找镇级中心经纬度并且移至
				this.areaValue = e.data.label;
				if (e.config.valuekey !== "fcode") {
					const level = e.config.valuekey;
					this.areaFilter = ["=", level, e.data.value];
				} else {
					this.areaFilter = null;
				}
				this.districtSelectShow = false;
				this.handleFilterData();
			},
			//  获取任务
			async getCurrentTask() {
				try {
					const result = await this.$apis.formData.getFormRecords(TASK_FORM_NAME);
					if (result.list.length == 0) {
						this.currentTaskId = null;
						return
					}
					let list = result.list.map(item => {
						item.task_name = `${item.年份}${item.时间段}${item.任务名称}`
						return item
					})
					this.taskList = list

					let task = list.find(item => item.任务状态 == '已启动')
					this.currentTaskId = task._id;
					this.taskName = task.task_name
				} catch (error) {
					this.currentTaskId = null;
				}
			},
			//	切换tab
			changeTab(tab) {
				if (tab.name == this.pointType) return;
				this.pointType = tab.name;
				this.handleFilterData();
			},
			async getDataList() {
				if (!this.currentTaskId) return;
				const params = {
					filter: ["and", ["=", "任务_id", this.currentTaskId],
						["=", "fcode", this.fcode]
					],
				};
				const { list } = await this.$apis.formData.getFormRecords(FORM_NAME, params);
				this.originData = list
				console.log("新获取的数据", this.originData);
				this.handleFilterData();
			},
			handleFilterData() {
				let data = _.cloneDeep(this.originData)
				let filterName = 'tname';
				if (this.areaFilter) {
					filterName = this.areaFilter[1] ? 'vname' : 'fname'
					data = data.filter(item => item[this.areaFilter[1]] == this.areaFilter[2])
				}
				// 分类好相应等级的数据
				if (filterName) {
					let groupedByTname = data.reduce((result, item) => {
						if (result[item[filterName]]) {
							result[item[filterName]]['确权地块'] += item['确权地块'];
							result[item[filterName]]['未确权地块'] += item['未确权地块'];
							result[item[filterName]]['确权地块完成数'] += item['确权地块完成数'];
							result[item[filterName]]['未确权地块完成数'] += item['未确权地块完成数'];
						} else {
							result[item[filterName]] = { ...item };
						}
						return result;
					}, {});
					data = Object.values(groupedByTname)
				}
				let list;
				if (this.pointType === "全部") {
					list = data.map(item => {
						const obj = {
							name: item[filterName],
							finish: item['确权地块完成数'] + item['未确权地块完成数'],
							total: item['确权地块'] + item['未确权地块'],
							percentage: 0,
						};
						obj.percentage = obj.total === 0 ? 0 : (obj.finish / obj.total) * 100;
						return obj;
					});
				} else if (this.pointType === "确权地块") {
					list = data.map(item => {
						const obj = {
							name: item[filterName],
							finish: item['确权地块完成数'],
							total: item['确权地块'],
							percentage: 0,
						};

						obj.percentage = obj.total === 0 ? 0 : (obj.finish / obj.total) * 100;
						return obj;
					});
				} else if (this.pointType === "未确权地块") {
					list = data.map(item => {
						const obj = {
							name: item[filterName],
							finish: item['未确权地块完成数'],
							total: item['未确权地块'],
							percentage: 0,
						};

						obj.percentage = obj.total === 0 ? 0 : (obj.finish / obj.total) * 100;
						return obj;
					});
				}

				this.dataList = list.sort((a, b) => b.percentage - a.percentage);
			},
		},
	};
</script>

<style lang="scss" scoped>
	@import "@/static/styles/common.scss";

	/deep/ .u-navbar__content {
		.u-navbar__content__title {
			color: $gt-navbar-title-color !important;
		}
	}

	/deep/ .uicon-arrow-left {
		color: #fff !important;
	}

	.area-text {
		color: #fff;
		display: flex;
		align-items: center;
	}

	.content {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f8f8f8;
		box-sizing: border-box;

		.content-top {
			background-color: #fff;
			height: 70px;
			padding: 10px;

			.filter {
				height: 30px;
				width: 100%;
				display: flex;
				align-items: center;

				.text {
					flex: 1;
					text-align: center;
				}

				.icon {
					width: 60rpx;
				}
			}
		}

		.data-content {
			overflow-y: hidden;
			position: relative;
			// padding: 30rpx;

			.scroll-content {
				height: 100%;
				overflow-y: scroll;
				padding: 0 30rpx;

				.data-list {
					margin-top: 30rpx;
					padding-bottom: 40rpx;

					.data-item {
						padding: 40rpx 30rpx;
						background-color: #fff;
						margin-bottom: 10rpx;
						border-radius: 12rpx;

						.item-name {
							padding: 10rpx 0;
							font-size: 32rpx;
							font-weight: bold;
						}

						.line-container {
							padding-right: 200rpx;
							position: relative;

							.total-text {
								width: 180rpx;
								font-size: 28rpx;
								white-space: nowrap;
								position: absolute;
								right: 0px;
								bottom: 0;
							}
						}
					}
				}
			}
		}
	}
</style>