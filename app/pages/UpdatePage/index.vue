<template>
	<view class="page-container">
		<view class="app">
			<image class="logo" src="/static/images/logo.png" mode="aspectFill"></image>
			<view v-if="system" class="appname">{{system.appName}}</view>
			<view v-if="system" class="version">当前版本：{{system.appVersion}}</view>
			<view v-if="info" class="version">最新版本：{{info.版本}}</view>
		</view>
		<text class="tips">您的版本过旧,请点击下载更新</text>
		<u-button type="primary" size="large" shape="circle" text="下载更新" :customStyle="buttonStyle" @click="downloadApp"></u-button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				buttonStyle: {
					width: '80vw',
					margin: '50rpx auto 0'
				},
				system: uni.getSystemInfoSync(),
				info: null,
			}
		},
		onLoad(options) {
			this.info = JSON.parse(options.data)
		},
		methods: {
			downloadApp() {
				plus.runtime.openURL(this.info.url);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.app {
		margin-bottom: 200rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.appname {
			font-size: 50rpx;
			font-weight: bold;
		}

		.logo {
			width: 180rpx;
			height: 180rpx;
			border-radius: 12rpx;
			margin-bottom: 30rpx;
		}

		.version {
			font-size: 28rpx;
			margin-top: 10rpx;
		}
	}

	.tips {
		font-size: 32rpx;
		margin-top: 30rpx;
	}
</style>