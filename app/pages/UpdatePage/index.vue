<template>
	<view>
		<u-empty mode="history" width="280" icon="http://cdn.uviewui.com/uview/empty/history.png" text="您的版本过旧,请点击下载更新" marginTop="100" textSize="20"></u-empty>
		<u-button type="primary" text="下载更新" :customStyle="buttonStyle" @click="downloadApp"></u-button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				buttonStyle: {
					width: '300rpx',
					margin: '100rpx auto 0'
				},
				info: null,
			}
		},
		onLoad(options) {
			this.info = JSON.parse(options.data)
		},
		methods: {
			downloadApp() {
				plus.runtime.openURL(this.info.url);
			},
		}
	}
</script>

<style>

</style>