<template>
  <view class="wrap">
    <u-navbar
      title="数据审核"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      autoBack
    >
    </u-navbar>
    <view class="content">
      <view class="tab-wrapper">
        <u-subsection
          class="subsection"
          :list="sectionList"
          :current="currentSection"
          @change="sectionChangeHandler"
          :activeColor="$constants.COLOR.PRIMARY_COLOR"
        ></u-subsection>
      </view>
      <view class="form-wrap" v-if="loaded">
        <view v-for="(item, index) in tabs" :key="index">
          <TabGtForm
            :formUid="item.formUid"
            :formRecordUid="item.formRecordUid"
            v-show="index == currentSection"
          />
        </view>
      </view>
      <view class="bottom-btns" v-if="type == 'audit'">
        <u-button class="btn" type="error" @click="returnHanlde">退回</u-button>
        <u-button class="btn" type="primary" @click="passHandle"
          >审核通过</u-button
        >
      </view>
    </view>
    <u-popup v-if="show" :show="show" closeable @close="show = false">
      <view class="popup-cont">
        <view class="title">退回原因</view>
        <u--textarea v-model="value1" placeholder="请输入内容"></u--textarea>
        <u-button
          :disabled="!value1"
          class="btn"
          type="primary"
          @click="returnBtnClick"
        >
          确认
        </u-button>
      </view>
    </u-popup>
  </view>
</template>

<script>
import TabGtForm from "./components/TabGtForm";
const FORMUID = "野生植物原生境保护点填报表";
export default {
  components: { TabGtForm },
  data() {
    return {
      currentSection: 0,
      tabs: [
        {
          label: "监测表（一）",
          formUid: "野生植物原生境保护点监测表一",
        },
        {
          label: "监测表（二）",
          formUid: "野生植物原生境保护点监测表二",
        },
        {
          label: "报告",
          formUid: "野生植物原生境保护点监测报告表",
        },
      ],
      type: null,
      loaded: false,
      show: false,
      value1: null,
    };
  },
  onLoad(option) {
    this.type = option.type;
    const ids = JSON.parse(option.ids);
    this.recordId = option.record_id;
    console.log(ids);
    this.tabs.forEach((e, i) => {
      this.$set(e, "formRecordUid", ids[i]);
    });
    this.loaded = true;
  },
  computed: {
    sectionList() {
      return this.tabs.map((item) => item.label);
    },
  },
  methods: {
    sectionChangeHandler(e) {
      this.currentSection = e;
    },
    returnHanlde() {
      this.show = true;
    },
    async returnBtnClick() {
      console.log(this.value1);
      try {
        let res = await this.$apis.formData.updateFormRecordState(
          FORMUID,
          this.recordId,
          {
            field: "审核状态",
            state: "已退回",
            msg: this.value1,
            attrs: {
              退回原因: this.value1,
            },
          }
        );
        console.log(res);

        if (res) {
          this.show = false;

          uni.showToast({
            title: "退回成功",
            icon: "success",
            duration: 2000,
          });
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            });
          }, 500);
        }
      } catch (error) {
        uni.showToast({
          title: "退回失败",
          icon: "error",
          duration: 2000,
        });

        this.show = false;
      }
    },
    async passHandle() {
      let userInfo = JSON.parse(uni.getStorageSync("userInfo"));
      const codes = userInfo.roles.map((item) => item.code);
      let state = "县级已审核";
      if (codes.indexOf("yszw_county_manage") !== -1) {
        state = "县级已审核";
      }
      if (codes.indexOf("yszw_city_manage") !== -1) {
        state = "市级已审核";
      }
      if (codes.indexOf("yszw_province_manage") !== -1) {
        state = "省级已审核";
      }

      try {
        let res = await this.$apis.formData.updateFormRecordState(
          FORMUID,
          this.recordId,
          {
            field: "审核状态",
            state,
            msg: state,
            attrs: {
              退回原因: "",
            },
          }
        );
        uni.showToast({
          title: "审核成功",
          icon: "success",
          duration: 2000,
        });
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 500);
      } catch (err) {
        uni.showToast({
          title: "审核成功",
          icon: "error",
          duration: 2000,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";
/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
}
.wrap {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  .content {
    flex: 1;
    overflow: hidden;
    .bottom-btns {
      display: flex;
      position: absolute;
      bottom: 30rpx;
      left: 0;
      width: 750rpx;
      justify-content: space-between;
      .btn {
        width: 300rpx;
      }
    }
  }
  .tab-wrapper {
    // height: 70rpx;
    .subsection {
      height: 70rpx;
    }
  }
  .form-wrap {
    height: calc(100% - 70rpx);
    overflow-y: scroll;
  }
  .popup-cont {
    padding: 30rpx;
    .title {
      color: rgb(48, 49, 51);
      margin-bottom: 20rpx;
      text-align: center;
      font-weight: 550;
    }
    .btn {
      margin-top: 30rpx;
    }
  }
}
</style>
