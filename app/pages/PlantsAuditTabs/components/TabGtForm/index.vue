<template>
  <view class="content">
    <GTForm
      ref="gtForm"
      v-if="options"
      :options="options"
      @initialized="initHandler"
      @submit="submitHandler"
    ></GTForm>
  </view>
</template>

<script>
import GTForm from "@/components/form/GTForm";

export default {
  components: {
    GTForm,
  },

  data() {
    return {
      options: undefined,
      formDef: undefined,
      formRecordData: undefined,
    };
  },

  props: {
    formUid: {
      type: String,
    },
    formRecordUid: {
      type: String,
    },
  },

  mounted() {
    this.initForm();
  },

  methods: {
    async initForm() {
      let options = {};
      if (this.formUid) {
        // 获取表单定义
        options.formUid = this.formUid;
        options.formDef = await this.$apis.formDef.getFormDef(this.formUid);
      }
      if (this.formRecordUid) {
        // 获取表单数据
        options.formRecordUid = this.formRecordUid;
        const result = await this.$apis.formData.getFormRecord(
          this.formUid,
          this.formRecordUid
        );
        options.formRecordData = this.$utils.form.toFrontFormData(result);
      }
      options.readonly = true;
      this.options = options;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

.content {
  padding: 0px 20rpx 64px;
}
</style>
