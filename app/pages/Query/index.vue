<template>
	<view>
		<mapCheck :class="[tabIndex == 0 ? 'show-block' : 'hide-block']" />
		<dataList v-show="tabIndex !== 0" :class="[tabIndex !== 0 ? 'show-block' : 'hide-block']" :tabIndex="tabIndex" />

		<u-tabbar :value="tabIndex" @change="(val) => (tabIndex = val)" :fixed="true" :placeholder="false" :safeAreaInsetBottom="true">
			<u-tabbar-item text="地图模块" icon="map-fill"></u-tabbar-item>
			<u-tabbar-item text="措施记录" icon="file-text-fill"></u-tabbar-item>
			<u-tabbar-item text="ph测量记录" icon="list"></u-tabbar-item>
		</u-tabbar>
	</view>
</template>
<script>
	import mapCheck from "@/app/pages/Query/MapModule/index.vue";
	import dataList from "@/app/pages/Query/DataList/index.vue";

	export default {
		components: { mapCheck, dataList },
		data() {
			return {
				tabIndex: 0,
			};
		},
		onLoad() {
			uni.$off("changeTab");
			uni.$on("changeTab", (index) => {
				this.tabIndex = index;
			});
		},
		methods: {},
	};
</script>

<style lang="scss" scoped>
	.hide-block {
		position: fixed;
		right: 9999px;
		opacity: 0;
		width: 100vw;
		height: 100vh;
	}

	.show-block {
		transition: opacity 0.2s;
		opacity: 1;
		width: 100vw;
		height: 100vh;
		position: relative;
	}
</style>