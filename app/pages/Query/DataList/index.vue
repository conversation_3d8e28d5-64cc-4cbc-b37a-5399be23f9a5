<template>
	<view class="LogList-wrapper">
		<u-navbar :title="tabIndex === 1 ? '施工地块' : 'ph测量记录'" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder @leftClick="leftClickHandler">
			<view slot="right" class="area-text" :style="{
          fontSize: regionLabel && regionLabel.length > 5 ? '24rpx' : '28rpx',
        }" @click="districtSelectShow = true">切换[{{ regionLabel || "行政区划" }}]
			</view>
		</u-navbar>
		<view class="main">
			<view class="search-box">
				<u-search placeholder="请输入地块编码" bgColor="#fff" v-model="searchValue" @clear="reloadData" @custom="reloadData" @search="reloadData"></u-search>
			</view>
			<scroll-view scroll-y class="scroll-content" @scrolltolower="scrollLowerHandler" :style="contentStyle">
				<SelfDataList :data="dataList" :fields="fields" :titleField="titleField" :operations="operations" @edit="editHandler" @navigate="navigateHandler" @view="viewHandler"
					@delete="deleteHandler" />
				<view class="loading" v-if="dataList.length > 0">
					<u-loadmore :status="loadStatus" />
				</view>
			</scroll-view>
		</view>
		<AreaPicker ref="areaPicker" :default="regionDefault" :mustSelect="mustSelect" class="areaPicker" :baseLevel="2" v-model="districtSelectShow" @confirm="confirDistrictSelct"></AreaPicker>
		<u-modal class="delModal" :show="modalShow" :title="$constants.MSG.WARNING_TITLE" :content="$constants.MSG.WARNING_DATA_DELETE" :confirmColor="$constants.COLOR.PRIMARY_COLOR" showCancelButton
			@cancel="deleteCancelHandler" @confirm="deleteConfirmHandler" />
	</view>
</template>

<script>
	import AreaPicker from "@/app/components/AreaPicker/index.vue";
	import SelfDataList from "@/app/components/SelfDataList/index.vue";
	import BaseDataList from "@/components/common/BaseDataList";
	import Storage from "@/tools/Storage";

	const DK_FORM = "地块属性表";
	export default {
		name: "DataList",
		mixins: [BaseDataList],
		components: {
			SelfDataList,
			AreaPicker,
		},
		props: {
			tabIndex: {
				type: Number,
				default: 1,
			},
		},
		watch: {
			tabIndex(val) {
				if (val === 1) {
					this.formName = "措施落地佐证记录表";
				} else {
					this.formName = "地块ph测量记录表";
				}
				let userInfo = uni.getStorageSync("userInfo");
				if (userInfo) userInfo = JSON.parse(userInfo);
				this.init();
				this.$refs.areaPicker.reset();
				this.regionLabel = userInfo.fname;
				this.regionFilter.name = "fcode";
				this.regionFilter.value = userInfo.fcode;
				this.searchValue = null;
				this.reloadData();
			},
		},

		created() {
			this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
			this.mustSelect = this.userInfo.role_code.includes("city_manage");
			console.log("tabIndex", this.tabIndex);
		},
		computed: {
			contentStyle() {
				const system = uni.getSystemInfoSync();
				return {
					height: `${system.windowHeight - system.statusBarHeight - 44 - 50 - 50}px`,
				};
			},
		},

		async mounted() {
			await this.init();
			uni.$on("refresh-project", this.refreshHandler);
		},
		data() {
			return {
				// 搜索
				searchValue: "",
				formName: "措施落地佐证记录表",

				regionLabel: "",
				regionFilter: null,
				districtSelectShow: false,
				dataList: [],
				fields: [],
				titleField: undefined,
				operations: [{
						key: "navigate",
						label: "定位",
					},
					{
						key: "delete",
						label: "删除",
						type: "error",
					},
					{
						key: "edit",
						label: "编辑",
					},
					{
						key: "view",
						label: "查看",
					},
				],
				page: {
					pageNum: 1,
					pageSize: 10,
				},
				loadStatus: "loadmore",
				dataLoading: false,
				modalShow: false,
				delItemId: undefined,
				userInfo: undefined,
				regionDefault: null,
				mustSelect: false,
			};
		},
		methods: {
			async init() {
				const res = await this.$apis.formDef.getFormDef(this.formName);
				this.fields = res.listFields;
				this.titleField = res.titleField;
			},
			leftClickHandler() {
				uni.navigateBack({ delta: 1 });
			},
			confirDistrictSelct(e) {
				const { data, config } = e;
				if (this.regionFilter && this.regionFilter.value === data.value) return;
				this.regionFilter = {
					name: config.valuekey,
					value: data.value,
				};
				this.regionLabel = data.label;
				this.districtSelectShow = false;
				this.reloadData();
			},
			async getList() {
				this.showLoading("数据加载中...");
				try {
					const joins = [{
						type: "left",
						table: `${DK_FORM}_${this.regionFilter.value.slice(0, 6)}`,
						joinedTable: this.formName,
						multiJoinFields: [{
							field: "地块编码",
							joinedField: "地块编码",
						}, ],
						outFields: [
							"中心经度",
							"中心纬度",
							"_id",
							"cname",
							"fname",
							"tname",
							"vname",
							"地块编码",
						],
					}, ];
					let filter = [];
					filter.push(["=", "create_by", this.userInfo.user_id]);
					// 搜索条件
					if (this.searchValue) filter.push(["=", "地块编码", this.searchValue]);
					// 区县过滤
					if (this.regionFilter) {
						const { name, value } = this.regionFilter;
						joins[0].filter = ["=", name, value];
					}
					if (filter.length > 1) {
						filter.unshift("and");
					} else {
						filter = filter.flat(1);
					}
					const res = await this.$apis.formData.joinSearchFormData(
						this.formName, {
							joins,
							filter,
							page: this.page,
						}
					);
					return res.list.map((i) => {
						const obj = {};
						for (const key in i) {
							const formatKey = key.split(".")[1];
							if (key.endsWith("_id")) obj.地块_id = i[key];
							if (key.startsWith(DK_FORM) && key.endsWith("_id")) continue;
							obj[formatKey] = i[key];
						}
						return obj;
					});
				} catch (error) {
					console.log(error);
				}
			},
			async reloadData() {
				this.page.pageNum = 1;
				this.dataLoading = true;
				try {
					const list = await this.getList();
					this.dataList = list;
					if (list && list.length < this.page.pageSize)
						this.loadStatus = "nomore";
					if (list.length == this.page.pageSize) this.loadStatus = "loadmore";
					console.log("信息", list.length, this.page.pageSize);
					console.log("状态", this.loadStatus);
					console.log("数据", list);
				} catch (error) {
					console.log("加载数据出现问题", error);
					this.loadStatus = "nomore";
				} finally {
					this.hideLoading();
					this.dataLoading = false;
				}
			},
			async loadMore() {
				this.loadStatus = "loading";
				this.dataLoading = true;
				try {
					const list = await this.getList();
					this.dataList.push(...list);
					if (list && list.length < this.page.pageSize)
						this.loadStatus = "nomore";
					if (list.length == this.page.pageSize) this.loadStatus = "loadmore";
					console.log("状态", list.length, this.page.pageSize);
					console.log("数据", list);
				} catch (error) {
					console.log("加载数据发生错误：", e);
					this.loadStatus = "nomore";
				} finally {
					this.hideLoading();
					this.dataLoading = false;
				}
			},
			scrollLowerHandler() {
				if ((this.loadStatus == "nomore") | (this.dataLoading == true)) return;
				this.page.pageNum += 1;
				this.loadMore();
			},
			async editHandler(data) {
				const formRecordUid = data._id;
				if (!formRecordUid) return;
				try {
					const res = await this.$apis.formData.getFormRecord(
						this.formName,
						formRecordUid, {
							resolveDic: false,
							resolveSubItems: true,
						}
					);
					uni.$off("refresh-page");
					uni.$once("refresh-page", (formName) => {
						if (this.formName !== formName) return;
						this.reloadData();
					});
					Storage.saveFormData(formRecordUid, res);
					const pageUrl = `/pages/FormPage/index?formUid=${this.formName}&formRecordUid=${formRecordUid}&cacheable=false`;
					uni.navigateTo({ url: pageUrl });
				} catch (error) {
					this.showError(error);
				}
			},
			async viewHandler(data) {
				const formRecordUid = data._id;
				if (!formRecordUid) return;
				const res = await this.$apis.formData.getFormRecord(
					this.formName,
					formRecordUid, {
						resolveDic: false,
						resolveSubItems: true,
					}
				);
				Storage.saveFormData(formRecordUid, res);
				const pageUrl = `${this.$constants.PAGE.VIEW_PAGE_URL}?formUid=${this.formName}&formRecordUid=${formRecordUid}`;
				uni.navigateTo({ url: pageUrl });
			},
			navigateHandler(data) {
				console.log("定位信息", data);
				// let lon = data[POINT_LON_FIELD];
				// let lat = data[POINT_LAT_FIELD];
				// if (isNaN(lon) || isNaN(lat)) return;
				// Navigation.navigateTo(lon, lat);
				uni.$emit("changeTab", 0);
				uni.$emit("point-fly", data);
			},
			deleteHandler(data) {
				const formRecordUid = data._id;
				if (!formRecordUid) return;
				this.delItemId = formRecordUid;
				this.modalShow = true;
			},
			async deleteConfirmHandler() {
				try {
					this.showLoading(this.$constants.MSG.DELETING);
					await this.$apis.formData.deleteFormRecord(
						this.formName,
						this.delItemId
					);
					await this.reloadData();
					this.showSuccess(this.$constants.MSG.DELETE_SUCCESS);
				} catch (error) {
					this.showError(this.$constants.MSG.DELETE_FAIL);
				} finally {
					this.hideLoading();
				}
				this.delItemId = undefined;
				this.modalShow = false;
			},
			deleteCancelHandler() {
				this.delItemId = undefined;
				this.modalShow = false;
			},

			refreshHandler(formName) {
				console.log("formName", formName);
				if (this.formName === formName) {
					this.reloadData();
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	@import "@/static/styles/common.scss";

	/deep/ .u-navbar__content {
		.u-navbar__content__title {
			color: $gt-navbar-title-color !important;
		}
	}

	/deep/ .u-icon__icon.uicon-arrow-left {
		color: #fff !important;
	}

	.area-text {
		color: #fff;
		display: flex;
		align-items: center;
	}

	.LogList-wrapper {
		display: flex;
		flex-direction: column;
		width: 100vw;
		height: 100vh;

		.main {
			background-color: #f8f8f8;
			box-sizing: border-box;
			overflow: hidden;

			.search-box {
				width: 100%;
				height: 50px;
				box-sizing: border-box;
				padding: 10px 0;
				background-color: #fff;
				display: flex;
				align-items: center;
			}

			.select-wrapper {
				width: 30%;
				height: 100%;
				flex-shrink: 0;
			}

			.scroll-content {
				box-sizing: border-box;
				overflow: hidden;
				padding: 0 30rpx
			}

			.loading {
				padding-bottom: 40rpx;
			}
		}
	}

	.areaPicker {
		position: absolute;
	}

	.delModal {
		position: absolute;
	}
</style>