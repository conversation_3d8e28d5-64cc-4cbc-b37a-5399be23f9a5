<template>
  <view class="wrap">
    <u-navbar
      :title="pointName"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      autoBack
    ></u-navbar>
    <view class="part">
      <view class="title">历次调查情况</view>
      <view class="content">
        <view class="table">
          <view class="tr">
            <view
              class="th"
              v-for="col in columns"
              :style="{ width: col.width }"
            >
              {{ col.label }}
            </view>
          </view>
          <view class="tr" v-for="item in tableData">
            <view
              class="td"
              v-for="col in columns"
              :style="{ width: col.width }"
            >
              <view
                v-if="col.type == 'operate'"
                class="operate"
                @click="recordClick(col.operate, item)"
              >
                {{ col.operate }}
              </view>
              <view v-else> {{ item[col.dataIndex] }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="part">
      <view class="title"> 历次调查物种丰富度</view>
      <view class="content chart-box">
        <qiun-data-charts
          type="line"
          :opts="lineOpts"
          :ontouch="true"
          :chartData="lineChartData"
        />
      </view>
    </view>
    <u-popup :show="show" closeable @close="show = false">
      <view class="popup-content">
        <view class="title">{{ currentRound }}-样方物种丰富度</view>
        <view class="chart-box">
          <qiun-data-charts
            type="column"
            :opts="colOpts"
            :ontouch="true"
            :chartData="colChartData"
          />
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
const FORM_UID = "野生植物原生境保护点监测表二";
import { LINE_OPTS, TABLE_COLUMNS, COL_OPTS } from "./config/chart.config";
export default {
  data() {
    return {
      show: false,
      pointName: "",
      columns: TABLE_COLUMNS,
      tableData: [],
      lineOpts: LINE_OPTS,
      lineChartData: {},
      colOpts: COL_OPTS,
      colChartData: {},
      currentRound: null,
    };
  },
  onLoad(option) {
    this.pointName = option.point_name;
    this.pointId = option.point_id;
    this.getData();
  },
  methods: {
    async getData() {
      let res = await this.$apis.formData.getFormRecords(FORM_UID, {
        filter: ["=", "保护点_uid", this.pointId],
      });
      let arr = [];
      let speciesList = [];
      res.list.forEach((item) => {
        item = this.$utils.form.toFrontFormData(item);
        item.监测任务 = `${item["年份"]}年${item["轮次"]}`;
        item.目标物种数 =
          item["野生植物原生境保护点监测表二_目标物种表"].data.length;
        // return item;
        if (item["轮次"] == "上半年") {
          item.round = item["年份"] * 10 + 1;
        }
        if (item["轮次"] == "下半年") {
          item.round = item["年份"] * 10 + 2;
        }

        item["野生植物原生境保护点监测表二_目标物种表"].data.forEach((i) => {
          speciesList.push(i["中文学名"]);
        });
        arr.push(item);
      });
      if (speciesList && !speciesList.length > 0) {
        uni.showToast({
          title: "暂无监测信息",
          icon: "error",
          duration: 2000,
        });
        return;
      }
      console.log(speciesList);

      arr = arr.sort((a, b) => {
        return a["round"] - b["round"];
      });
      const categories = [];
      const series = [...new Set(speciesList)].map((item) => {
        return {
          name: item,
          connectNulls: true,
          data: [],
        };
      });
      arr.forEach((item) => {
        console.log(item);

        series.forEach((serie) => {
          const res = item["野生植物原生境保护点监测表二_目标物种表"].data.find(
            (i) => {
              return i["中文学名"] == serie.name;
            }
          );
          console.log(res);
          if (res) {
            serie.data.push(res["目标物种丰富度"]);
          } else {
            serie.data.push(null);
          }
        });
        categories.push(item["监测任务"]);
      });
      this.lineChartData = {
        categories,
        series,
      };
      console.log(this.lineChartData);
      this.tableData = arr.sort((a, b) => {
        return b["round"] - a["round"];
      });
    },
    recordClick(key, record) {
      this.show = true;
      console.log(key, record);
      this.currentRound = record["监测任务"];
      const data = record["野生植物原生境保护点监测表二_目标物种表"].data;
      let res = {
        categories: data[0][
          "野生植物原生境保护点监测表二_目标物种样方表"
        ].data.map((i, n) => {
          return `样方${n + 1}`;
        }),
        series: data.map((item) => {
          return {
            name: item["中文学名"],
            data: item["野生植物原生境保护点监测表二_目标物种样方表"].data.map(
              (item) => {
                return item["目标物种丰富度"];
              }
            ),
          };
        }),
      };
      this.colChartData = res;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    // font-size: 36rpx;
  }
}
.wrap {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f9;
  height: 100vh;
  .part {
    margin: 30rpx;
    width: 690rpx;

    border-radius: 16rpx;
    border: 8rpx solid #ffffff;
    background-color: #fff;
    .title {
      height: 70rpx;
      background: linear-gradient(
        180deg,
        rgba(0, 168, 139, 0.08) 0%,
        rgba(0, 168, 139, 0) 100%
      );
      font-size: 36rpx;
      font-weight: 500;
      color: #00a88b;
      line-height: 70rpx;
      padding-left: 20rpx;
    }
    .content {
      min-height: 400rpx;
      width: 670rpx;
      margin: 20rpx 0;
      .table {
        font-size: 28rpx;
        .tr {
          display: flex;
          .th {
            font-weight: 550;
            margin-bottom: 20rpx;
            text-align: center;
          }
          .td {
            box-sizing: border-box;
            padding: 15rpx 10rpx;
            text-align: center;
            .operate {
              color: #00a88b;
            }
          }
        }
      }
    }
    .chart-box {
      height: 550rpx;
    }
  }
  .popup-content {
    width: 750rpx;
    height: 650rpx;
    .title {
      margin-top: 10rpx;
      height: 70rpx;
      background: linear-gradient(
        180deg,
        rgba(0, 168, 139, 0.08) 0%,
        rgba(0, 168, 139, 0) 100%
      );
      font-size: 36rpx;
      font-weight: 500;
      color: #00a88b;
      line-height: 70rpx;
      padding-left: 20rpx;
    }
    .chart-box {
      width: 750rpx;
      height: 500rpx;
    }
  }
}
</style>
