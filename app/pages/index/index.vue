<template>
	<view class="page-content">
		<u-navbar title="首页" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder>
		</u-navbar>
		<view class="card-list">
			<vew v-for="item in list" :key="item.name">
				<view class="card-block" v-if="showRoles(item.showRoles)" :style="{backgroundColor:item.color}" @click="toPath(item.url)">
					<view class="info">
						<image class="icon" :src="item.icon" mode="aspectFill"></image>
						<view class="name">{{item.name}}</view>
					</view>
					<image class="icon" src="/static/images/arrow-right.svg" mode="aspectFill"></image>
				</view>
			</vew>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					name: "地块核查",
					icon: "/static/images/map-check-icon.svg",
					color: "#69b076",
					url: "/app/pages/Verify/index",
					showRoles: ["check_manage", "county_manage"],
				}, {
					name: "项目管理",
					icon: "/static/images/project-manage-icon.svg",
					color: "#007bbb",
					url: "/app/pages/ProjectList/index",
					showRoles: ["county_manage", "city_manage", "implementer"],
				}, {
					name: "推荐措施",
					icon: "/static/images/map-check-icon.svg",
					color: "#69b076",
					url: "/app/pages/Query/index",
					showRoles: ["farmer"],
				}],
			}
		},
		onLoad() {
			if (process.env.NODE_ENV != "dev") {
				this.checkNewVersion();
			}
		},
		methods: {
			async checkNewVersion() {
				const result = await this.$apis.version.checkVersion();
				if (!result) {
					return;
				}
				const { upgrade, version, url } = result;
				if (upgrade === true) {
					uni.reLaunch({
						url: `/app/pages/UpdatePage/index?data=${JSON.stringify(result)}`
					})
					// uni.showModal({
					// 	title: "版本更新",
					// 	content: this.$constants.VERSION.NEW_VERSION,
					// 	showCancel: process.env.NODE_ENV == "dev",
					// 	success: (res) => {
					// 		if (res.confirm) {
					// 			plus.runtime.openURL(url);
					// 		}
					// 	},
					// });
				}
			},
			showRoles(roles) {
				let userInfo = JSON.parse(uni.getStorageSync("userInfo"));
				let show = false
				roles.forEach(item => {
					if (userInfo.role_code.includes(item)) {
						show = true
					}
				})
				return show
			},
			toPath(url) {
				if (url) uni.navigateTo({ url })
			},
		}
	}
</script>

<style scoped lang="scss">
	@import "@/static/styles/common.scss";

	/deep/ .u-navbar__content {
		.u-navbar__content__title {
			color: $gt-navbar-title-color !important;
		}
	}

	/deep/ .u-icon__icon.uicon-arrow-left {
		color: #fff !important;
	}

	/deep/ .u-navbar__content__left {
		display: none;
	}

	.page-content {
		background-color: #fff;
		min-height: 100vh;
	}

	.card {
		&-block {
			width: 80%;
			height: 200rpx;
			padding: 30rpx;
			margin: 30rpx auto 0;
			border-radius: 24rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.info {
				display: flex;
				align-items: center;
			}

			.icon {
				width: 68rpx;
				height: 68rpx;
				margin-right: 50rpx;
				flex-shrink: 0;
			}

			.name {
				font-size: 40rpx;
				font-weight: bold;
				color: #fff;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				width: 350rpx;
			}
		}
	}
</style>