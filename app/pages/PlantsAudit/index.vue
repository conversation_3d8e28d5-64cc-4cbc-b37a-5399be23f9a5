<template>
  <view class="wrap">
    <u-navbar
      title="原生境保护点列表"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      autoBack
    >
    </u-navbar>
    <view class="content">
      <view class="filter-bar">
        <view class="filter-item" @click="showPicker('年份')">
          <text :style="{ color: year ? '' : '#ccc' }">
            {{ year ? year : "年份" }}
          </text>
          <u-icon color="#ccc" class="arrow" name="arrow-down"></u-icon>
        </view>
        <view class="filter-item" @click="showPicker('轮次')">
          <text :style="{ color: round ? '' : '#ccc' }">
            {{ round ? round : "轮次" }}
          </text>
          <u-icon color="#ccc" class="arrow" name="arrow-down"></u-icon>
        </view>
        <view class="filter-item" @click="showPicker('状态')">
          <text :style="{ color: state ? '' : '#ccc' }">
            {{ state ? state : "状态" }}
          </text>
          <u-icon color="#ccc" class="arrow" name="arrow-down"></u-icon>
        </view>
      </view>
      <view class="data-list">
        <DataList
          :data="dataList"
          :userInfo="userInfo"
          @view="viewHandle"
          @edit="editHandle"
        />
      </view>
    </view>
    <u-picker
      v-if="show"
      :show="show"
      :columns="columns"
      @confirm="confirm"
      @cancel="cancel"
    ></u-picker>
  </view>
</template>

<script>
const FORMUID = "野生植物原生境保护点填报表";
import DataList from "./components/DataList";
import Storage from "@/tools/Storage";
export default {
  components: { DataList },
  data() {
    return {
      show: false,
      columns: [],
      currentFilter: null,
      year: null,
      round: null,
      state: null,
      userInfo: null,
      dataList: [],
    };
  },
  mounted() {
    let userInfo = JSON.parse(uni.getStorageSync("userInfo"));
    this.userInfo = userInfo;
    this.getDataList();
  },
  onShow() {
    if (this.userInfo) {
      this.getDataList();
    }
  },
  methods: {
    showPicker(e) {
      this.currentFilter = e;
      switch (e) {
        case "年份":
          const year = new Date().getFullYear();
          const years = [];
          for (let i = year; i >= 2021; i--) {
            years.push(i);
          }
          this.columns = [years];
          break;
        case "轮次":
          this.columns = [["上半年", "下半年"]];
          break;
        case "状态":
          this.columns = [
            ["未审核", "县级已审核", "市级已审核", "省级已审核", "已退回"],
          ];
          break;
      }
      this.show = true;
    },
    confirm(e) {
      if (this.currentFilter == "年份") {
        this.year = e.value[0];
      }
      if (this.currentFilter == "轮次") {
        this.round = e.value[0];
      }
      if (this.currentFilter == "状态") {
        this.state = e.value[0];
      }
      this.show = false;
      this.getDataList();
    },
    cancel() {
      if (this.currentFilter == "年份") {
        this.year = null;
      }
      if (this.currentFilter == "轮次") {
        this.round = null;
      }
      if (this.currentFilter == "状态") {
        this.state = null;
      }
      this.show = false;
      this.getDataList();
    },
    async getDataList() {
      let filter = [
        "and",
        ["!=", "审核状态", "填报中"],
        // ["!=", "审核状态", "已退回"],
      ];
      console.log(this.userInfo);
      if (this.userInfo.fcode) {
        filter.push(["=", "县编码", this.userInfo.fcode]);
      } else if (this.userInfo.ccode) {
        filter.push(["=", "市编码", this.userInfo.ccode]);
      }
      if (this.year) {
        filter.push(["=", "年份", this.year]);
      }
      if (this.round) {
        filter.push(["=", "轮次", this.round]);
      }
      if (this.state) {
        filter.push(["=", "审核状态", this.state]);
      }
      if (filter.length == 2) {
        filter = filter[1];
      }
      let res = await this.$apis.formData.getFormRecords(FORMUID, { filter });
      console.log(res);
      this.dataList = res.list;
    },
    viewHandle(item) {
      const { 监测表一数据_uid, 监测表二数据_uid, 监测报告表数据_uid, _id } =
        item;
      const obj = [监测表一数据_uid, 监测表二数据_uid, 监测报告表数据_uid];
      uni.navigateTo({
        url: `/app/pages/PlantsAuditTabs/index?type=view&ids=${JSON.stringify(
          obj
        )}&record_id=${_id}`,
      });
    },
    editHandle(item) {
      const { 监测表一数据_uid, 监测表二数据_uid, 监测报告表数据_uid, _id } =
        item;
      const obj = [监测表一数据_uid, 监测表二数据_uid, 监测报告表数据_uid];
      uni.navigateTo({
        url: `/app/pages/PlantsAuditTabs/index?type=audit&ids=${JSON.stringify(
          obj
        )}&record_id=${_id}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";
/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
}
.wrap {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f9;
}
.content {
  flex: 1;
  overflow: hidden;
  .filter-bar {
    width: 710rpx;
    margin: 20rpx auto 0;
    display: flex;
    justify-content: space-between;
    height: 80rpx;
    background-color: #fff;
    border-radius: 15rpx;
    font-size: 30rpx;
    .filter-item {
      height: 100%;
      align-items: center;
      flex: 1;
      display: flex;
      justify-content: center;
      .arrow {
        margin-left: 20rpx;
      }
    }
  }
  .data-list {
    height: calc(100% - 100rpx);
    overflow-y: scroll;
  }
}
</style>
