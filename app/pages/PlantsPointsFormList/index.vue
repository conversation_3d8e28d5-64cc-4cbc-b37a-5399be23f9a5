<template>
  <view class="wrap">
    <u-navbar
      title="原生境保护点列表"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      autoBack
    ></u-navbar>
    <view class="content">
      <view class="filter-bar">
        <view class="filter-item" @click="showPicker('年份')">
          <text :style="{ color: year ? '' : '#ccc' }">
            {{ year ? year : "年份" }}
          </text>
          <u-icon color="#ccc" class="arrow" name="arrow-down"></u-icon>
        </view>
        <view class="filter-item" @click="showPicker('轮次')">
          <text :style="{ color: round ? '' : '#ccc' }">
            {{ round ? round : "轮次" }}
          </text>
          <u-icon color="#ccc" class="arrow" name="arrow-down"></u-icon>
        </view>
      </view>
      <view class="tab-wrapper">
        <u-subsection
          class="subsection"
          :list="sectionList"
          :current="currentSection"
          @change="sectionChangeHandler"
          :activeColor="$constants.COLOR.PRIMARY_COLOR"
        ></u-subsection>
      </view>
      <view class="data-list">
        <DataList
          :data="dataList"
          @edit="editDataHandle"
          @view="viewDataHandle"
        />
      </view>
      <view class="btn-wrap">
        <u-button type="primary" @click="btnClickHandle">添加</u-button>
      </view>
    </view>
    <u-picker
      v-if="show1"
      :show="show1"
      :columns="columns1"
      @confirm="confirm1"
      @cancel="cancel1"
    ></u-picker>
    <u-picker
      v-if="show"
      :show="show"
      :columns="columns"
      @confirm="confirm"
      @cancel="cancel"
      :defaultIndex="defaultIndex"
    ></u-picker>
  </view>
</template>

<script>
import Storage from "@/tools/Storage.js";
const FORMUID = "野生植物原生境保护点填报表";
import _ from "lodash";
import DataList from "./components/DataList";

export default {
  components: { DataList },
  data() {
    return {
      pointId: null,
      currentSection: 0,
      sectionList: ["已上报", "已退回"],
      thisPoint: {},
      show: false,
      columns: [[], ["上半年", "下半年"]],
      defaultIndex: [],
      dataList: [],
      show1: false,
      columns1: [],
      currentFilter: null,
      year: null,
      round: null,
    };
  },
  onLoad(option) {
    this.pointId = option.pointId;
    const result = Storage.getFormData(this.pointId);
    this.thisPoint = result;
    this.getYear();
    this.getDataList();
  },
  onShow() {
    this.getDataList();
  },
  methods: {
    getYear() {
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth();
      const years = [];
      for (let i = year; i >= 2021; i--) {
        years.push(i);
      }
      this.columns[0] = years;
      this.defaultIndex = [years.indexOf(year), month >= 6 ? 1 : 0];
    },
    sectionChangeHandler(e) {
      console.log(e);
      this.currentSection = e;
      this.getDataList();
    },
    async getDataList() {
      let stateFilter;
      if (this.currentSection == 0) {
        stateFilter = ["!=", "审核状态", "已退回"];
      } else {
        stateFilter = ["=", "审核状态", "已退回"];
      }
      const filter = ["and", ["=", "保护点_uid", this.pointId], stateFilter];
      if (this.year) {
        filter.push(["=", "年份", this.year]);
      }
      if (this.round) {
        filter.push(["=", "轮次", this.round]);
      }
      let res = await this.$apis.formData.getFormRecords(FORMUID, {
        filter,
      });
      this.dataList = res.list;
      console.log(res);
    },
    btnClickHandle() {
      this.show = true;
    },
    confirm(e) {
      console.log(e);
      console.log(this.thisPoint);
      const {
        省名称,
        省编码,
        市名称,
        市编码,
        县名称,
        县编码,
        名称,
        所在地,
        状态,
        启用,
        目标物种,
        _id,
      } = this.thisPoint;
      const 年份 = e.value[0];
      const 轮次 = e.value[1];
      const obj = {
        年份,
        轮次,
        省名称,
        省编码,
        市名称,
        市编码,
        县名称,
        县编码,
        保护点_uid: _id,
        保护点名称: 名称,
        所在地,
        目标物种,
        唯一值: `${年份}_${轮次}_${_id}`,
      };
      console.log(obj);
      this.defaultIndex = e.indexs;
      this.show = false;
      const res = this.dataList.find((item) => {
        return item["唯一值"] == obj["唯一值"];
      });
      console.log(res);
      if (res) {
        Storage.saveFormData(obj["唯一值"], res);
        let url = `/app/pages/PlantsFormTypes/index?primaryId=${obj["唯一值"]}`;
        if (
          res["审核状态"] &&
          !(
            res["审核状态"] == "填报中" ||
            res["审核状态"] == "未审核" ||
            res["审核状态"] == "已退回"
          )
        ) {
          url = `/app/pages/PlantsFormTypes/index?primaryId=${obj["唯一值"]}&type=view`;
        }
        uni.navigateTo({
          url,
        });
      } else {
        Storage.saveFormData(obj["唯一值"], obj);
        uni.navigateTo({
          url: `/app/pages/PlantsFormTypes/index?primaryId=${obj["唯一值"]}`,
        });
      }
    },
    editDataHandle(data) {
      const obj = _.cloneDeep(data);
      Storage.saveFormData(obj["唯一值"], obj);
      uni.navigateTo({
        url: `/app/pages/PlantsFormTypes/index?primaryId=${obj["唯一值"]}`,
      });
    },
    viewDataHandle(data) {
      const obj = _.cloneDeep(data);
      Storage.saveFormData(obj["唯一值"], obj);
      uni.navigateTo({
        url: `/app/pages/PlantsFormTypes/index?primaryId=${obj["唯一值"]}&type=view`,
      });
    },
    cancel() {
      this.show = false;
    },
    showPicker(e) {
      this.currentFilter = e;
      switch (e) {
        case "年份":
          const year = new Date().getFullYear();
          const years = [];
          for (let i = year; i >= 2021; i--) {
            years.push(i);
          }
          this.columns1 = [years];
          break;
        case "轮次":
          this.columns1 = [["上半年", "下半年"]];
          break;
      }
      this.show1 = true;
    },
    confirm1(e) {
      if (this.currentFilter == "年份") {
        this.year = e.value[0];
      }
      if (this.currentFilter == "轮次") {
        this.round = e.value[0];
      }
      this.show1 = false;
      this.getDataList();
    },
    cancel1() {
      if (this.currentFilter == "年份") {
        this.year = null;
      }
      if (this.currentFilter == "轮次") {
        this.round = null;
      }
      this.show1 = false;
      this.getDataList();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";
/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
}
.wrap {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .content {
    flex: 1;
    position: relative;
    overflow: hidden;
    .filter-bar {
      width: 710rpx;
      margin: 0 auto 10rpx;
      display: flex;
      justify-content: space-between;
      height: 80rpx;
      background-color: #fff;
      border-radius: 15rpx;
      font-size: 30rpx;
      .filter-item {
        height: 100%;
        align-items: center;
        flex: 1;
        display: flex;
        justify-content: center;
        .arrow {
          margin-left: 20rpx;
        }
      }
    }
    /deep/ .u-subsection {
      height: 84rpx !important;
      .u-subsection--button {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .data-list {
      height: calc(100% - 184rpx);
      overflow-y: scroll;
    }
    .btn-wrap {
      position: absolute;
      z-index: 99;
      bottom: 12rpx;
      left: 30rpx;
      width: 690rpx;
      height: 100rpx;
    }
  }
}
</style>
