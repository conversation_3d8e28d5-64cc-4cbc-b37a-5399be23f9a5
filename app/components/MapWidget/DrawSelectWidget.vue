<template>
	<view>
		<view class="draw-widget" :style="customStyle">
			<template v-if="drawing">
				<view class="draw-item" @click="backClick()">
					<u-icon name="close"></u-icon>
				</view>
				<view class="draw-item" @click="redoPoint()">
					<image class="icon" src="/static/images/draw-return.png" mode="widthFix"></image>
				</view>
				<view class="draw-item" @click="saveHandler">
					<image class="icon" src="/static/images/draw-check.png" mode="widthFix"></image>
				</view>
			</template>
			<view v-else class="draw-item" @click="addPoint()">
				<image class="icon" src="/static/images/polygon-select.png" mode="widthFix"></image>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				drawing: false,
			};
		},
		props: {
			customStyle: {
				type: Object,
				default: () => {},
			},
		},
		methods: {
			addPoint() {
				this.drawing = true;
				uni.$emit("startDraw");
			},
			redoPoint() {
				uni.$emit("redoPoint");
			},
			backClick() {
				this.drawing = false;
				uni.$emit("toolDeactivate");
			},
			saveHandler() {
				this.drawing = false;
				uni.$emit("endDraw");
			},
		},
	};
</script>

<style lang="scss" scoped>
	.draw-widget {
		position: absolute;
		display: flex;
		flex-direction: column;
		z-index: 8;

		.draw-item {
			padding: 18rpx;
			height: 16px;
			margin-bottom: 20rpx;
			background-color: #fff;

			.icon {
				width: 16px;
				height: 16px;
			}
		}
	}
</style>