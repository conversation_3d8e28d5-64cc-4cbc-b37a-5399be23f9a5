<template>
	<view class="location-widget" :style="customStyle">
		<view class="location-item" @click="locationClick">
			<u-icon name="map" v-show="!loading"></u-icon>
			<u-loading-icon v-show="loading" size="16"></u-loading-icon>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: false,
			};
		},
		props: {
			customStyle: {
				type: Object,
				default: () => {},
			},
		},
		methods: {
			async locationClick() {
				if (this.loading) return;
				this.loading = true;
				try {
					let res = await this.$utils.location.getLocation();
					let lnglat = this.$utils.proj.wgs84togcj02(res.longitude, res.latitude)
					this.$emit("click", {
						longitude: lnglat[0],
						latitude: lnglat[1],
					});
				} catch (error) {
					console.log("定位错误", error);
				} finally {
					this.loading = false;
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	.location-widget {
		position: absolute;
		display: flex;
		flex-direction: column;
		z-index: 8;
		background-color: #fff;

		.location-item {
			padding: 18rpx;
		}
	}
</style>