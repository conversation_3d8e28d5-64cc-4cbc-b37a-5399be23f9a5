<template>
	<view class="gt-text-field" v-show="!hide">
		<u-form-item :label="label" :prop="field" :required="required" labelWidth="auto">
			<u--textarea v-model="value" :placeholder="placeholder" :suffixIcon="unit" border="surround" clearable
				confirmType="done" :maxlength="maxlength" @input="changeHandler" :disabled="readonly"></u--textarea>
		</u-form-item>
	</view>
</template>

<script>
	import BaseField from '@/components/form/BaseField'

	export default {

		name: 'TextField',

		mixins: [BaseField],

		computed: {
			hide() {
				return this.options.hide === true
			},
			maxlength() {
				return this.options.maxlength ? this.options.maxlength : -1
			}
		},

		methods: {

			setValue(value, silently = true) {
				this.value = this.genPlaceholderValue(value)
				if (!silently) this.updateFormData()
			},

			changeHandler() {
				this.updateFormData()
			}
		}
	}
</script>

<style lang="less" scoped>
	.gt-text-field {
		width: 100%;
	}
</style>