<template>
  <view class="gt-select-field">
    <u-form-item
      :label="label"
      :prop="field"
      :required="required"
      labelWidth="auto"
      @click="clickHandler"
    >
      <u-input
        border="surround"
        :placeholder="placeholder"
        v-model="valueLabel"
        disabled
        :disabledColor="readonly ? '#f5f7fa' : '#ffffff'"
      >
      </u-input>
      <u-icon slot="right" name="arrow-down"></u-icon>
    </u-form-item>
    <u-picker
      :show="pickerVisible"
      :showToolbar="true"
      :defaultIndex="defaultIndex"
      :title="label"
      :columns="columns"
      keyName="label"
      confirmColor="$constants.COLOR.PRIMARY_COLOR"
      @cancel="pickerCancelHandler"
      @confirm="pickerConfirmHandler"
      @close="pickerCancelHandler"
    >
    </u-picker>
  </view>
</template>

<script>
const PARENT_FORM_DATA_KEY = "parentFormData.";
import <PERSON><PERSON>ield from "@/components/form/BaseField";

export default {
  name: "SelectField",

  mixins: [BaseField],

  data() {
    return {
      items: undefined,
      itemsMap: undefined,
      columns: [],
      defaultIndex: [0],
      value: undefined,
      valueLabel: undefined,
      pickerVisible: false,
    };
  },

  computed: {

    placeholder() {
      return `请选择${this.label}`;
    },

    filter() {
      return this.options.filter;
    },
    endYear () {
      return this.options.endYear;
    },
    startYear () {
      return this.options.startYear;
    }
    // "endYear": 2022,
    // "startYear": 2023
  },

  mounted() {
    this.initDict();
  },

  methods: {
    initDict() {
      const startYear = this.startYear || new Date().getFullYear();
      const endYear = this.endYear || 1980;
      const items = []
      for (let index = startYear; index >= endYear; index--) {
         items.push({ label: index, value: index });
      }
      this.columns = [items]
      this.valueLabel = this.value || undefined
    },

    clickHandler() {
      if (this.readonly) return;
      this.pickerVisible = true;
    },

    pickerConfirmHandler(e) {
      // 记录选中
      this.defaultIndex = e.indexs;
      // 设置显示label和值
      const selectedItem = e.value && e.value[0];
      this.valueLabel = selectedItem?.label;
      this.setValue(selectedItem?.value);
      this.updateFormData();
      //
      this.resetPicker();
    },

    pickerCancelHandler() {
      this.resetPicker();
    },

    // 重置
    resetPicker() {
      this.pickerVisible = false;
      // 恢复数据列表
      this.initDict();
    },

    updateFormData() {
      const value = this.getValue();
      const data = undefined
      // 派发字段值变化事件
      this.bus.$emit(this.$events.form.FIELD_CHANGE, {
        field: this.field,
        value,
        data,
      });
    },

    // 设置值
    setValue(value, silently = true) {
      this.value = value === null ? undefined : value;
      if (!silently) this.updateFormData();
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/static/styles/common.scss";

.gt-select-field {
  width: 100%;
}

.picker-chunk,
.picker-content {
  width: 100%;
}

/deep/ .u-form-item__body__right {
  position: relative !important;
}

/deep/ .item__body__right__content__icon {
  position: absolute;
  right: 12px;

  .u-icon__icon {
    color: $gt-border-color !important;
  }
}

.picker-search-wrapper {
  position: fixed;
  bottom: 262px;
  left: 0;
  right: 0;
  z-index: 99999;
  background: #ffffff;

  .u-input {
    padding: 10px !important;
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-top: none;
  }
}
</style>
