<template>
	<u-popup :show="value" mode="bottom" @close="close">
		<view class="area-picker">
			<scroll-view scroll-x>
				<view class="selected">
					<view class="selected-item" v-if="defaultValue" @click="reset">{{defaultValue.label}}</view>
					<view class="selected-item" v-for="(item,index) in selects" @click="selectLevel(index)">
						{{item.label}}
					</view>
				</view>
			</scroll-view>
			<scroll-view class="content" scroll-y>
				<view class="label-item" v-for="item in columns" :key="item.value" @click="select(item)">{{item.label}}
				</view>
			</scroll-view>
			<view class="button-item">
				<view class="cancel button" @click="reset">重置</view>
				<view class="confirm button" @click="confirm">确定</view>
			</view>
		</view>
	</u-popup>
</template>

<script>
	import login from '../../../apis/login'
	const areaConfig = {
		province: {
			dict: "admin_province",
			valuekey: "pcode",
			labelkey: "pname",
		},
		city: {
			dict: "admin_city",
			valuekey: "ccode",
			labelkey: "cname",
		},
		county: {
			dict: "admin_county",
			valuekey: "fcode",
			labelkey: "fname",
		},
		town: {
			dict: "admin_town",
			valuekey: "tcode",
			labelkey: "tname",
		},
		village: {
			dict: "admin_village",
			valuekey: "vcode",
			labelkey: "vname",
		},
	}
	const areaDefault = {
		label: '广西壮族自治区',
		value: "45"
	}
	export default {
		props: {
			value: {
				type: Boolean,
				default: false,
			},
			config: {
				type: Object,
				default: () => areaConfig
			},
			default: {
				type: Object,
				default: () => areaDefault
			},
			baseLevel: {
				type: [Number, String],
				default: 1,
			},
			mustSelect: {
				type: Boolean,
				default: false,
			}
		},
		data() {
			return {
				info: null,
				userInfo: null,
				columns: [],
				selects: [],
				defaultValue: null,
				areaLevel: ['province', 'city', 'county', 'town', 'village'],
				level: 0,
				minLevel: 0,
				load: false,
			}
		},
		mounted() {
			this.init()
		},
		methods: {
			// 初始化基本数据
			async init() {
				this.selects = []
				this.areaLevel = Object.keys(this.config);
				let userInfo = uni.getStorageSync('userInfo');
				if (userInfo) userInfo = JSON.parse(userInfo);
				this.userInfo = userInfo;
				let userAreaInfo = {
					pcode: userInfo.pcode,
					ccode: userInfo.ccode,
					fcode: userInfo.fcode,
					tcode: userInfo.tcode
				};
				this.level = Object.values(userAreaInfo).filter(val => val).length; //自动计算level
				this.minLevel = this.level ? this.level : this.baseLevel
				let defaultValue = this.default
				if (this.level > 0) {
					let key = this.areaLevel[this.level - 1];
					let info = this.config[key];
					defaultValue = {
						label: userInfo[info.labelkey],
						value: userInfo[info.valuekey]
					}
				} else {
					this.level = 1
				}
				this.defaultValue = defaultValue
				await this.getData(defaultValue.value);
				// 是否默认选中第一个
				if (this.mustSelect) this.select(this.columns[0])
				this.confirm()
			},
			async getData(code) {
				let parentInfo = null
				if (this.level > 0) {
					let parent = this.areaLevel[this.level - 1]
					parentInfo = this.config[parent]
				}
				let key = this.areaLevel[this.level]
				let info = this.config[key]
				if (!info) return
				let filter = code ? ["=", parentInfo.valuekey, code] : undefined
				try {
					this.load = true
					let res = await this.$apis.formData.getFormRecords(info.dict, {
						filter,
						outFields: [info.valuekey, info.labelkey],
					});
					this.columns = res.list.map(item => {
						return {
							label: item[info.labelkey],
							value: item[info.valuekey],
						}
					})
				} catch (error) {
					console.log(error);
				} finally {
					this.load = false
				}
			},
			select(item) {
				if (this.load) return
				if (this.level < this.areaLevel.length) {
					this.level += 1
					this.selects.push(item)
					this.getData(item.value)
				} else {
					let index = this.selects.length - 1
					this.$set(this.selects, index, item)
				}
			},
			selectLevel(index) {
				this.selects.splice(index + 1, this.selects.length - 1)
				this.level = index + this.minLevel + 1
				let code = this.selects[index].value
				this.getData(code)
			},
			close() {
				this.$emit('input', false)
				this.$emit('close')
			},
			reset() {
				this.selects = []
				this.level = this.minLevel
				this.getData(this.defaultValue.value)
			},
			confirm() {
				if (this.mustSelect && this.level == this.minLevel) {
					uni.showToast({
						title: "请选择行政区过滤",
						icon: "none"
					})
					return
				}
				if (this.level == 0) {
					this.$emit('confirm', null)
				} else {
					let obj = this.config[this.areaLevel[this.level - 1]]
					let data = this.selects.length == 0 ? this.defaultValue : this.selects[this.selects.length - 1]
					this.$emit('confirm', { config: obj, data })
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	@import '@/static/styles/common.scss';

	.area-picker {
		height: 100%;
		background-color: #fff;

		.selected {
			padding: 0 30rpx;
			height: 120rpx;
			display: flex;
			flex-wrap: nowrap;
			align-items: center;

			&-item {
				flex-shrink: 0;
				border-radius: 8rpx;

				&:last-child {
					&::after {
						content: "";
					}
				}

				&::after {
					content: " >>";
					margin: 0 20rpx;
				}
			}
		}

		.content {
			height: 50vh;

			.label-item {
				height: 88rpx;
				display: flex;
				align-items: center;
				padding: 0 30rpx;
				box-sizing: border-box;
				background-color: #f8f8f8;
				margin-bottom: 2rpx;
			}
		}

		.button-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.button {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 88rpx;
		}

		.cancel {
			background-color: #f8f8f8;
			color: #333;
		}

		.confirm {
			background-color: $gt-primary-color;
			color: #fff;
		}
	}
</style>