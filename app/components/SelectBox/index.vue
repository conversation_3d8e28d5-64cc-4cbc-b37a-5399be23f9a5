<template>
	<view class="selectBox-wrapper" @click="selectShow = true">
		<span :class="['text',value ? 'value' : 'placeholder']">{{ value ? value : placeholder }}</span>
		<u-icon name="arrow-down" color="#9ca1a8" size="16"></u-icon>
		<u-picker v-if="columns" :show="selectShow" :columns="columns" class="picker" @cancel="selectShow = false" @confirm="selectHandler"></u-picker>
	</view>
</template>

<script>
	export default {
		name: "selectBox",
		props: {
			value: {
				type: String,
			},
			columns: {
				type: Array,
			},
			placeholder: {
				type: String,
				default: "",
			},
		},
		data() {
			return {
				selectShow: false,
			};
		},
		methods: {
			selectHandler(e) {
				const { value } = e;
				if (this.activeYear !== value[0]) {
					this.$emit("input", value[0]);
					this.$emit("change", value[0]);
				}
				this.selectShow = false;
			},
		},
	};
</script>

<style scoped lang="scss">
	.selectBox-wrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		padding: 0rpx 30rpx;
		background-color: #fff;
		border-radius: 10rpx;

		.picker {
			position: absolute;
		}

		.text {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			font-size: 24rpx;
		}

		.value {
			color: #000;
		}

		.placeholder {
			color: #9ca1a8;
		}
	}
</style>