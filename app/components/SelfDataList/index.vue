<template>
  <view class="data-list">
    <view v-if="!data || data.length === 0">
      <u-empty
        text="没有数据"
        marginTop="200"
        icon="/static/images/empty.png"
      ></u-empty>
    </view>
    <view
      class="data-item"
      v-for="(item, index) of data"
      :key="index"
      @click="clickHandler(data)"
    >
      <view class="data-item-line">
        <view v-if="isSelect">
          <u-checkbox
            :checked="selectData.includes(item._id)"
            @change="handleCheckboxChange(item)"
            size="20"
          />
        </view>
        <view class="data-title">
          <view class="title-label">{{ titleField.label }}</view>
          <view
            class="title-value"
            @click.stop="copyText(titleField.copy, setValue(item, titleField))"
            >{{ setValue(item, titleField) }}</view
          >
        </view>
      </view>

      <view class="data-item-fields">
        <view
          class="data-item-field"
          v-if="dataShow(item, field)"
          v-for="(field, index) of fields"
          :key="index"
        >
          <view class="title">{{ field.label }}：</view>
          <view class="value">{{ setValue(item, field) }}</view>
        </view>
      </view>
      <view class="data-item-operations">
        <view
          class="btn-wrapper"
          v-if="operationShow(operation, item)"
          v-for="(operation, index) of operations"
          @click.stop="operationBtnClickHandler(operation.key, item)"
          :key="index"
        >
          <u-button size="mini" :type="operation.type || 'primary'">{{
            operation.label
          }}</u-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "selfDataList",
  props: {
    // 数据
    data: {
      type: Array,
    },
    // 显示字段
    fields: {
      type: Array,
    },
    // 操作按钮
    operations: {
      type: Array,
    },
    cache: {
      type: Boolean,
      default: false,
    },
    titleField: {
      type: Object,
    },
    isSelect: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      userInfo: null,
      selectData: [],
    };
  },
  mounted() {
    this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
  },
  computed: {},
  methods: {
    copyText(copy, data) {
      if (!copy) return;
      uni.setClipboardData({
        data,
        success() {
          uni.showToast({
            title: "复制成功!",
            icon: "success",
          });
        },
      });
    },
    operationShow(operation, item) {
      // 判断角色编辑权限
      if (operation.key != "edit" && operation.key != "delete") return true;
      if (this.userInfo.user_id !== item.create_by) return false;
      // 判断依赖权限
      if (!operation.depends) return true;
      const type = operation.depends[0];
      const field = operation.depends[1];
      const value = operation.depends[2];
      switch (type) {
        case "=":
          return item[field] == value;
        default:
          return true;
      }
    },
    dataShow(item, field) {
      const { dynamic, name } = field;
      if (!dynamic) return true;
      let value = item[name];
      return ![undefined, null, ""].includes(value);
    },
    setValue(item, field) {
      let value;
      if (this.cache) {
        value = item.content[field.name];
      } else {
        value = item[field.name];
      }
      if ([undefined, null, ""].includes(value)) return value;
      switch (field.type) {
        case "text":
          return value;
        case "date":
          return this.$utils.date.format(value);
        case "json":
          return this.jsonFormat(item, field);
        default:
          return value;
      }
    },
    jsonFormat(item, field) {
      let array = item[field.name];
      return array.join("、");
    },

    operationBtnClickHandler(key, item) {
      this.$emit(key, item);
    },

    clickHandler(item) {
      this.$emit(this.$events.list.ITEM_CLICK, item);
    },
    selectHandler(item) {
      if (this.selectData) {
        if (!this.selectData.includes(item._id)) {
          this.selectData.push(item._id);
        } else {
          this.selectData = this.selectData.filter((id) => id !== item._id);
        }
      } else {
        this.selectData = [item._id];
      }
      this.$emit("select", this.selectData);
    },

    // 选择或取消选择
    handleCheckboxChange(item) {
      if (this.selectData.includes(item._id)) {
        this.selectData = this.selectData.filter((id) => id !== item._id);
      } else {
        this.selectData.push(item._id);
      }
      this.$emit("select", this.selectData);
    },

    resetPointList() {
      this.selectData = [];
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

.data-list {
  padding-bottom: 30px;

  .data-item {
    padding: 30rpx;
    border-radius: 16rpx;
    background-color: #fff;
    margin-top: 30rpx;

    .data-item-line {
      display: flex;
      border-bottom: 2rpx solid $gt-border-color;

      .data-title {
        display: flex;
        align-items: center;
        height: 70rpx;
        box-sizing: border-box;
        padding-bottom: 30rpx;

        .title-label {
          font-weight: 700;
          font-size: 28rpx;
          margin-right: 20rpx;
        }

        .title-value {
          font-size: 28rpx;
        }
      }
    }

    .data-item-field {
      padding: 2px 10rpx;
      line-height: 50rpx;
      box-sizing: border-box;

      .title {
        display: inline-block;
      }

      .value {
        display: inline-block;
      }
    }

    .data-item-operations {
      width: 100%;
      margin-top: 10rpx;
      display: flex;
      justify-content: flex-end;
      // column-gap: 6px;

      .btn-wrapper {
        margin-left: 30rpx;

        /deep/.u-button--mini {
          width: 140rpx;
          height: 60rpx;
        }
      }

      // .u-button {
      //  width: 160rpx;
      //  height: 68rpx;
      //  margin-left: 20rpx;
      // }
    }
  }
}
</style>
