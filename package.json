{"name": "gt-mis-uniapp", "version": "1.0.0", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "http://**************/mis/gt-mis-uniapp.git"}, "author": "", "license": "ISC", "dependencies": {"axios": "^0.26.1", "axios-miniprogram-adapter": "^0.3.2", "crypto-js": "^4.1.1", "dayjs": "^1.11.0", "dotenv": "^16.0.1", "lodash": "^4.17.21", "moment": "^2.29.1", "omit-deep-lodash": "^1.1.7", "uid": "^2.0.0", "uview-ui": "^2.0.38"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "description": "", "–minimize": {"dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize"}}