{"name": "gt-mis-uniapp", "version": "1.0.0", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky install"}, "repository": {"type": "git", "url": "http://**************/mis/gt-mis-uniapp.git"}, "author": "", "license": "ISC", "dependencies": {"axios": "^0.26.1", "axios-miniprogram-adapter": "^0.3.2", "crypto-js": "^4.1.1", "dayjs": "^1.11.0", "dotenv": "^16.0.1", "gt-mis-app-components": "^1.2.8", "lodash": "^4.17.21", "moment": "^2.29.1", "omit-deep-lodash": "^1.1.7", "uid": "^2.0.0", "uview-ui": "^2.0.35"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "description": "", "–minimize": {"dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/eslint-parser": "^7.19.1", "eslint": "^8.36.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-ali": "^14.0.2", "eslint-config-prettier": "^8.7.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.9.0", "prettier": "^2.8.4", "vue-eslint-parser": "^9.1.0"}, "lint-staged": {"*.js": "eslint --cache --fix"}}