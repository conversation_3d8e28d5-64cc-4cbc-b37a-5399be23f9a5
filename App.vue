<script>
	import Vue from "vue";
	import FormDataLocal from "@/apis/formDataLocal";

	export default {
		async onLaunch() {
			uni.getStorage({
				key: "refresh_token",
				success: (res) => {
					console.log(res);
				},
				fail: (err) => {
					console.log(err);
					uni.clearStorageSync();
					uni.reLaunch({
						url: "/pages/Login/index",
					});
				},
			});
			// 关闭本地缓存数据库
			await FormDataLocal.closeDB();
		},
		methods: {},
	};
</script>

<style lang="scss">
	@import "uview-ui/index.scss";

	html,
	body {
		background-color: #fff;
		overflow-x: hidden;
	}

	body::-webkit-scrollbar {
		display: none;
	}

	::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
		color: transparent;
	}
</style>