
import formData from './formData'
import env from '@/env'

const TASK_FORM_UID = '任务表'

export default {

	getThumbUrl ( formName ) {
		return `${ env.API_URL }formDesign/${ formName }/thumb`
	},

	async getTaskList (filter = []) {
		let result = await formData.getFormRecords( TASK_FORM_UID, {
			filter
		} )
		if ( !result || !result.list ) return
		const list = result.list
		if ( list.length === 0 ) return []
		// 数据处理
		return list
	}

}