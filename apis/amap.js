import axios from "axios";
import env from "@/env";

const URL = env.AMAP_GEOCODE_API_URL;

const AMAP = axios.create();

export default {
  // 获取位置信息
  async getLocation(lon, lat) {
    const url = `${URL}?lon=${lon}&lat=${lat}`;
    try {
      const result = await AMAP.get(url);
      console.log(result);
      if (!result) return;
      const res = result.data?.data;
      if (!res) return {};
      let {
        provinceCode,
        provinceName,
        cityCode,
        cityName,
        countyCode,
        countyName,
        townCode,
        townName,
      } = res;
      provinceCode = provinceCode ? provinceCode.substr(0, 2) : provinceCode;
      cityCode = cityCode ? cityCode.substr(0, 4) : cityCode;
      countyCode = countyCode ? countyCode.substr(0, 6) : countyCode;
      townCode = townCode ? townCode.substr(0, 9) : townCode;
      const address =
        (provinceName ? provinceName : "") +
        (cityName ? cityName : "") +
        (countyName ? countyName : "") +
        (townName ? townName : "");
      return {
        // 地址
        address,
        // 省
        provinceName,
        provinceCode,
        // 市
        cityName,
        cityCode,
        // 区县
        countyName,
        countyCode,
        // 乡镇
        townName,
        townCode,
      };
    } catch (err) {
      console.log("获取地址错误", err);
      return {};
    }
  },
};
