import request from "./ajax";
import { FORM_DATA_URL, FORM_DESIGN_URL, STS_URL } from "./url";
import FormDataLocalAPI from "./formDataLocal";

export default {
  /**
   * 查询表单的数据列表
   * @param {*} formId 表单的uid
   * @returns
   */
  async getFormRecords(formId, param) {
    if (!param || param.resolveSubItems !== false) {
      param = Object.assign({ resolveSubItems: true }, param);
    }
    return await request.post(FORM_DATA_URL.LIST(formId), param);
  },

  /**
   * 查询表单的某条数据详情
   * @param {*} formId 表单的uid
   * @param {*} recordId 表单数据的uid
   * @returns
   */
  async getFormRecord(formId, recordId) {
    return await request.get(FORM_DATA_URL.GET(formId, recordId));
  },

  /**
   * 关联表单数据查询
   * @param {*} formUid 表名
   * @param {*} param 查询参数
   * @returns
   */

  async joinSearchFormData(formUid, param) {
    return await request.post(FORM_DATA_URL.JOIN_SEARCH(formUid), param);
  },

  /**
   * 表单数据更新接口
   * @param {*} formId    表单ID
   * @param {*} recordId  表单数据ID
   * @param {*} record    表单数据
   * @param {*} cacheId   缓存ID
   * @returns
   */
  async updateFormRecord(formId, recordId, record, cacheId) {
    // 先更新本地数据
    const newCacheId = await FormDataLocalAPI.upsertCacheRecord(
      formId,
      cacheId,
      record
    );
    //if(!newCacheId) return false
    // 同步到云端
    try {
      await request.put(FORM_DATA_URL.UPDATE(formId, recordId), record);
      // 更新缓存状态为已同步
      await FormDataLocalAPI.syncCacheRecord(formId, newCacheId);
      return true;
    } catch (e) {
      console.log("新增表单数据失败：", e);
      return Promise.reject(e);
    }
    return false;
  },

  /**
   * 表单数据删除接口
   * @param {*} formId 表单的id
   * @param {*} recordId 表单数据的id
   * @returns
   */
  async deleteFormRecord(formId, recordId) {
    return await request.delete(FORM_DATA_URL.DELETE(formId, recordId));
  },

  /**
   * 表单数据添加接口
   * @param {*} formId  表单ID
   * @param {*} record  表单数据
   * @param {*} cacheId 缓存ID
   * @returns
   */
  async addFormRecord(formId, record, cacheId) {
    const newCacheId = await FormDataLocalAPI.upsertCacheRecord(
      formId,
      cacheId,
      record
    );
    //if(!newCacheId) return false
    try {
      // 同步到云端
      const res = await request.post(FORM_DATA_URL.ADD(formId), record);
      // 更新缓存状态为已同步
      await FormDataLocalAPI.syncCacheRecord(formId, newCacheId);
      return res;
    } catch (e) {
      console.log("新增表单数据失败：", e);
      return Promise.reject(e);
    }
    return false;
  },

  /**
   * 表单数据批量添加接口
   * @param {*} formName 表名
   * @param {*} formData 表单数据
   * @returns
   */

  async batchAddFormData(formName, formDataList) {
    return await request.post(FORM_DATA_URL.BATCH(formName), formDataList);
  },

  /**
   * 表单数据批量删除接口
   * @param {*} formName 表名
   * @param {*} formData 表单数据
   * @returns
   */

  async batchDeleteFormData(formName, ids) {
    return await request.delete(FORM_DATA_URL.BATCH(formName), {
      data: {
        dataIds: ids,
      },
    });
  },

  /**
   * 表单数据统计
   */
  async aggregationFormData(formUid, param) {
    return await request.post(FORM_DATA_URL.AGGREGATION(formUid), param);
  },

  /**
   * 表单数据状态更新
   * @param {*} formName 表名
   * @param {*} dataId 单条数据id
   * @param {*} param 查询参数
   */
  async updataFormRecordState(formName, dataId, param) {
    return await request.put(
      FORM_DATA_URL.UPDATE_STATE(formName, dataId),
      param
    );
  },

  /**
   * 表单数据批量修改状态
   * @param {*} formName 表名
   * @returns
   */
  async batchUpdateState(formName, data) {
    return await request.post(FORM_DATA_URL.BATCH_STATE(formName), data);
  },
};
