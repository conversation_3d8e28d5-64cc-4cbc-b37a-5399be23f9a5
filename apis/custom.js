import request from "./ajax";
import { CUSTOM_DATA_URL, FORM_DESIGN_URL, STS_URL } from "./url";
import FormDataLocalAPI from "./formDataLocal";

export default {
	/**
	 * 同步上次核查记录
	 * @param {*} fcode 县编码
	 * @param {*} oldTaskId 需要同步的任务id
	 * @param {*} newTaskId 正在进行的任务id
	 * @param {*} oldInfoId 需要同步的数据id
	 * @param {*} plotType 地块类型
	 * @returns
	 */
	async synchroInfo(fcode, param) {
		return await request.post(`custom/form/${fcode}/synchroInfo`, param);
	},
	/**
	 * 查询指定范围的地块核查记录(施工日志)
	 * @param {*} fcode 县编码
	 * @param {*} projectId 任务id
	 * @param {*} lonMax 最大经度
	 * @param {*} lonMin 最小经度
	 * @param {*} latMax 最大纬度
	 * @param {*} latMin 最小纬度
	 * @returns
	 */
	async getRecordPlotInRange(fcode, param) {
		return await request.post(
			`project/form/${fcode}/getRecordPlotInRange`,
			param
		);
	},
	/**
	 * 查询指定范围的地块
	 * @param {*} fcode 县编码
	 * @param {*} taskId 任务id
	 * @param {*} lonMax 最大经度
	 * @param {*} lonMin 最小经度
	 * @param {*} latMax 最大纬度
	 * @param {*} latMin 最小纬度
	 * @returns
	 */
	async getPlotInRange(fcode, param) {
		return await request.post(`custom/form/${fcode}/getPlotInRange`, param);
	},
	/**
	 * 查询地块核查记录详情
	 * @param {*} fcode 县编码
	 * @param {*} taskId 任务id
	 * @param {*} pageSize 条数
	 * @param {*} pageNum 页数
	 * @param {*} tcode 镇编码
	 * @param {*} vcode 村编码
	 * @param {*} verify 核查类型 true:已核查, false:未核查
	 * @param {*} plotCode 地块编码
	 * @param {*} landType
	 * @param {*} plotType 地块类型
	 * @param {*} cropType 种植类型
	 * @returns
	 */
	async getPlotRecord(fcode, param) {
		return await request.post(`custom/form/${fcode}/selectPlotRecord`, param);
	},

	/**
	 * 查询表单的数据列表
	 * @param {*} fcode 县编码
	 * @param {*} formId 表单的uid
	 * @returns
	 */
	async getFormRecords(fcode, formId, param) {
		if (!param || param.resolveSubItems !== false) {
			param = Object.assign({ resolveSubItems: true }, param);
		}
		return await request.post(CUSTOM_DATA_URL.LIST(fcode, formId), param);
	},

	/**
	 * 查询表单的某条数据详情
	 * @param {*} fcode 县编码
	 * @param {*} formId 表单的uid
	 * @param {*} recordId 表单数据的uid
	 * @returns
	 */
	async getFormRecord(fcode, formId, recordId) {
		return await request.get(CUSTOM_DATA_URL.GET(fcode, formId, recordId));
	},

	/**
	 * 关联表单数据查询
	 * @param {*} fcode 县编码
	 * @param {*} formId 表名
	 * @param {*} param 查询参数
	 * @returns
	 */

	async joinSearchFormData(fcode, formId, param) {
		return await request.post(
			CUSTOM_DATA_URL.JOIN_SEARCH(fcode, formId),
			param
		);
	},

	/**
	 * 表单数据更新接口
	 * @param {*} fcode 县编码
	 * @param {*} formId    表单ID
	 * @param {*} recordId  表单数据ID
	 * @param {*} record    表单数据
	 * @param {*} cacheId   缓存ID
	 * @returns
	 */
	async updateFormRecord(fcode, formId, recordId, record, cacheId) {
		// 先更新本地数据
		const newCacheId = await FormDataLocalAPI.upsertCacheRecord(
			formId,
			cacheId,
			record
		);
		//if(!newCacheId) return false
		// 同步到云端
		try {
			await request.put(
				CUSTOM_DATA_URL.UPDATE(fcode, formId, recordId),
				record
			);
			// 更新缓存状态为已同步
			await FormDataLocalAPI.syncCacheRecord(formId, newCacheId);
			return true;
		} catch (e) {
			return Promise.reject(e);
		}
		return false;
	},
	/**
	 * 表单数据批量更新数据
	 * @param {*} fcode 县编码
	 * @param {*} formId 表名
	 * @returns
	 */
	async batchUpdateData(fcode, formId, data) {
		return await request.post(
			CUSTOM_DATA_URL.BATCH_UPDATE(fcode, formId),
			data
		);
	},

	/**
	 * 表单数据删除接口
	 * @param {*} fcode 县编码
	 * @param {*} formId 表单的id
	 * @param {*} recordId 表单数据的id
	 * @returns
	 */
	async deleteFormRecord(fcode, formId, recordId) {
		return await request.delete(
			CUSTOM_DATA_URL.DELETE(fcode, formId, recordId)
		);
	},

	/**
	 * 表单数据添加接口
	 * @param {*} fcode 县编码
	 * @param {*} formId  表单ID
	 * @param {*} record  表单数据
	 * @param {*} cacheId 缓存ID
	 * @returns
	 */
	async addFormRecord(fcode, formId, record, cacheId) {
		const newCacheId = await FormDataLocalAPI.upsertCacheRecord(
			formId,
			cacheId,
			record
		);
		//if(!newCacheId) return false
		try {
			// 同步到云端
			const res = await request.post(
				CUSTOM_DATA_URL.ADD(fcode, formId),
				record
			);
			// 更新缓存状态为已同步
			await FormDataLocalAPI.syncCacheRecord(formId, newCacheId);
			return res;
		} catch (e) {
			console.log("新增表单数据失败：", e);
			return Promise.reject(e);
		}
		return false;
	},

	/**
	 * 表单数据批量添加接口
	 * @param {*} fcode 县编码
	 * @param {*} formId 表名
	 * @param {*} formData 表单数据
	 * @returns
	 */

	async batchAddFormData(fcode, formId, formDataList) {
		return await request.post(
			CUSTOM_DATA_URL.BATCH(fcode, formId),
			formDataList
		);
	},

	/**
	 * 表单数据统计
	 * @param {*} fcode 县编码
	 */
	async aggregationFormData(fcode, formId, param) {
		return await request.post(
			CUSTOM_DATA_URL.AGGREGATION(fcode, formId),
			param
		);
	},

	/**
	 * 表单数据状态更新
	 * @param {*} fcode 县编码
	 * @param {*} formId 表名
	 * @param {*} dataId 单条数据id
	 * @param {*} param 查询参数
	 */
	async updataFormRecordState(fcode, formId, dataId, param) {
		return await request.put(
			CUSTOM_DATA_URL.UPDATE_STATE(fcode, formId, dataId),
			param
		);
	},

	/**
	 * 表单数据批量修改状态
	 * @param {*} fcode 县编码
	 * @param {*} formId 表名
	 * @returns
	 */
	async batchUpdateState(fcode, formId, data) {
		return await request.post(CUSTOM_DATA_URL.BATCH_STATE(fcode, formId), data);
	},

	/**
	 * 批量删除表单数据
	 * @param {*} fcode 县编码
	 * @param {*} formId 表名
	 * @param {*} data 表单数据
	 */
	async batchDeleteFormData(fcode, formId, data) {
		return await request.post(
			CUSTOM_DATA_URL.BATCH_DELETE(fcode, formId),
			data
		);
	},
};