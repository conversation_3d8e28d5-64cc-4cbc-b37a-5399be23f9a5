import { v4 as uuidv4 } from 'uuid'

import SQLITE from './sqlite'

// 表单数据库（读写）信息
const DB_NAME = 'form_cache'
const DB_PATH = '_doc/form_cache.db'

const RECORD_STATUS = {
	TEMP: 0,
	SYNC: 1
}

export default {

	/**
	 * 创建表单数据表
	 * id            缓存ID
	 * cloud_id      云端表单数据ID
	 * content       数据内容
	 * status        是否已与云端同步
	 *
	 * @param {Object} formId
	 */
	_getCreateTableSql(formId){
		return `CREATE TABLE IF NOT EXISTS ${formId} ("id" TEXT PRIMARY KEY, "content" TEXT, "status" INTEGER DEFAULT 0, "create_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP)`
	},

	_getInsertSql(formId, record, newRecordId){
		// record转成字符串
		const content = (typeof record === 'object') ? JSON.stringify(record) : record
		// Sqlite生成UUID
		return `INSERT INTO ${formId} (id, content, status) VALUES('${newRecordId}', '${content}', ${RECORD_STATUS.TEMP})`
	},

	_getUpdateSql(formId, recordId, record){
		const content = (typeof record === 'object') ? JSON.stringify(record) : record
		return `UPDATE ${formId} SET content='${content}' WHERE id='${recordId}'`
	},

	_genUID(){
		return uuidv4().replace(/-/g, '')
	},

	/**
	 * 新增暂存的表单数据
	 * @param {String} formId 表单ID
	 * @param {Object} record 记录内容
	 */
	async addCacheRecord(formId, record){
		console.log(formId, record);
		if(!formId || !record) return
		try{
			await SQLITE.openDB(DB_NAME, DB_PATH)
			const tableSql = this._getCreateTableSql(formId)
			// 确保表存在
			await SQLITE.existsOrCreateTable(DB_NAME, formId, tableSql)
			const newRecordId = this._genUID()
			const sql = this._getInsertSql(formId, record, newRecordId)
			await SQLITE.executeSQL(DB_NAME, sql)
			return newRecordId
		}
		catch(e){
			console.log('新增表单数据失败:', e)
		}
	},

	/**
	 * 更新暂存的表单数据
	 * @param {String} formId    表单ID
	 * @param {String} recordId  记录ID
	 * @param {Object} record    记录内容
	 */
	async updateCacheRecord(formId, recordId, record){
		if(!formId || !record ||!recordId) return
		try{
			await SQLITE.openDB(DB_NAME, DB_PATH)
			const sql = this._getUpdateSql(formId, recordId, record)
			return await SQLITE.executeSQL(DB_NAME, sql)
		}
		catch(e){
			console.log('更新表单数据失败:', e)
		}
	},

	/**
	 * 更新暂存的表单数据
	 * @param {String} formId    表单ID
	 * @param {String} recordId  记录ID
	 * @param {Object} record    记录内容
	 */
	async upsertCacheRecord(formId, recordId, record){
		if(!formId || !record) return
		try{
			let res = await SQLITE.openDB(DB_NAME, DB_PATH)
			const tableSql = this._getCreateTableSql(formId)
			// 确保表存在
			await SQLITE.existsOrCreateTable(DB_NAME, formId, tableSql)
			const newRecordId = this._genUID()
			const sql = !recordId ? this._getInsertSql(formId, record, newRecordId) : this._getUpdateSql(formId, recordId, record)
			await SQLITE.executeSQL(DB_NAME, sql)
			return recordId || newRecordId
		}
		catch(e){
			console.log('UPSERT表单数据失败:', e)
		}
	},

	/**
	 * 删除暂存的表单数据
	 * @param {String} formId    表单ID
	 * @param {String} recordId  记录ID
	 */
	async delCacheRecord(formId, recordId){
		if(!formId || !recordId) return
		try{
			await SQLITE.openDB(DB_NAME, DB_PATH)
			const sql = `DELETE FROM ${formId} WHERE id='${recordId}'`
			return await SQLITE.executeSQL(DB_NAME, sql)
		}
		catch(e){
			console.log('删除表单数据失败:', e)
		}
	},

	/**
	 * 查询暂存的表单数据
	 * @param {String} formId    表单ID
	 */
	async getCacheRecords(formId){
		if(!formId) return
		try{
			await SQLITE.openDB(DB_NAME, DB_PATH)
			const tableSql = this._getCreateTableSql(formId)
			// 确保表存在
			await SQLITE.existsOrCreateTable(DB_NAME, formId, tableSql)
			const sql = `SELECT id, content FROM ${formId} WHERE status=${RECORD_STATUS.TEMP}`
			const list = await SQLITE.selectSQL(DB_NAME, sql)
			if(!list || !Array.isArray(list)) return
			return list.map(item => {
				return {
					id: item.id,
					content: JSON.parse(item.content)
				}
			})
		}
		catch(e){
			console.log('查询表单数据失败:', e)
		}
	},

	/**
	 * 查询暂存的表单数据
	 * @param {String} formId    表单ID
	 * @param {String} recordId  记录ID
	 */
	async getCacheRecord(formId, recordId){
		if(!formId || !recordId) return
		try{
			await SQLITE.openDB(DB_NAME, DB_PATH)
			const tableSql = this._getCreateTableSql(formId)
			// 确保表存在
			await SQLITE.existsOrCreateTable(DB_NAME, formId, tableSql)
			const sql = `SELECT id, content FROM ${formId} WHERE id='${recordId}' AND status=${RECORD_STATUS.TEMP} LIMIT 1`
			const list = await SQLITE.selectSQL(DB_NAME, sql)
			if(list && Array.isArray(list)){
				const record = list[0]
				record.content = !record.content ? null : JSON.parse(record.content)
				return record
			}
		}
		catch(e){
			console.log('查询表单数据失败:', e)
		}
	},

	async countCacheRecords(formId){
		if(!formId) return
		try{
			await SQLITE.openDB(DB_NAME, DB_PATH)
			const tableSql = this._getCreateTableSql(formId)
			// 确保表存在
			await SQLITE.existsOrCreateTable(DB_NAME, formId, tableSql)
			const sql = `SELECT count(*) AS count FROM ${formId} WHERE status=${RECORD_STATUS.TEMP}`
			const result = await SQLITE.selectSQL(DB_NAME, sql)
			return result && result[0] && result[0].count
		}
		catch(e){
			console.log('查询表单数据失败:', e)
		}
	},

	/**
	 * 更新暂存的表单数据状态为“已同步”
	 * @param {Object} formId
	 * @param {Object} recordId
	 */
	async syncCacheRecord(formId, recordId){
		if(!formId || !recordId) return
		try{
			await SQLITE.openDB(DB_NAME, DB_PATH)
			// record转成字符串
			const sql = `UPDATE ${formId} SET status=${RECORD_STATUS.SYNC} WHERE id='${recordId}'`
			console.log(sql);
			return await SQLITE.executeSQL(DB_NAME, sql)
		}
		catch(e){
			console.log('更新表单数据状态失败:', e)
		}
	},

	async closeDB(){
		try{
			await SQLITE.closeDB(DB_NAME)
		}
		catch(e){
			console.log(`关闭数据库${DB_NAME}错误：${e}`)
		}
	}
}
