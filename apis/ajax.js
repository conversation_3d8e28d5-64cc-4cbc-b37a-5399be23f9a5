import axios from 'axios'
import mpAdapter from 'axios-miniprogram-adapter'
import clientBasic from "./client"
import loginRequest from "./login"
import { LOGIN_URL } from "./url"
import env from '@/env'

axios.defaults.adapter = mpAdapter
axios.defaults.baseURL = env.API_URL
axios.defaults.timeout = 10000

axios.defaults.withCredentials = true
axios.defaults.validateStatus = (status) => status === 200
axios.defaults = {
	timeout: 10000,
	headers: {
		'Content-Type': 'application/json',
	}
}

/**
 * 添加请求拦截器
 */
axios.interceptors.request.use(
	function(config) {
		const { url } = config
		// 如果是登录和刷新token，添加专用token
		// 否则使用正常的 token
		if ([LOGIN_URL.LOGIN].includes(url)) {
			config.headers.Authorization = `Basic ${ clientBasic }`
		} else {
			config.headers.Authorization = `Bearer ${ uni.getStorageSync("access_token") }`
		}
		return config
	},
	function(error) {
		return Promise.reject(error)
	}
)

/**
 * 添加响应拦截器
 */
axios.interceptors.response.use(
	function(response) {
		if (response.request.responseType === "blob") {
			return response.data
		}

		const { data, code, msg } = response.data

		if (code === 2000) {
			return Promise.resolve(data)
		}

		// token失效，尝试使用refresh_token刷新token
		if (code === 4001) {
			const refreshToken = uni.getStorageSync("refresh_token")
			if (!refreshToken) {
				return Promise.reject(new Error(msg))
			}
			loginRequest.refreshToken()
		} else {
			return Promise.reject(msg)
		}
	},
	function(error) {
		if (error.response.status === 401) {
			const refreshToken = uni.getStorageSync("refresh_token");
			if (!refreshToken) {
				return Promise.reject(new Error(error));
			}
			loginRequest.refreshToken();
		} else {
			return Promise.reject(error);
		}
	}
)

export default {
	//GET
	async get(url, params) {
		return await axios.get(url, { params })
	},
	// POST
	async post(url, data = {}) {
		return await axios.post(url, data)
	},
	// PUT
	async put(url, data) {
		console.log(url, data);
		return await axios.put(url, data)
	},
	// DELETE
	async delete(url, params) {
		return await axios.delete(url, { params })
	}
}