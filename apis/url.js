export const LOGIN_URL = {
  LOGIN: "oauth/token",
  LOGOUT: "oauth/logout",
};

export const USER_URL = {
  EDITPSD: "user/modifyPassword",
  GETUSERINFO: "user/info",
  SMS: "sms/register",
  REGISTER: "custom/user/register",
};

/**
 * 表单定义操作
 */
export const FORM_DESIGN_URL = {
  GET: (formUid) => `formDesign/${formUid}`,
};

/**
 * 表单数据操作
 */
export const FORM_DATA_URL = {
  ADD: (formUid) => `form/${formUid}`,
  BATCH: (formName) => `form/${formName}/batch`,
  LIST: (formUid) => `form/${formUid}/search`,
  GET: (formUid, formRecordUid) =>
    `form/${formUid}/${formRecordUid}?resolveSubItems=true`,
  UPDATE: (formUid) => `form/${formUid}`,
  DELETE: (formUid, formRecordUid) => `form/${formUid}/${formRecordUid}`,
  TABLE: () => `resource/current/table`,
  AGGREGATION: (formUid) => `form/${formUid}/aggregation`,
  JOIN_SEARCH: (formUid) => `form/${formUid}/joinSearch`,
  UPDATE_STATE: (formUid, dataId) => `custom/form/${formUid}/${dataId}/state`, // 定制接口: 状态更新
  BATCH_STATE: (formName) => `custom/form/${formName}/state/batchUpdate`,
};

export const CUSTOM_DATA_URL = {
  ADD: (fcode, formUid) => `custom/form/${fcode}/${formUid}`,
  BATCH: (fcode, formName) => `custom/form/${fcode}/${formName}/batch`,
  LIST: (fcode, formUid) => `custom/form/${fcode}/${formUid}/search`,
  GET: (fcode, formUid, formRecordUid) =>
    `custom/form/${fcode}/${formUid}/${formRecordUid}?resolveSubItems=true`,
  UPDATE: (fcode, formUid) => `custom/form/${fcode}/${formUid}`,
  DELETE: (fcode, formUid, formRecordUid) =>
    `custom/form/${fcode}/${formUid}/${formRecordUid}`,
  JOIN_SEARCH: (fcode, formUid) => `custom/form/${fcode}/${formUid}/joinSearch`,
  UPDATE_STATE: (fcode, formUid, dataId) =>
    `custom/form/${fcode}/${formUid}/${dataId}/state`,
  BATCH_STATE: (fcode, formName) =>
    `custom/form/${fcode}/${formName}/state/batchUpdate`,
  BATCH_UPDATE: (fcode, formName) =>
    `custom/form/${fcode}/${formName}/batchUpdate`,

  BATCH_DELETE: (fcode, formName) =>
    `custom/form/${fcode}/${formName}/batchDelete`,
};

/**
 * 获取上传sts token
 */
export const STS_URL = {
  GET: "sts/getTempToken",
};
