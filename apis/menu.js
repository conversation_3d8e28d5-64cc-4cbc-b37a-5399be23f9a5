
import formData from './formData'
import env from '@/env'

const TASK_FORM_UID = 'app_menu_table'
const OUT_FIELDS = ['menu_name', 'task_type', 'menu_roles', 'menu_index', 'menu_image', 'page_url']

export default {

  async getMenuList () {
    let result = await formData.getFormRecords( TASK_FORM_UID, {
      outFields: OUT_FIELDS
    } )
    if ( !result || !result.list ) return
    const list = result.list.sort( ( a, b ) => {
      return a.menu_index - b.menu_index
    } )
    return list
  }

}