<template>
  <view class="wrapper">
    <u-navbar title="工作台" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder></u-navbar>
    <view> </view>
  </view>
</template>

<script>
import BasePage from "@/pages/BasePage";

export default {
  mixins: [BasePage],

  data() {
    return {
      taskList: [],
      emptyVisible: false,
    };
  },

  onLoad() {},

  methods: {
    async getTaskList() {
      let result = await this.$apis.task.getTaskList();
      if (!result) {
        return;
      }
      this.taskList = result;
      this.emptyVisible = this.taskList.length === 0;
    },

    taskClickHandler(formUid) {
      const pageUrl = `${this.$constants.PAGE.FORM_LIST_URL}?formUid=${formUid}`;
      this.navigateTo(pageUrl);
    },

    fillOutFormHandler(formUid) {
      const pageUrl = `${this.$constants.PAGE.FORM_PAGE_URL}?formUid=${formUid}`;
      this.navigateTo(pageUrl);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .u-navbar__content__left {
  display: none;
}

uni-page-body {
  height: 100%;
}

.wrapper {
  height: 100%;
}

.empty-wrapper {
  height: calc(100% - 128px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  padding: 12px;
  .task-group {
    .task-group-title {
      display: flex;

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
      }
    }
  }

  .task-item {
    display: flex;
    column-gap: 12px;
    border: 1px solid $gt-light-border-color;
    margin: 8px 0;
    padding: 12px;
    border-radius: $gt-border-radius;
    position: relative;

    .task-item-thumb {
      width: 64px;
      height: 64px;
      border: 1px solid $gt-light-border-color;
      display: flex;
      align-items: center;
      justify-content: center;

      .thumb-image {
        width: 64px;
        height: 64px;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
      }
    }

    .task-item-content {
      padding-right: 64px;
      position: relative;

      .task-item-info {
        position: absolute;
        bottom: 0;
        color: $gt-light-color;
        font-size: 14px;
      }
    }

    .task-item-operation {
      position: absolute;
      right: 12px;
      top: 0;
      height: 100%;
      display: flex;
      align-items: center;

      .u-button {
        height: $gt-btn-height;
      }
    }
  }
}
</style>
