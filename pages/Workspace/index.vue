<template>
  <view class="wrapper">
    <u-navbar
      :title="`${this.menuName || ''}任务列表`"
      @leftClick="leftClickHandler"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
    ></u-navbar>
    <view class="empty-wrapper" v-if="emptyVisible">
      <u-empty
        text="当前用户未分配任务, 请联系管理员"
        icon="/static/images/empty.png"
      ></u-empty>
    </view>

    <view class="content">
      <view
        class="task-item"
        v-for="(task, index) in taskList"
        :key="index"
        @click="taskClickHandler(task)"
      >
        <view class="task-item-title-wrapper">
          <u-icon name="calendar-fill" color="#FF7A45" size="80rpx"></u-icon>
          <view class="task-item-title">{{ task.name}}</view>
        </view>
          <view class="task-item-info">
            <view
              class="task-item-info-content"
              v-for="item in taskItemInfo"
              :key="item.key"
            >
              {{
                `${item.label}: ${
                  item.key === "create_time"
                    ? $utils.date.format(task[item.key])
                    : task[item.key]
                }`
              }}
            </view>
          </view>
          <view class="task-item-operation">
            <view
              class="btn-task-item"
              v-for="btn in getTaskButtonList(task)"
              :key="btn.key"
              @tap.stop="fillOutFormHandler(task, btn.key)"
            >
              <u-button
                :disabled="btn.disabled"
                :icon="btn.icon"
                :type="btn.type"
                :text="btn.label"
                shape="circle"
                plain
              ></u-button>
            </view>
          </view>
      </view>
    </view>
  </view>
</template>

<script>
const TASK_TYPE = {
  氮磷流失调查: "dl",
  农用薄膜监测: "dm",
};

const TASK_ITEM_INFO = [
  // { label: "年份", key: "年份" },
  { label: "开始日期", key: "create_time" },
];
const TASK_STATUS = ["未开始", "进行中", "已结束"];

import BasePage from "@/pages/BasePage";
import TabPage from "@/pages/TabPage";

export default {
  mixins: [BasePage, TabPage],

  data() {
    return {
      taskList: [],
      taskItemInfo: TASK_ITEM_INFO,
      emptyVisible: false,
      userInfo: null,
      menuName: null,
    };
  },

  onLoad(options) {
    this.menuName = options.menuName;
    this.getTaskList();
  },

  methods: {
    async getTaskList() {
      const filter = this.getTaskFilter();

      let result = await this.$apis.task.getTaskList(filter);
      if (!result) {
        return;
      }
      this.taskList = result;
      this.emptyVisible = this.taskList.length === 0;
    },

    getTaskFilter() {
      return [
        "and",
        ["=", "业务类型", TASK_TYPE[this.menuName]],
        ["=", "status", "进行中"],
      ];
    },

    getTaskButtonList(task) {
      switch (this.menuName) {
        case Object.keys(TASK_TYPE)[0]: {
          const { status } = task;
          if (!status) return;
          switch (status) {
            case TASK_STATUS[0]: {
              return [
                { label: "填报", key: "add", disabled: true, icon:"edit-pen" },
                { label: "查看", key: "readonly", disabled: true, icon:"file-text" },
              ];
            }
            case TASK_STATUS[1]: {
              return [
                { label: "填报", key: "add", disabled: false, icon:"edit-pen", type:"primary" },
                { label: "查看", key: "readonly", disabled: false, icon:"file-text", type:"info" },
              ];
            }
            case TASK_STATUS[2]: {
              return [
                { label: "填报", key: "add", disabled: true, icon:"edit-pen" },
                { label: "查看", key: "readonly", disabled: false, icon:"file-text" },
              ];
            }
          }
          break;
        }
        case Object.keys(TASK_TYPE)[1]: {
          return [{ label: "进入", key: "goTo", disabled: false, icon:"edit-pen", type:"primary" }];
          break;
        }
      }
    },

    taskClickHandler(task) {
      switch (this.menuName) {
        case Object.keys(TASK_TYPE)[0]: {
          const { 调查表_uid, _id, 年份 } = task;
          const pageUrl = `${this.$constants.PAGE.FORM_LIST_URL}?formUid=${调查表_uid}&任务_uid=${_id}&年份=${年份}`;
          this.navigateTo(pageUrl);
          break;
        }
        case Object.keys(TASK_TYPE)[1]: {
          this.navigatePointListHandler(task);
          break;
        }
      }
    },

    navigatePointListHandler(task) {
      const { 子任务表_uid, _id, 任务类型 } = task;
      const pageUrl = `${this.$constants.PAGE.POINT_LIST_URL}?子任务表_uid=${子任务表_uid}&任务_uid=${_id}&任务类型=${任务类型}&menuName=${this.menuName}`;
      this.navigateTo(pageUrl);
    },

    fillOutFormHandler(task, key) {
      if (!key) return;
      switch (key) {
        case "add": {
          const { status } = task;
          if (status === TASK_STATUS[1]) {
            const { 调查表_uid, _id, 年份 } = task;
            const pageUrl = `${this.$constants.PAGE.FORM_PAGE_URL}?formUid=${调查表_uid}&任务_uid=${_id}&年份=${年份}`;
            this.navigateTo(pageUrl);
            break;
          }
        }
        case "readonly": {
          this.taskClickHandler(task);
          break;
        }
        case "goTo": {
          this.navigatePointListHandler(task);
          break;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";
/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
}
.wrapper {
  position: fixed;
  width: calc(100% - 40rpx);
  height: 100vh;
  background: #f5f5f9;
  padding: 20rpx;

  .content {
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 120rpx);
    overflow: auto;
    .task-item {
      padding: 8rpx;
      margin: 0 5rpx 20rpx 5rpx;
      border-radius: 20rpx;
      border: solid 1px #eee;
      box-shadow: 0 0 3px 2px #eee;
      width: calc(100% - 28rpx);
      max-height: 400rpx;
      background: #fff;
      display: flex;
      flex-direction: column;
        .task-item-info {
          display: flex;
          flex-direction: column;
          padding: 0 20rpx;
          font-weight: normal;
          .task-item-info-content {
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #010101;
            line-height: 40rpx;
            padding: 18rpx;
          }
        }

        .task-item-operation {
          display: flex;
          justify-content: flex-end;
          width: 100%;
          height: 64rpx;
          margin: 30rpx 20rpx 15rpx -20rpx;
          .btn-task-item {
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
            margin-left: 30rpx;
            .u-button {
              width: 160rpx;
              height: 100%;
            }
          }
        }
      
      .task-item-title-wrapper {
        display: flex;
        padding: 20rpx;
        box-sizing: border-box;
        background: linear-gradient(180deg, rgba(0,168,139,0.08) 0%, rgba(0,168,139,0) 100%);
        border-radius: 16rpx;
        border: 8rpx solid #FFFFFF;

        .task-item-title {
          display: flex;
          align-items: center;
          margin-left: 20rpx;
          font-size: 32rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: $gt-primary-color;
          line-height: 45rpx;
        }
      }
    }
  }
}
.empty-wrapper {
  height: calc(100% - 128px);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
