/**
 * 页面基类，实现页面基础功能
 */

const TOAST_DURATION = 2000

export default {

	data() {
		return {
			bgColor: this.$constants.COLOR.PRIMARY_COLOR
		}
	},

	methods: {
		// 提示成功
		showSuccess(msg) {
			uni.showToast({
				title: msg,
				icon: 'success',
				duration: TOAST_DURATION
			})
		},

		// 提示失败
		showError(msg) {
			let icon = msg.length > 4 ? 'none' : 'error'
			uni.showToast({
				title: msg,
				icon: icon,
				duration: TOAST_DURATION
			})
		},

		// 显示LOADING
		showLoading(msg) {
			uni.showLoading({
				title: msg,
				mask: true
			})
		},

		// 隐藏LOADING
		hideLoading() {
			uni.hideLoading()
		},

		// 跳转页面
		navigateTo(url) {
			uni.navigateTo({
				url
			})
		},

		// 返回上一页
		navigateBack() {
			uni.navigateBack({
				delta: 1
			})
		},

		// 回到上一页
		navigateBackWithNoCheck() {
			uni.$u.route({
				type: 'navigateBack'
			})
		},

		leftClickHandler() {
			this.navigateBack()
		}

	}
}