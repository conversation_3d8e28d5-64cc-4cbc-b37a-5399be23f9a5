/**
 * 页面基类，实现页面基础功能
 */

const TOAST_DURATION = 2000

export default {

	data() {
		return {
			bgColor: this.$constants.COLOR.PRIMARY_COLOR
		}
	},
	computed: {
		readonlyRole() {
			if (!this.userInfo.role_code) return true
			return !this.userInfo.role_code.includes('check_manage')
		}
	},
	methods: {
		// 提示成功
		showSuccess(msg, func) {
			let icon = 'success'
			if (msg.length > 5) icon = 'none'
			uni.showToast({
				title: msg,
				icon,
				mask: true,
				duration: TOAST_DURATION
			})
			if (func) setTimeout(func, TOAST_DURATION)
		},

		// 提示失败
		showError(msg, func) {
			let icon = 'error'
			if (msg.length > 5) icon = 'none'
			uni.showToast({
				title: msg,
				icon,
				mask: true,
				duration: TOAST_DURATION
			})
			if (func) setTimeout(func, TOAST_DURATION)
		},

		// 显示LOADING
		showLoading(msg) {
			uni.showLoading({
				title: msg || "加载中...",
				mask: true
			})
		},

		// 隐藏LOADING
		hideLoading() {
			uni.hideLoading()
		},

		// 跳转页面
		navigateTo(url) {
			uni.navigateTo({
				url
			})
		},

		// 返回上一页
		navigateBack() {
			uni.navigateBack({
				delta: 1
			})
		},

		// 回到上一页
		navigateBackWithNoCheck() {
			uni.$u.route({
				type: 'navigateBack'
			})
		}

	}
}