<template>
  <view class="home-wrapper">
 
   <view class="head-wrapper">
       <u-image
         class="login-title-wrapper"
         src="@/static/images/loginTitle.png"
         mode="aspectFit"
       ></u-image>
   </view>
   <view class="body-wrapper">
     <view class="body-items">
        <view class="body-item" v-for="item in menuList" :key="item.menu_index"  @click="menuClickHandler(item)">
           <view class="item-logo" :style="{ backgroundImage: 'url('+item.menu_image+')'}"/>
           <text class="grid-text">{{ item.menu_name }}</text>
       </view>
       
     </view>
   </view>
   <u-tabbar :value="0" fixed safeAreaInsetBottom placeholder :activeColor="'#00a88a'">
       <u-tabbar-item text="首页" icon="home-fill"></u-tabbar-item>
       <u-tabbar-item
         text="我的"
         icon="account"
         @click="navToAccount"
       ></u-tabbar-item>
     </u-tabbar>
  </view> 
 </template>
 
 <script>
 
 import BasePage from "@/pages/BasePage";
 import TabPage from "@/pages/TabPage";
 import Env from '@/env'
 
 export default {
   mixins: [BasePage, TabPage],
   data () {
     return {
       menuList: []
     }
   },
   async created() {
     await this.getMenuList()
     this.checkRoles()
   },
   methods: {
     async getMenuList () {
       const res =  await this.$apis.menu.getMenuList()
       if (!res) {
         return
       }
       const endPoint = Env.OSS_ENDPOINT
       this.menuList = res
       this.menuList.forEach (menu => {
         const { menu_image } = menu
         if (menu_image && menu_image.length > 0) {
           const fileUrl = menu_image[0]
           const url = `${endPoint}/${fileUrl}`
           menu.menu_image = url
         }
       })
     },
 
     checkRoles () {
       let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
       const { roles } = userInfo
       const genRoles = []
       roles.forEach(item => {
         if (["dl_county_collection", "dm_county_collection"].includes(item.code)) {
           genRoles.push(item)
         }
       })
 
       if (Array.isArray(genRoles) && genRoles.length === 1) {
 
           this.menuList.forEach(item => {
           if (item?.menu_roles?.includes(genRoles[0].code)) {
             const { page_url, task_type } = item
             uni.navigateTo({ url: `${page_url}?menuName=${task_type}` })
           }
         })
       }
       
     },
 
     menuClickHandler (menu) {
       const { page_url, task_type } = menu
       if (!page_url || !task_type) {
         const msg = '开发中'
         this.showError(msg)
         return
       }
       const state = this.menuState(menu)
       if (!state || !page_url) {
         const msg = '没有权限！'
         this.showError(msg)
         return
       }
       uni.navigateTo({ url: `${page_url}?menuName=${task_type}` })
     },
 
     menuState({ menu_roles }) {
       if (!menu_roles || !this.userInfo || !this.userInfo.roles) return false
       let result = false
       this.userInfo.roles
         .map(item => {
           return item.code
         })
         .forEach(item => {
           if (menu_roles.indexOf(item) >= 0) {
             result = true
           }
         })
       return result
     },
   }
 }
 </script>
 
 <style lang="scss" scoped>
 @import "@/static/styles/common.scss";
 .home-wrapper {
   width: 100%;
   height: 100vh;
   background-color: #fff;
 
 }
 .head-wrapper {
     box-sizing: border-box;
     position: fixed;
     width: 100%;
     background: url("@/static/images/userBackground.png") no-repeat;
     background-position: center;
     background-size: 100% 100%;
     display: flex;
     height: 450rpx;
     flex-direction: column;
     align-items: center;
   }
 
 .body-wrapper {
   position: fixed;
   height: 70vh;
   width: 100%;
   top: 350rpx;
   background: #fff;
   border-radius: 30rpx 30rpx 0 0;
   .body-title {
     font-size: 32rpx;
     font-weight: bolder;
   }
 
   .body-items {
     height: 100%;
     width: calc(100% - 40rpx);
     display: flex;
     flex-direction: column;
     padding: 20rpx;
 
     .body-item {
       margin: 0rpx 45rpx 20rpx 45rpx;
       height: 200rpx;
       width: calc(100% - 90rpx);
       border-radius: 20rpx;
       background: #fff;
       border: 1px solid #eee;
       box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.05);
       display: flex;
       align-items: center;
       justify-content: space-evenly;
       .item-logo {
         background-size: 100% 100%;
         background-repeat: no-repeat;
         height: 120rpx;
         width: 120rpx;
       }
 
       .grid-text {
 
       font-size: 36rpx;
       font-family: PingFangSC-Semibold, PingFang SC;
       font-weight: 600;
       color: #333333;
       line-height: 50rpx;
     }
 
     }
   }
 }
 
 </style>