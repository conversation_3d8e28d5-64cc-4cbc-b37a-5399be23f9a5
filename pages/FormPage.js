
import login from '../apis/login'
import BasePage from './BasePage'

export default {

	mixins: [BasePage],

	data () {
		return {
			options: {
				formDef: undefined,
				formRecordData: undefined,
				cacheRecordId: undefined
			},
			modalShow: false,
			modal: {
				title: this.$constants.MSG.WARNING_TITLE,
				content: this.$constants.MSG.WARNING_DATA_SAVE,
			},
			title:''
		}
	},

	// 响应手机物理或者虚拟返回按键
	onBackPress ( e ) {
		if ( e.from === 'backbutton' ) {
			this.navigateBack()
			return true
		}
	},

	methods: {

		// 获取表单定义
		async getFormDef () {
			if ( !this.formUid ) return
			// const result = await this.$apis.formDesign.getFormDesign(this.formUid)
			const result = await this.$apis.formLocal.getFormDef( this.formUid )
			if ( result ) {
				this.options.formDef = result
				return result
			}
			this.showError( this.$constants.MSG.GET_FORM_DEF_FAIL )
		},

		// 本地存储
		async saveLocal ( formData ) {
			if ( !formData ) return
			const cacheRecordId = await this.$apis.formDataLocal.upsertCacheRecord(
				this.formUid,
				this.cacheRecordId,
				formData
			)
			if ( cacheRecordId ) {
				this.cacheRecordId = cacheRecordId
				this.showSuccess( this.$constants.MSG.DATA_CACHED_SUCCESS )
				// 重置变化检测
				this.changeDetector.setSource( formData )
			} else {
				this.showError( this.$constants.MSG.DATA_CACHED_FAIL )
			}
		},

		// 向服务器端提交数据
		async submitData ( data ) {
			if ( !data ) return
			this.showLoading( this.$constants.MSG.DATA_SUBMITTING )
			const formData = this.$utils.form.toApiFormData( data )
			console.log(formData)
			if ( formData._id ) {
				await this.updateFormData( formData )
			} else {
				await this.addFormData( formData )
			}
		},

		// 更新表单数据
		async updateFormData ( formData ) {
			try {
				const result = await this.$apis.formData.updateFormRecord(
					this.formUid,
					formData._id,
					formData,
					this.cacheRecordId
				)
				this.showSuccess( this.$constants.MSG.DATA_SUBMIT_SUCCESS )
				this.navigateBackWithNoCheck()
			} catch ( err ) {
				this.showError( this.$constants.MSG.DATA_SUBMIT_FAIL )
			} finally {
				uni.hideLoading()
			}
		},

		// 增加表单数据
		async addFormData ( formData ) {
			try {
				const result = await this.$apis.formData.addFormRecord(
					this.formUid,
					formData,
					this.cacheRecordId
				)
				this.showSuccess( this.$constants.MSG.DATA_SUBMIT_SUCCESS )
				this.navigateBackWithNoCheck()
			} catch ( err ) {
				this.showError( this.$constants.MSG.DATA_SUBMIT_FAIL )
			} finally {
				uni.hideLoading()
			}
		},

		async updataFormDataState ( formData ) {
			try {
				const result = await this.$apis.formData.updateFormRecordState(
					this.formUid,
					formData._id,
					{
						field: "report_status",
						state: "已上报",
						msg: "重新上报"
					},
					this.cacheRecordId
				)
				this.showSuccess( this.$constants.MSG.DATA_SUBMIT_SUCCESS )
				this.navigateBackWithNoCheck()
			} catch ( err ) {
				this.showError( this.$constants.MSG.DATA_SUBMIT_FAIL )
			} finally {
				uni.hideLoading()
			}
		},

		modalCancel () {
			this.modalShow = false
		},

		modalConfirm () {
			this.navigateBackWithNoCheck()
			this.modalShow = false
		},

	}
}