<template>
  <view>
		<u-navbar title="数据详情" @leftClick="leftClickHandler" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder></u-navbar>
    <view class="content">
      <GTForm ref="gtForm" v-if="options.formDef" :options="options"></GTForm>
    </view>
	</view>
</template>

<script>

import BasePage from '@/pages/BasePage'
import GTForm from '@/components/form/GTForm'
import Storage from '@/tools/Storage'

export default {

	mixins: [ BasePage ],

  components: {GTForm},

  data() {
    return {
			options: {},
			formUid: undefined,
			formDef: undefined,
			cacheRecordId: undefined
		}
  },

  onLoad(option) {
		this.formUid = option.formUid
		this.formRecordUid = option.formRecordUid
		this.cacheRecordId = option.cacheRecordId
		this.initForm()
  },

  methods: {
		async initForm() {
      let options = {}
      if (this.formUid) {
        // 获取表单定义
        options.formUid = this.formUid
        options.formDef = await this.$apis.formDef.getFormDef(this.formUid)
      }
      if (this.formRecordUid) {
        // 获取表单数据
      	options.formRecordUid = this.formRecordUid
        const result = Storage.getFormData(this.formRecordUid)
      	options.formRecordData = this.$utils.form.toFrontFormData(result)
      }
			// 只读
			options.readonly = true
      this.options = options
    },

		// 返回到上一页
		leftClickHandler(){
			this.navigateBack()
		}

	}
}
</script>

<style lang="scss" scoped>
@import '@/static/styles/common.scss';
	/deep/ .u-navbar__content{
		.u-navbar__content__title, .u-icon__icon{
			color: $gt-navbar-title-color !important;
		}
	}
.content {
  padding: 0 30rpx 140rpx;
}

</style>
