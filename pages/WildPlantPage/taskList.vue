<template>
  <view class="page-container">
    <u-navbar
      title="野生植物保护任务列表"
      @leftClick="leftClickHandler"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
    ></u-navbar>
    <view class="content">
      <view v-if="!taskList || taskList.length === 0">
        <u-empty text="没有数据" icon="/static/images/empty.png"></u-empty>
      </view>
      <view v-else>
        <view class="task-item" v-for="item in taskList" :key="item._id">
          <view class="task-item-title-wrapper">
            <!-- <u-icon name="calendar-fill" color="#FF7A45" size="80rpx"></u-icon> -->
            <u-icon name="map-fill" color="#00a88a" :size="'50rpx'"></u-icon>
            <view class="task-item-title">{{ item["保护点名称"] }}</view>
          </view>
          <view class="task-item-info">
            <view class="task-item-info-content">
              年份：{{ item["年份"] }}
            </view>
            <view class="task-item-info-content">
              轮次：{{ item["轮次"] }}
            </view>
            <view class="task-item-info-content">
              审核状态：{{ item["审核状态"] }}
            </view>
            <view class="task-item-info-content" v-if="item['退回原因']">
              退回原因：{{ item["退回原因"] }}
            </view>
          </view>

          <view class="data-item-operations">
            <view
              class="dc-item-operations"
              v-for="(tableData, idx) in tableIdList"
              :key="idx"
            >
              <view class="operations-label">监测{{ idx + 1 }}</view>
              <view class="btn-wrapper btn-wrapper-show-tab">
                <u-button
                  :disabled="!item[tableData.dataId]"
                  icon="file-text"
                  type="primary"
                  text="查看"
                  shape="circle"
                  plain
                  @click="openMonitorTable('view', idx, item)"
                ></u-button>
              </view>
              <view class="btn-wrapper btn-wrapper-show-tab">
                <u-button
                  :disabled="['市级已审核', '省级已审核'].includes(item.审核状态)"
                  icon="edit-pen"
                  type="primary"
                  :text="item[tableData.dataId] ? '编辑' : '填报'"
                  shape="circle"
                  @click="openMonitorTable('add', idx, item)"
                  plain
                ></u-button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import TabPage from "@/pages/TabPage";
import BasePage from "@/pages/BasePage";
import Storage from "@/tools/Storage";
export default {
  mixins: [TabPage, BasePage],
  onLoad(option) {
    this.formUid = option.子任务表_uid;
    this.dataType = option.任务类型;
    this.任务_uid = option.任务_uid;
    this.menuName = option.menuName;
    //  获取子任务数据
    this.getTaskList();
  },
  onShow() {
    this.getTaskList();
    //  修改数据后返回页面时更新按钮状态
    if (this.currentTaskId) this.getOnceTask();
  },
  data() {
    return {
      menuName: null,
      dataType: null,
      任务_uid: null,
      tabSearchFilter: null,
      tabSearchHeight: null,
      tabFontSize: 15,
      formUid: null,
      taskList: [],
      buttonList: [],
      tableIdList: [
        { tableId: "监测表一_uid", dataId: "监测表一数据_uid" },
        { tableId: "监测表二_uid", dataId: "监测表二数据_uid" },
      ],
      currentTaskId: null,
      clickTableDataId: null,
    };
  },

  methods: {
    //  获取子任务列表
    async getTaskList() {
      const data = await this.$apis.formData.getFormRecords(this.formUid, {
        filter: ["=", "任务_uid", this.任务_uid],
      });
      this.taskList = data.list;
    },
    //  获取单个任务数据
    async getOnceTask() {
      const dataId = this.clickTableDataId;
      const data = await this.$apis.formData.getFormRecord(
        this.formUid,
        this.currentTaskId
      );
      this.taskList.forEach((item) => {
        //  当同一条数据中的数据内容发生从无到有的修改时，将新的数据ID赋值给数组，更新按钮状态
        if (item._id === data._id && item[dataId] !== data[dataId])
          item[dataId] = data[dataId];
      });
      //  执行完后初始化点击任务信息
      this.currentTaskId = null;
      this.clickTableDataId = null;
    },
    //  获取监测表数据
    async openMonitorTable(btnType, tableNum, item) {
      // 获取点击任务id
      const { tableId, dataId } = this.tableIdList[tableNum];
      // const dataId = this.tableIdList[tableNum].dataId;
      const formUid = item[tableId];
      //  判断是否有数据ID，有则获取数据存入Storage
      if (item[dataId]) {
        const data = await this.$apis.formData.getFormRecord(
          item[tableId],
          item[dataId]
        );
        Storage.saveFormData(item["_id"], data);
      }
      let pageUrl = `${this.$constants.PAGE.WILD_PLANT_PAGE}?formUid=${formUid}&formRecordUid=${item._id}&cacheable=false`;
      //  查看
      if (btnType === "view") {
        pageUrl = pageUrl + `&readonly=true`;
        this.navigateTo(pageUrl);
      } else if (btnType === "add") {
        //  点击不为查看时，获取任务信息
        this.currentTaskId = item._id;
        this.clickTableDataId = dataId;
        //  编辑\填报
        const genUrl = this.getGenPageUrl(pageUrl, item);
        this.navigateTo(genUrl);
      }
    },
    getGenPageUrl(url, item) {
      const fields = [
        "任务_uid",
        "年份",
        "保护点_uid",
        "保护点名称",
        "所在地",
        "轮次",
      ];
      fields.forEach((f) => {
        const itemUrl = `&${f}=${item[f]}`;
        url = url.concat(itemUrl);
      });

      if (item["采样指标值"])
        url = url.concat(`&采样指标值=${item["采样指标值"]}`);
      return url;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";
.page-container {
  height: 100vh;
  // background-color: #f5f5f9;
  padding: 20rpx;
  box-sizing: border-box;
  /deep/ .u-navbar__content {
    .u-navbar__content__title,
    .u-icon__icon {
      color: $gt-navbar-title-color !important;
      font-size: 36rpx;
    }
  }
  .content {
    padding-bottom: 40rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .task-item {
      margin-top: 20rpx;
      border: 1px solid #dadbde;
      border-radius: 16rpx;
      background-color: #fff;
      box-shadow: 0 -2rpx 4rpx #eee;
      padding: 8rpx;
      .task-item-info {
        display: flex;
        flex-direction: row;
        min-width: 45%;
        flex-wrap: wrap;
        padding: 0 20rpx;
        font-weight: normal;
        .task-item-info-content {
          font-size: 28rpx;
          // font-family: PingFangSC-Regular, PingFang SC;
          // font-weight: 400;
          color: #010101;
          line-height: 40rpx;
          padding: 18rpx;
          margin-right: 80rpx;
        }
      }

      .task-item-operation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: calc(100% - 40rpx);
        height: 64rpx;
        margin: 30rpx 0rpx 15rpx 0rpx;
        padding: 0 20rpx;
        .btn-task-item {
          display: flex;
          // justify-content: flex-start;
          font-size: 28rpx;
          // font-family: PingFangSC-Regular, PingFang SC;
          // font-weight: 400;
          color: #333333;
          line-height: 40rpx;
          // margin-left: 30rpx;
          .u-button {
            width: 120rpx;
            height: 100%;
          }
        }
      }

      .task-item-title-wrapper {
        display: flex;
        padding: 20rpx;
        box-sizing: border-box;
        background: linear-gradient(
          180deg,
          rgba(0, 168, 139, 0.08) 0%,
          rgba(0, 168, 139, 0) 100%
        );
        border-radius: 16rpx;
        border: 8rpx solid #ffffff;

        .task-item-title {
          display: flex;
          align-items: center;
          margin-left: 20rpx;
          font-size: 32rpx;
          // font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: $gt-primary-color;
          line-height: 45rpx;
        }
      }
    }
    .data-item-operations {
      display: flex;
      // flex-direction: row-reverse;

      justify-content: space-between;
      box-sizing: border-box;

      width: 100%;
      height: 64rpx;
      padding: 0 20rpx;
      margin: 8rpx 0 14rpx 0;

      .cy-item-operations {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        height: 100%;
      }
      .dc-item-operations {
        // margin-left: 20rpx;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        height: 100%;
      }

      .operations-label {
        margin: 0;
        white-space: nowrap;
        text-align: center;
        font-size: 28rpx;
        // font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 30rpx;
      }

      .btn-wrapper {
        font-size: 28rpx;
        // font-family: PingFangSC-Regular, PingFang SC;
        // font-weight: 400;
        color: #333333;
        line-height: 40rpx;
        margin-left: 10rpx;
        height: 100%;
        .u-button {
          width: 160rpx;
          height: 100%;
          background: rgba(0, 168, 138, 0.05) !important;
        }
      }

      .btn-wrapper-show-tab {
        font-size: 28rpx;
        // font-family: PingFangSC-Regular, PingFang SC;
        // font-weight: 400;
        line-height: 30px;
        width: 100%;
        height: 56rpx;
        border-radius: 32rpx;

        .u-button {
          width: 112rpx;
          height: 100%;
          background: rgba(0, 168, 138, 0.05) !important;
        }
      }
    }
  }
}
</style>
