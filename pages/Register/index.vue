<template>
	<view class="page-content">
		<u-navbar title="用户注册" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder @leftClick="leftClickHandler">
		</u-navbar>
		<u--form labelPosition="left" :model="model1" :rules="rules" labelWidth="70" ref="uForm">
			<u-form-item label="手机号" prop="register.phone" ref="item1" required>
				<u--input v-model="model1.register.phone" border="none"></u--input>
			</u-form-item>
			<u-form-item label="验证码" prop="register.code" ref="item1" required>
				<u--input v-model="model1.register.code" border="none"></u--input>
				<view slot="right">
					<u-button type="primary" size="mini" @click="sendSms">{{smsText}}</u-button>
				</view>
			</u-form-item>
			<u-form-item label="姓名" prop="register.nickname" ref="item1" required>
				<u--input v-model="model1.register.nickname" border="none"></u--input>
			</u-form-item>
			<u-form-item label="市" prop="register.city" @click="areaPicker = true" ref="item1" required>
				<u--input v-model="model1.register.city.label" disabled disabledColor="#ffffff" placeholder="请选择所在市" border="none"></u--input>
				<u-icon slot="right" name="arrow-right"></u-icon>
			</u-form-item>
			<u-form-item label="区县" prop="register.county" @click="areaPicker = true" ref="item1" required>
				<u--input v-model="model1.register.county.label" disabled disabledColor="#ffffff" placeholder="请选择所在区县" border="none"></u--input>
				<u-icon slot="right" name="arrow-right"></u-icon>
			</u-form-item>
			<u-form-item label="用户名" prop="register.username" ref="item1" required>
				<u--input v-model="model1.register.username" border="none"></u--input>
			</u-form-item>
			<u-form-item label="密码" prop="register.password" ref="item1" required>
				<u--input v-model="model1.register.password" type="password" border="none"></u--input>
			</u-form-item>
			<u-form-item label="确认密码" prop="register.again" ref="item1" required>
				<u--input v-model="model1.register.again" type="password" border="none"></u--input>
			</u-form-item>
		</u--form>
		<view class="register">
			<u-button type="primary" @click="submit">注册账号</u-button>
		</view>
		<u-picker :show="areaPicker" ref="uPicker" keyName="label" :columns="columns" @cancel="areaPicker=false" @close="areaPicker=false" @confirm="confirmArea" @change="changeArea"></u-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				model1: {
					register: {
						phone: "",
						code: "",
						city: "",
						county: "",
						username: "",
						nickname: "",
						password: "",
						again: ""
					},
				},
				areaPicker: false,
				sendStatus: false,
				loading: false,
				columnIndex: 0,
				columns: [],
				smsText: "发送验证码",
				rules: {
					'register.phone': {
						required: true,
						message: '请填写正确的手机号码',
						validator: (rule, value) => {
							return uni.$u.test.mobile(value);
						},
						trigger: ['blur']
					},
					'register.code': {
						type: 'string',
						required: true,
						message: '请输入手机验证码',
						trigger: ['blur']
					},
					'register.nickname': {
						type: 'string',
						required: true,
						message: '请填写用户昵称',
						trigger: ['blur']
					},
					'register.city': {
						type: 'object',
						required: true,
						message: '请选择所在市',
						trigger: ['change']
					},
					'register.county': {
						type: 'object',
						required: true,
						message: '请选择所在区县',
						trigger: ['change']
					},
					'register.username': {
						required: true,
						message: '仅可包含中文、英文和数字，5-17位',
						validator: (rule, value) => {
							let regex = /^[\u4e00-\u9fa5A-Za-z0-9]{5,17}$/
							return regex.test(value)
						},
						trigger: ['blur']
					},
					'register.password': {
						required: true,
						message: '仅可包含英文和数字，6-16位',
						validator: (rule, value) => {
							let regex = /^[a-zA-Z0-9]{6,16}$/
							return regex.test(value)
						},
						trigger: ['blur']
					},
					'register.again': {
						required: true,
						message: '与输入的密码不一致',
						validator: (rule, value) => {
							return this.model1.register.password === value
						},
						trigger: ['change', 'blur']
					}
				},
			};
		},
		onLoad() {
			this.getDicts()
		},
		onReady() {
			//onReady 为uni-app支持的生命周期之一
			this.$refs.uForm.setRules(this.rules)
		},
		methods: {
			leftClickHandler() {
				uni.navigateBack()
			},
			async submit() {
				if (this.loading) return
				this.loading = true
				try {
					let validate = await this.$refs.uForm.validate()
					console.log(validate);
					const { phone, password, code, username, nickname, city, county } = this.model1.register
					let params = {
						phone,
						password,
						verifyCode: code,
						username,
						nickname,
						extraInfo: {
							pcode: "45",
							pname: "广西",
							ccode: city.ccode,
							cname: city.cname,
							fcode: county.fcode,
							fname: county.fname,
						}
					}
					await this.$apis.login.registerBySms(params)
					uni.showToast({
						title: "注册成功",
						duration: 1500,
						mask: true,
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} catch (error) {
					console.log(error);
					let msg = '注册失败'
					if (typeof error == 'string') msg = error
					if (Array.isArray(error)) msg = error[0].message
					uni.showToast({
						title: msg,
						icon: "none"
					})
				} finally {
					this.loading = false
				}
			},
			// 行政区选择操作
			confirmArea(e) {
				let cityIndex = e.indexs[0]
				let countyIndex = e.indexs[1]
				this.model1.register.city = this.columns[0][cityIndex]
				this.model1.register.county = this.columns[1][countyIndex]
				this.areaPicker = false
			},
			changeArea(e) {
				let columnIndex = e.columnIndex
				let index = e.index
				if (columnIndex !== 0) return
				let county = this.countyData.filter(item => { return item.ccode == this.cityData[index].ccode })
				// this.columns[1] = county
				this.$set(this.columns, 1, county)
				console.log(this.columns, county);
			},
			// 发送手机验证码
			async sendSms() {
				if (this.sendStatus) return
				this.sendStatus = true
				try {
					if (!this.model1.register.phone) {
						uni.showToast({
							title: "请填写手机号码",
							icon: "none"
						})
						return
					}
					await this.$apis.login.sendSms(this.model1.register.phone)
					uni.showToast({
						title: "发送成功",
						icon: "success"
					})
					let num = 60
					this.smsInterval = setInterval(() => {
						num -= 1
						this.smsText = `${num} s`
						if (num - 1 <= 0) {
							this.smsText = '重新发送'
							this.sendStatus = false
							clearInterval(this.smsInterval)
						}
					}, 1000)
				} catch (error) {
					uni.showToast({
						title: error || '发送失败',
						icon: "none"
					})
				} finally {
					this.sendStatus = false
				}
			},
			// 获取行政区字典表
			async getDicts(e) {
				try {
					let { list: city } = await this.$apis.formData.getFormRecords('admin_city')
					let { list: county } = await this.$apis.formData.getFormRecords('admin_county')
					city = city.map(item => {
						item.label = item.cname
						return item
					})
					county = county.map(item => {
						item.label = item.fname
						return item
					})
					this.cityData = city
					this.countyData = county
					let pickerCounty = county.filter(item => { return item.ccode == city[0].ccode })
					this.columns = [city, pickerCounty]
				} catch (error) {
					uni.showToast({
						title: "获取行政区失败，请重新打开App",
						icon: "none"
					})
				}
			},
		},
	};
</script>
<style scoped lang="scss">
	@import "@/static/styles/common.scss";

	.page-content {
		background-color: #f8f8f8;
		min-height: 100vh;
		padding: 30rpx;
	}

	/deep/ .u-navbar__content {
		.u-navbar__content__title {
			color: $gt-navbar-title-color !important;
		}
	}

	/deep/ .u-icon__icon.uicon-arrow-left {
		color: #fff !important;
	}

	/deep/ .u-form-item__body__left {
		width: 80px !important;
	}

	/deep/ .u-form-item__body__left__content {
		padding-bottom: 0;
	}

	/deep/ .u-form-item__body {
		padding: 10rpx 0;
	}

	/deep/ .u-form-item {
		background-color: #fff;
		margin-bottom: 10rpx;
		padding: 20rpx 30rpx;
		border-radius: 12rpx;
	}

	.register {
		margin-top: 100rpx;
	}
</style>