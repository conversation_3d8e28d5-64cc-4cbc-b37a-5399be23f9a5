<template>
	<view>
		<view class="login-bg">
			<view class="t-b">{{ tip }}</view>
		</view>
		<view class="login-view" style="">
			<view class="t-login">
				<u-form :model="loginInfo" ref="uForm">
					<view class="input-wrapper">
						<u-form-item prop="userName">
							<u-input placeholder="请输入用户名" :adjustPosition="false" :clearable="true" @clear="handleClear('userName')" border="none" v-model="loginInfo.userName" prefixIcon="account" placeholderClass="data-placeholder">
							</u-input>
						</u-form-item>
					</view>

					<view v-if="inputType == 'text'" :key="1">
						<u-form-item prop="passWord">
							<u-input placeholder="请输入密码" :adjustPosition="false" :clearable="true" :type="'text'" :password="inputType == 'password'" @clear="handleClear('passWord')" border="none" v-model="loginInfo.passWord" prefixIcon="lock" placeholderClass="data-placeholder">
								<template slot="suffix">
									<view @click="iconChange" class="psd-icon">
										<u-icon :name="pswSuffixIcon" size="16"></u-icon>
									</view>
								</template>
							</u-input>
						</u-form-item>
					</view>
					<view v-if="inputType == 'password'" :key="2">
						<u-form-item prop="passWord">
							<u-input placeholder="请输入密码" :adjustPosition="false" :clearable="true" :type="'password'" :password="inputType == 'password'" @clear="handleClear('passWord')" border="none" v-model="loginInfo.passWord" prefixIcon="lock" placeholderClass="data-placeholder">
								<template slot="suffix">
									<view @click="iconChange" class="psd-icon">
										<u-icon :name="pswSuffixIcon" size="16"></u-icon>
									</view>
								</template>
							</u-input>
						</u-form-item>
					</view>
				</u-form>
				<view class="login-btn">
					<u-button @click="login" type="primary">登 录</u-button>
				</view>
				<view class="register" @click="register">点击注册农户账号</view>
			</view>
		</view>
		<view style="width: 100%;text-align: center;font-size: 24rpx;color: #333;position: absolute;bottom: 50rpx;width: 100%;">
			<view>承建:广西壮族自治区农业生态与资源保护站</view>
			<view>投诉与建议电话:0771-2182876</view>
		</view>
	</view>
</template>

<script>
	import Storage from "@/tools/Storage";
	const USER_FORM = "tb_user_info_map";

	export default {
		data() {
			return {
				tip: "广西安全利用",
				userInfo: undefined,
				iconState: false,
				loading: false,
				pswSuffixIcon: "eye-off",
				inputType: "password",
				loginInfo: {
					userName: "",
					passWord: "",
				},
				rules: {
					userName: [{
						required: true,
						message: "请输入用户名",
						// 可以单个或者同时写两个触发验证方式
						trigger: ["blur", "change"],
					}, ],
					passWord: [{
						required: true,
						message: "请输入密码",
						trigger: ["blur", "change"],
					}, ],
				},
				whiteRoles: ["city_manage", "check_manage", "county_manage", "implementer", "farmer"]
			};
		},
		onLoad() {
			const _this = this;
			uni.getStorage({
				key: "refresh_token",
				success: async function(res) {
					const data = await this.$apis.login.refreshToken();
					console.log('data=====', data);
					await _this.getUserInfo(data.user_id);
					uni.reLaunch({ url: "/pages/Workspace/index" });
				},
			});
		},
		// H5端
		mounted() {
			this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			register() {
				uni.navigateTo({
					url: "/pages/Register/index"
				})
			},
			handleClear(field) {
				this.loginInfo[field] = ""
			},
			login() {
				this.$refs.uForm
					.validate()
					.then(async (res) => {
						this.loading = true;
						uni.showLoading({ title: "登录中...", mask: true });
						await this.$apis.login
							.login(this.loginInfo.userName, this.loginInfo.passWord)
							.then(async (data) => {
								await uni.setStorage({
									key: "access_token",
									data: data.access_token,
								});
								await uni.setStorage({
									key: "refresh_token",
									data: data.refresh_token,
								});

								await this.getUserInfo(data.user_id);
								let role = this.userInfo.role_code
								let isRole = false
								this.whiteRoles.forEach(item => {
									if (role.includes(item)) isRole = true
								})
								if (isRole) {
									uni.reLaunch({ url: "/app/pages/index/index" });
								} else {
									uni.clearStorageSync();
									uni.showToast({
										title: "权限不足",
										icon: 'none'
									})
									return
								}
							})
							.catch((err) => {
								console.log(err);
								uni.showToast({
									title: err,
									icon: "none",
								});
							});
						this.loading = false;
						uni.hideLoading();
					})
					.catch((errors) => {
						// uni.$u.toast('校验失败')
					});
			},
			iconChange() {
				this.pswSuffixIcon = this.pswSuffixIcon === "eye-off" ? "eye" : "eye-off";
				this.inputType = this.inputType === "password" ? "text" : "password";
			},
			async getUserInfo(userId) {
				const { list } = await this.$apis.formData.getFormRecords(USER_FORM, {
					filter: ["=", "user_id", userId],
				});
				this.userInfo = list[0];
				await uni.setStorage({
					key: "userInfo",
					data: JSON.stringify(this.userInfo),
				});
				// 存储用户信息作为全局变量
				await Storage.saveUser(this.userInfo);
			},

		},
	};
</script>
<style lang="scss" scoped>
	@import "@/static/styles/common.scss";

	.login-bg {
		width: 100%;
		height: 480rpx;
		background-color: $gt-primary-color;
		background-position: center;
		background-size: 100% 100%;

		.t-b {
			text-align: center;
			font-size: 46rpx;
			color: #ffffff;
			padding: 190rpx 0 0 0;
			line-height: 70rpx;
		}
	}

	.login-view {
		width: 100%;
		position: relative;
		margin-top: -120rpx;
		background-color: #ffffff;
		border-radius: 8% 8% 0% 0;

		.t-login {
			width: 600rpx;
			margin: 0 auto;
			font-size: 28rpx;
			padding-top: 80rpx;
		}

		.t-login input {
			height: 90rpx;
			line-height: 90rpx;
			margin-bottom: 50rpx;
			border-bottom: 1px solid #e9e9e9;
			font-size: 28rpx;
		}

		.login-btn {
			margin-top: 30rpx;
		}

		.register {
			margin-top: 40rpx;
			font-size: 28rpx;
			width: 100%;
			text-align: right;
			color: $gt-primary-color;
		}
	}
</style>