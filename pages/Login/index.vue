<template>
	<view class="login-wrapper">
		<view class="login-bg">
			<u-image class="login-title-wrapper" src="@/static/images/loginTitle.png" mode="aspectFit"></u-image>
			<u-image class="login-logo-wrapper" src="@/static/images/loginLogo.png" :style="{ marginTop: `${BackgroundHeight - 50}px` }" mode="aspectFit" width="100" height="100"></u-image>
		</view>
		<view class="login-view" :style="{ marginTop: `${BackgroundHeight + 50}px` }">
			<view class="login-form">
				<u-form :model="loginInfo" ref="uForm">
					<view class="input-wrapper">
						<u-form-item prop="userName">
							<u-input placeholder="请输入用户名" :adjustPosition="false" :clearable="true" @clear="handleClear('user')" border="none" v-model="loginInfo.userName" prefixIconStyle="font-size: 18px;color: #00a88a" prefixIcon="account" placeholderClass="data-placeholder">
							</u-input>
						</u-form-item>
					</view>

					<view v-if="inputType == 'text'" :key="1">
						<u-form-item prop="passWord">
							<u-input placeholder="请输入密码" :adjustPosition="false" :clearable="true" :type="'text'" :password="inputType == 'password'" @clear="handleClear('psd')" border="none" v-model="loginInfo.passWord" prefixIconStyle="font-size: 18px;color: #00a88a" prefixIcon="lock"
								placeholderClass="data-placeholder">
								<template slot="suffix">
									<view @click="iconChange" class="psd-icon">
										<u-icon :name="pswSuffixIcon" size="18" :color="$constants.COLOR.PRIMARY_COLOR"></u-icon>
									</view>
								</template>
							</u-input>
						</u-form-item>
					</view>
					<view v-if="inputType == 'password'" :key="2">
						<u-form-item prop="passWord">
							<u-input placeholder="请输入密码" :adjustPosition="false" :clearable="true" :type="'password'" :password="inputType == 'password'" @clear="handleClear('psd')" border="none" v-model="loginInfo.passWord" prefixIcon="lock" prefixIconStyle="font-size:18px;color: #00a88a"
								placeholderClass="data-placeholder">
								<template slot="suffix">
									<view @click="iconChange" class="psd-icon">
										<u-icon :name="pswSuffixIcon" size="18" :color="$constants.COLOR.PRIMARY_COLOR"></u-icon>
									</view>
								</template>
							</u-input>
						</u-form-item>
					</view>
				</u-form>
				<view class="login-btn">
					<button @click="login">登 录</button>
				</view>
			</view>
		</view>
		<view style="width: 100%;text-align: center;font-size: 24rpx;color: #8a8a8a;position: absolute;bottom: 30rpx;width: 100%;">广西壮族自治区农业生态与资源保护站</view>
	</view>
</template>

<script>
	import Storage from "@/tools/Storage";

	export default {
		data() {
			return {
				BackgroundHeight: null,
				userInfo: undefined,
				iconState: false,
				loading: false,
				pswSuffixIcon: "eye-off",
				inputType: "password",
				loginInfo: {
					userName: "",
					passWord: "",
				},
				rules: {
					userName: [{
						required: true,
						message: "请输入用户名",
						// 可以单个或者同时写两个触发验证方式
						trigger: ["blur", "change"],
					}, ],
					passWord: [{
						required: true,
						message: "请输入密码",
						trigger: ["blur", "change"],
					}, ],
				},
			};
		},
		onLoad() {
			uni.getStorage({
				key: "refresh_token",
				success: async function(res) {
					console.log(res);
					await this.$apis.login.refreshToken();
					await this.getUserInfo();
					uni.reLaunch({ url: this.$constants.PAGE.INDEX_PAGE });
				},
			});
		},
		// H5端
		mounted() {
			this.$refs.uForm.setRules(this.rules);
			this.getBackgroundHeight();
		},
		methods: {
			login() {
				this.$refs.uForm
					.validate()
					.then(async (res) => {
						this.loading = true;
						uni.showLoading({ title: "登录中...", mask: true });
						await this.$apis.login
							.login(this.loginInfo.userName, this.loginInfo.passWord)
							.then(async (data) => {
								await uni.setStorage({
									key: "access_token",
									data: data.access_token,
								});
								await uni.setStorage({
									key: "refresh_token",
									data: data.refresh_token,
								});

								await this.getUserInfo();

								uni.reLaunch({ url: this.$constants.PAGE.INDEX_PAGE });
							})
							.catch((err) => {
								console.log(err);
								uni.showToast({
									title: err,
									icon: "error",
								});
							});
						this.loading = false;
						uni.hideLoading();
					})
					.catch((errors) => {
						// uni.$u.toast('校验失败')
					});
			},
			getBackgroundHeight() {
				this.$nextTick(() => {
					let height = 0;
					uni.getSystemInfo({
						success: (res) => {
							let info = uni.createSelectorQuery().select(".login-bg");
							info
								.boundingClientRect((data) => {
									height = data.height; // 获取元素高度
									this.BackgroundHeight = height;
								})
								.exec();
						},
					});
				});
			},
			iconChange() {
				this.pswSuffixIcon = this.pswSuffixIcon === "eye-off" ? "eye" : "eye-off";
				this.inputType = this.inputType === "password" ? "text" : "password";
			},
			async getUserInfo() {
				let userInfo = await this.$apis.login.getUserInfo();
				this.userInfo = userInfo;
				await uni.setStorage({
					key: "userInfo",
					data: JSON.stringify(userInfo),
				});
				// 存储用户信息作为全局变量
				Storage.saveUser(userInfo);
			},
		},
	};
</script>
<style lang="less" scoped>
	.login-wrapper {
		width: 100%;
		height: 100vh;
		background-color: #fff;

		.login-bg {
			box-sizing: border-box;
			position: fixed;
			width: 100%;
			background: url("@/static/images/loginBackground.png") no-repeat;
			background-position: center;
			background-size: 100% 100%;
			display: flex;
			height: 450rpx;
			flex-direction: column;
			align-items: center;

			.login-title-wrapper {
				display: flex;
				align-items: center;
				margin-left: 40rpx;
				width: calc(100% - 80rpx);
			}

			.login-logo-wrapper {
				position: fixed;
				z-index: 2;
			}
		}

		.login-view {
			position: fixed;
			width: 100%;
			z-index: 2;
		}
	}

	.login-form {
		width: 600rpx;
		margin: 0 auto;
		font-size: 28rpx;
		padding-top: 80rpx;
	}

	.login-form button {
		font-size: 28rpx;
		background-color: #00a88a;
		color: #fff;
		// height: 90rpx;
		line-height: 38px;
		font-weight: bold;
	}

	/deep/.u-form-item {
		position: relative;
		padding-bottom: 40rpx !important;
	}

	/deep/.u-form-item__body {
		background: rgb(252, 252, 252) !important;
		border: 1px solid;
		border-radius: 16rpx;
		border-color: rgb(232, 232, 232);

		.u-form-item__body__right {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.u-form-item__body__right__content {
			width: calc(100% - 20rpx);
			padding-left: 10rpx;
		}
	}

	/deep/.u-form-item__body__right__message {
		margin: 0 0 5rpx 50rpx !important;
		position: absolute;
		bottom: 5rpx;
	}

	.psd-icon {
		padding-right: 10rpx;
	}

	.login-btn {
		margin-top: 30rpx;
	}
</style>