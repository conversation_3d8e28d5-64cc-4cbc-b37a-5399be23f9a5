<template>
	<view class="content">
		<view class="tab-wrapper" v-if="showTab">
			<u-tabs :list="sectionList" :lineColor="'#00a88a'" :lineWidth="'80rpx'" :activeStyle="{color: '#00a88a'}"
				:itemStyle="tabStyleObj" @click="sectionChangeHandler" :scrollable="false"></u-tabs>
		</view>
		<view class="search-wrapper">
			<u-search :placeholder="`请输入${placeholder}`" v-model="keyword" clearabled @custom="customHandler"
				@search="customHandler" @clear="clearHandler"></u-search>
		</view>
		<map id="map" class="map" ref="map" show-location :latitude="latitude" :longitude="longitude"
			@markertap="markertapHandle" :markers="covers" enable-satellite :scale="7">
			<cover-view class="marker-card" v-if="currentMarker">
				<view class="card-content">
					<view class="card-top">
						<view class="close-btn">
							<u-icon @click="closeCard" class="btn-icon" name="close"> </u-icon>
						</view>
					</view>
					<view class="data-item-fields">
						<view class="data-item-title">
							<view class="data-item-title-info">
								<u-icon name="map-fill" color="#00a88a" :size="'50rpx'"></u-icon>
								<text class="data-item-title-key">点位编号</text>
								<text class="data-item-title-label">{{`${currentMarker.监测点编号}`}}</text>
							</view>
							<view class="data-item-title-right">
								<fui-button size="28" :radius="'32rpx'" :background="'rgba(0, 168, 138, 0.05)'"
									:height="'56rpx'" :width="'118rpx'" text="到这去" :color="'#00a88a'"
									:borderColor="'#00a88a'" @click="navigateHandler(currentMarker)"></fui-button>
							</view>
						</view>
						<view class="data-item-body">
							<view class="data-item-field" v-for="(field, index) of fields" :key="index">
								<text class="field-title">{{`${ field.label}:`}}</text>
								<text class="field-value" :class="{
                    'title-label-error':
                      setValue(currentMarker, field) === '已退回' &&
                      field.label === '审核状态',
                    'title-label-primary':
                      setValue(currentMarker, field) !== '已退回' &&
                      field.label === '审核状态',
                    'title-label-doing':
                      setValue(currentMarker, field) === '填报中' &&
                      field.label === '审核状态',
                  }">{{`${ setValue(currentMarker, field) }`}}</text>
							</view>
						</view>
					</view>
					<view class="data-item-operations">
						<view class="dc-item-operations">
							<text v-if="showTab" class="operations-label">调查</text>
							<view class="btn-wrapper btn-wrapper-show-tab" v-for="(operation, index) of operations"
								v-if="operationShow(operation, currentMarker)" :key="index"
								@click.stop="operationBtnClickHandler('dc', operation.key, currentMarker)">
								<fui-button :icon="operation.icon || ''" size="28" :radius="'32rpx'"
									:background="'rgba(0, 168, 138, 0.05)'" :height="'56rpx'" :width="'118rpx'"
									:text="operation.label" :color="'#00a88a'" :borderColor="'#00a88a'"></fui-button>
							</view>
						</view>
						<view class="cy-item-operations" v-if="showTab">
							<text class="operations-label">采样</text>
							<view class="btn-wrapper btn-wrapper-show-tab" v-for="(operation, index) of operations"
								v-if="operationShow(operation, currentMarker)" :key="index" @click.stop="
                  operationBtnClickHandler('cy', operation.key, currentMarker)
                ">
								<fui-button :icon="operation.icon || ''" size="28" :radius="'32rpx'"
									:background="'rgba(0, 168, 138, 0.05)'" :height="'56rpx'" :width="'118rpx'"
									:text="operation.label" :color="'#00a88a'" :borderColor="'#00a88a'"></fui-button>
							</view>
						</view>
					</view>
				</view>
			</cover-view>
		</map>
		<view class="picker" v-if="showAdminPicker">
			<u-picker :show="showAdminPicker" :columns="adminColumns" keyName="label" @confirm="adminConfirmHandle"
				@cancel="adminCancelHandle"></u-picker>
		</view>
	</view>
</template>

<script>
	import Storage from "@/tools/Storage";
	import Navigation from "@/tools/Navigation";
	import FuiButton from "@/components/fui-button/fui-button";
	import _ from "lodash";

	const POINT_LON_FIELD = "经度";
	const POINT_LAT_FIELD = "纬度";
	const DATA_TYPE_ARRAY = ["地膜监测", "地膜调查", "棚膜调查"];
	const TAB_FILTER_CHANGE = "tabFilterChange";
	const TAB_SEARCH_FILTER_CHANGE = "tabSearchFilterChange";

	import ComparatorFactory from "@/components/form/GTForm/comparator/Factory.js";

	export default {
		data() {
			return {
				longitude: 108,
				latitude: 22,
				_mapContext: null,
				thisMarker: null,
				cardColumns: null,
				cardOperates: null,
				popupShow: false,
				currentMarker: null,
				currentTask: {
					任务名称: "aa",
				},
				showAdminPicker: false,
				columns: [],
				currentAdmin: null,
				codeInput: null,
				fields: [{
						name: "监测点类型",
						label: "类型",
					},
					{
						name: "作物",
						label: "作物",
					},
					{
						name: "审核状态",
						label: "审核状态",
					},
				],

				operations: [{
						key: "view",
						label: "查看",
						icon: "file-text",
						type: "info",
					},
					{
						key: "add",
						label: "填报",
						icon: "edit-pen",
						type: "primary",
					},
				],
				locationLonLat: null,
				location: null,
				pageUrl: null,
				showLegend: true,
				currentTab: 0,
				keyword: null,
				searchItem: { key: "乡镇名称", value: "乡镇名称" },
				tabStyleObj: {
					width: '355rpx',
					height: '80rpx'
				},
				btnCustomStyle: {
					color: '#00a88a'
				}
			};
		},
		props: {
			dataList: {
				type: Array,
			},
			dataType: {
				type: String,
			},
			任务_uid: {
				type: String,
			},
		},
		computed: {
			covers() {
				let list = this.coversList;
				if (this.currentAdmin) {
					list = list.filter((item) => {
						return item.fcode == this.currentAdmin.fcode;
					});
				}
				if (this.codeInput) {
					list = list.filter((item) => {
						return item.预设编号.indexOf(this.codeInput) >= 0;
					});
				}
				return list;
			},
			coversList() {
				const param = {
					已填报: "/static/point.png",
					待填报: "/static/point1.png",
				};
				const data = this.dataList;
				return data.map((item, index) => {
					item.id = index;
					item.iconPath = "/static/images/point.png";
					item.width = 27;
					item.height = 27;
					return item;
				});
			},
			adminColumns() {
				const data = this.dataList;
				return [
					[
						...new Set(
							data.map((item, index) => {
								return JSON.stringify({
									label: item.fname,
									fname: item.fname,
									fcode: item.fcode,
								});
							})
						),
					].map((item) => {
						return JSON.parse(item);
					}),
				];
			},

			tabSectionList() {
				const userInfo = JSON.parse(uni.getStorageSync("userInfo"));
				switch (this.dataType) {
					case DATA_TYPE_ARRAY[0]: {
						return [{
								filter: [
									"and",
									["=", "监测点类型", "地膜监测国控点"],
									["=", "任务_uid", this.任务_uid],
									["=", "分配采集人", userInfo._id]
								],
								section: { name: "国控点" },
							},
							{
								filter: [
									"and",
									["=", "监测点类型", "地膜监测省控点"],
									["=", "任务_uid", this.任务_uid],
									["=", "分配采集人", userInfo._id],
								],
								section: { name: "省控点" },
							},
						];
					}
					case DATA_TYPE_ARRAY[1]: {
						return ["=", "任务_uid", this.任务_uid];
					}
					case DATA_TYPE_ARRAY[2]: {
						return ["=", "任务_uid", this.任务_uid];
					}
				}
			},
			showTab() {
				switch (this.dataType) {
					case DATA_TYPE_ARRAY[0]: {
						return true;
					}
					case DATA_TYPE_ARRAY[1]: {
						return false;
					}
					case DATA_TYPE_ARRAY[2]: {
						return false;
					}
				}
			},
			sectionList() {
				return (
					(Array.isArray(this.tabSectionList) &&
						this.tabSectionList.map((f) => f.section)) || []
				);
			},
			placeholder() {
				return (
					(this.searchItem &&
						Object.keys(this.searchItem).length > 0 &&
						this.searchItem.value) ||
					this.searchItem?.key ||
					"关键字"
				);
			},
			searchField() {
				return Object.keys(this.searchItem).length > 0 && this.searchItem.key;
			},
			searchFilter() {
				return this.searchField && this.keyword ? ["like", this.searchField, `%${this.keyword}%`] : [];
			},
			tabFilter() {
				return Array.isArray(this.tabSectionList[this.currentTab]?.filter) &&
					this.showTab ?
					this.tabSectionList[this.currentTab].filter : [];
			},
		},
		onReady() {
			// 设置地图样式
		},
		onLoad() {
			uni.$on("setPointData", (list) => {
				this.dataList = list;
			});
		},
		mounted() {
			this._mapContext = uni.createMapContext("map", this);
		},
		methods: {
			async locationClick() {
				let res = await getApp().$utils.location.getLocation();
				this._mapContext.moveToLocation({
					longitude: res.longitude,
					latitude: res.latitude,
				});
			},
			async getUserLocation() {
				try {
					let res = await getApp().$utils.location.getLocation();
					this.location = res;
					this.locationLonLat = [res.longitude, res.latitude];
					this.loading = false;
				} catch (e) {
					this.showError(getApp().$constants.MSG.LOCATION_FAIL);
				}
				// this.locationLonLat = [116.498391, 39.915952];
			},
			operationShow(operation, item) {
				let visible = true;
				if (
					operation.depends &&
					Array.isArray(operation.depends) &&
					operation.depends.length > 0
				) {
					const comparator = ComparatorFactory.createComparator(
						operation.depends,
						item,
						null
					);
					visible = comparator.compare();
				}
				return visible;
			},
			setValue(item, field) {
				let value;
				if (typeof field.name == "object") {
					return field.name
						.map((i) => {
							return item[i];
						})
						.join("");
					// value = item[field.name];
				}
				value = item[field.name];

				if (field.name == "经度" || field.name == "纬度") {
					return `${value.toFixed(6)}`;
					// （${getApp().$utils.degreesToDigital.ToDegrees(value)}）`;
				}
				switch (field.type) {
					case "text":
						return value;
					case "date":
						return getApp().$utils.date.format(value);
					default:
						return value;
						break;
				}
			},
			setPointData(data) {
				this.coversList = data.map((item, index) => {
					item.id = index;
					item.iconPath = "/static/images/point.png";
					item.width = 27;
					item.height = 27;
					return item;
				});
				this.adminColumns = [
					[
						...new Set(
							data.map((item, index) => {
								return JSON.stringify({
									label: item.fname,
									fname: item.fname,
									fcode: item.fcode,
								});
							})
						),
					].map((item) => {
						return JSON.parse(item);
					}),
				];
				console.log(this.adminColumns);
			},
			searchHandler(e) {
				console.log(e);
				this.codeInput = e;
			},
			closeCard() {
				this.currentMarker = null;
			},
			btnClickHandle() {
				this.currentMarker = null;
				console.log(this.adminColumns);
				this.showAdminPicker = true;
			},
			adminConfirmHandle(e) {
				// uni.$emit("taskSelect", e.value[0]);
				console.log(e);
				this.currentAdmin = e.value[0];
				this.showAdminPicker = false;
			},
			adminCancelHandle(e) {
				this.currentAdmin = null;
				this.showAdminPicker = false;
			},
			async markertapHandle(e) {
				// await this.getUserLocation();
				console.log(e, this.covers);
				let detail = e.detail;
				const marker = this.coversList.find((item) => {
					return item.id == detail.markerId;
				});
				// marker.distance = getApp().$utils.distance.getDistance(
				//   this.locationLonLat,
				//   [marker.经度, marker.纬度]
				// );
				this.currentMarker = marker;
			},
			async getFormDataTotal(formUid, item) {
				const { 任务_uid, 监测点_uid } = item;
				const filter = [
					"and",
					["=", "任务_uid", 任务_uid],
					["=", "监测点_uid", 监测点_uid],
				];
				const params = {
					filter,
					page: {
						pageNum: 1,
						pageSize: 10,
					},
				};
				const res = await getApp().$apis.formData.getFormRecords(formUid, params);
				return res.total;
			},
			getGenPageUrl(url, item) {
				const fields = [
					"任务_uid",
					"年份",
					"监测点_uid",
					"监测点类型",
					"监测点编号",
					"省编码",
					"省名称",
					"市编码",
					"市名称",
					"县编码",
					"县名称",
					"乡镇编码",
					"乡镇名称",
					"调查指标值",
				];
				fields.forEach((f) => {
					const itemUrl = `&${f}=${item[f]}`;
					url = url.concat(itemUrl);
				});
				return url;
			},
			async operationBtnClickHandler(type, key, item) {
				console.log(
					"🚀 ~ file: index.nvue ~ line 496 ~ operationBtnClickHandler ~ type, key, item",
					type,
					key,
					item
				);
				const typeArr = ["cy", "dc"];
				const { 调查表单_uid, 采样表单_uid, 调查指标值, 采样指标值 } = item;
				let formUid = type === typeArr[0] ? 采样表单_uid : 调查表单_uid;
				let indexField = type === typeArr[0] ? 采样指标值 : 调查指标值;
				console.log(
					"🚀 ~ file: index.nvue ~ line 500 ~ operationBtnClickHandler ~ indexField",
					indexField
				);
				console.log(
					"🚀 ~ file: index.nvue ~ line 500 ~ operationBtnClickHandler ~ formUid",
					formUid
				);

				if (key === "add") {
					console.log(
						"🚀 ~ file: index.nvue ~ line 502 ~ operationBtnClickHandler ~ key",
						key
					);
					const total = await this.getFormDataTotal(formUid, item);
					console.log(
						"🚀 ~ file: index.nvue ~ line 504 ~ operationBtnClickHandler ~ total",
						total
					);

					if (total >= indexField) {
						uni.showToast({
							title: "点位指标已满",
							icon: "error",
							duration: 2000,
						});
						return;
					}
					const pageUrl = `/pages/FormPage/index?formUid=${formUid}`;
					const genUrl = this.getGenPageUrl(pageUrl, item);
					this.$nextTick(() => {
						this.navigateTo(genUrl);
					});
				}
				if (key === "view") {
					const pageUrl = `/pages/FormList/index?formUid=${formUid}`;
					const genUrl = this.getGenPageUrl(pageUrl, item);
					this.navigateTo(genUrl);
				}
			},

			navigateHandler(data) {
				let lon = data[POINT_LON_FIELD];
				let lat = data[POINT_LAT_FIELD];
				if (isNaN(lon) || isNaN(lat)) return;
				Navigation.navigateTo(lon, lat);
			},
			// 跳转页面
			navigateTo(url) {
				uni.navigateTo({
					url,
				});
			},
			getDistance() {
				return (
					parseFloat((this.currentMarker["distance"] / 1000).toFixed(2)) + "公里"
				);
			},

			// tab切换的响应
			sectionChangeHandler(item) {
				let secIndex = 0;
				this.sectionList.forEach((f, index) => {
					if (f.name === item.name) {
						secIndex = index
					}
				})
				this.currentTab = secIndex;
				this.clearHandler();
				this.currentMarker = null;
				uni.$emit(TAB_FILTER_CHANGE, this.tabFilter);
			},

			// 清除搜索的响应
			clearHandler() {
				this.keyword = "";
			},
			// 搜索的响应
			customHandler() {
				const genFilter = this.getGenFilter();
				console.log("请求参数:", genFilter);

				uni.$emit(TAB_SEARCH_FILTER_CHANGE, genFilter);
			},
			getGenFilter() {
				let genFilter = [];

				if (Array.isArray(this.searchFilter) && !this.searchFilter.length > 0) {
					genFilter = [...this.tabFilter];
					return genFilter;
				}
				if (Array.isArray(this.tabFilter) && !this.tabFilter.length > 0) {
					genFilter = [...this.searchFilter];
					return genFilter;
				}
				if (Array.isArray(this.tabFilter[1]) && this.tabFilter[0] === "and") {
					genFilter.push(...this.tabFilter, this.searchFilter);
				} else {
					genFilter.push("and", this.tabFilter, this.searchFilter);
				}
				return genFilter;
			},
		},
	};
</script>

<style>
	.content {
		flex: 1;
		position: relative;
	}

	.tab-wrapper {
		margin: 0 20rpx;
	}

	.search-wrapper {
		margin: 20rpx;
	}

	.search-bar {
		position: absolute;
		width: 750rpx;
		height: 90rpx;
		display: flex;
		flex-direction: row;
		z-index: 9999;
		box-sizing: border-box;
		align-items: center;
	}

	.text {
		font-size: 26rpx;
	}

	.placeholder {
		color: #909193;
	}

	.btn {
		height: 68rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		border: 1px solid #eeeeef;
		border-radius: 20rpx;
	}

	.card-top {
		display: flex;
		flex-direction: row;
		height: 35rpx;
		background: #fff !important;
		justify-content: flex-end;
	}


	.r-search {
		flex: 1;
	}

	.btn .btn-icon {
		margin-left: 10rpx;
	}

	.map {
		flex: 1;
		width: 750rpx;
	}

	.marker-card {
		width: 700rpx;
		position: absolute;
		bottom: 35rpx;
		height: 400rpx;
		background-color: #fff;
		left: 25rpx;
	}

	.popup {
		position: absolute;
	}

	.card-content {
		border: 1px solid #dadbde;
		padding: 8px;
		border-radius: 4px;
		background-color: #fff;
		position: relative;
		display: flex;
		flex-direction: column;
	}

	.close-btn {
		padding: 5rpx;
	}

	.map-location {
		margin-top: 14rpx;
		background-color: rgba(255, 255, 255, 0.5);
		/* padding: 14rpx; */
		border-radius: 10rpx;
	}

	.img-map2 {
		width: 40rpx;
		height: 40rpx;
		background-color: #ffffff;
		border-radius: 5px;
	}

	.data-item-title {
		display: flex;
		flex-direction: row;
		box-sizing: border-box;
		justify-content: space-between;
		padding: 15rpx;
		width: 700rpx;
		height: 90rpx;
		font-size: 32rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #00a88a !important;
		background: linear-gradient(180deg, rgba(0, 168, 139, 0.08) 0%, rgba(0, 168, 139, 0) 100%);
		line-height: 45rpx;
		border-radius: 16rpx;
	}

	.data-item-title-info {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		font-size: 32rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #00a88a;
	}

	.data-item-title-right {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		line-height: 40rpx;
		margin-right: 25rpx;
		color: #00a88a !important;
	}

	.data-item-title-key {
		font-size: 32rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #00a88a;
		padding: 0 20rpx;
		text-align: center;
	}

	.data-item-title-label {
		text-justify: center;
		text-align: center;
		font-weight: bold;
		font-size: 32rpx;
		color: #00a88a !important;
		display: flex;
		flex-direction: row;
	}

	.u-button {
		color: #00a88a !important;
		width: 118rpx;
		height: 56rpx;
		/* border-radius: 32rpx; */
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		line-height: 28rpx;
	}

	u-button {
		color: #00a88a !important;
	}

	.data-item-body {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.data-item-field {
		display: flex;
		flex-direction: row;
		box-sizing: border-box;
		width: 300rpx;
		padding: 20rpx;
		font-size: 24rpx;
	}

	.field-title {
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #010101;
		line-height: 40rpx;
		text-align: center;
		min-width: 3em;
	}

	.field-value {
		margin: 0 10rpx;
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #010101;
		line-height: 40rpx;
		text-align: center;
	}

	.data-item-fields {
		display: flex;
		flex-direction: column;
	}

	.data-item-operations {
		display: flex;
		flex-direction: row-reverse;
		justify-content: space-between;
		box-sizing: border-box;
		width: 660rpx !important;
		height: 64rpx;
		margin: 8rpx 10rpx 15rpx 10rpx;
	}

	.cy-item-operations {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		height: 64rpx;
		width: 330rpx !important;
	}

	.dc-item-operations {
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
		align-items: center;
		height: 64rpx;
		width: 330rpx !important;
	}

	.operations-label {
		margin: 0;
		white-space: nowrap;
		text-align: center;
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 30rpx;
	}

	.btn-wrapper {
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 40rpx;
		margin-left: 10rpx;
		height: 64rpx;
	}

	.btn-wrapper-show-tab {}

	.title-label-error {
		font-size: 28rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #d0021b !important;
		line-height: 40rpx;
	}

	.title-label-primary {
		font-size: 28rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		line-height: 40rpx;
		color: #00a88a !important;
	}

	.title-label-doing {
		font-size: 28rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		line-height: 40rpx;
		color: #007cff !important;
	}

	.error-btn {
		color: #f00 !important;
	}

	.primary-btn {
		color: #00a88a !important;
	}

	.btn-custom-style {
		/* color: #00a88a !important; */
	}
</style>