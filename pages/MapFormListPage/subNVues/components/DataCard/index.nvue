<template>
  <view class="card-content">
    <u-tag class="tag" :text="'距离：'" plain></u-tag>
    <view class="data-item-fields">
      <view
        class="data-item-field"
        v-for="(field, index) of fields"
        :key="index"
      >
        <view class="title">
          <text class="text">{{ field.label }}：</text>
        </view>
        <view class="value">
          <text class="text">{{ setValue(currentMarker, field) }}</text>
        </view>
      </view>
    </view>
    <view class="data-item-operations">
      <view
        class="btn-wrapper"
        :style="{ width: operationWidth }"
        v-if="operationShow(operation, item)"
        v-for="(operation, index) of operations"
        @click.stop="operationBtnClickHandler(operation.key, item)"
        :key="index"
      >
        <u-button :type="operation.type || 'primary'">{{
          operation.label
        }}</u-button>
      </view>
    </view>
  </view>
</template>

<script>
const OPERATIONS = [
  {
    key: "view",
    label: "查看",
    depends: ["=", "填报状态", "已填报"],
  },
  {
    key: "edit",
    label: "编辑",
    depends: ["=", "填报状态", "已填报"],
  },
  {
    key: "edit",
    label: "填报",
    depends: ["=", "填报状态", "待填报"],
  },
  {
    key: "navigate",
    label: "导航",
  },
];
export default {
  props: {
    currentMarker: { type: Object },
  },
  data() {
    return {
      fields: [
        {
          name: "预设编号",
          label: "预设编号",
        },
        {
          name: ["cname", "fname", "tname", "xname"],
          label: "地址",
        },
        {
          name: "经度",
          label: "经度",
        },
        {
          name: "纬度",
          label: "纬度",
        },
      ],

      operations: OPERATIONS,
    };
  },
  methods: {
    setValue(item, field) {
      let value;
      if (typeof field.name == "object") {
        return field.name
          .map((i) => {
            return item[i];
          })
          .join("");
        // value = item[field.name];
      }

      switch (field.type) {
        case "text":
          return value;
        case "date":
          return this.$utils.date.format(value);
        default:
          return value;
          break;
      }
    },
  },
};
</script>

<style>
.card-content {
  border: 1px solid #dadbde;
  height: 100rpx;
  width: 100rpx;
  padding: 8px;
  border-radius: 4px;
  background-color: red;
  position: relative;
}
.data-item-field {
  padding: 4px 0;
}

.data-item-field .title {
  display: inline-block;
}

.data-item-field .value {
  display: inline-block;
}

.data-item-operations {
  padding: 6px 0 2px 0;
  display: flex;
  column-gap: 6px;
}

.data-item-operations .btn-wrapper {
  width: 100%;
}
.data-item-operations .u-button {
  height: 32px;
}
</style>
