<template>
	<view class="wrapper">
		<u-navbar title="点位地图" bgColor="#00a88a" leftIconColor="#fff" @leftClick="rightClickHandler"
			@rightClick="rightClickHandler" placeholder :titleStyle="{ color: '#FFF' }"></u-navbar>
		<view class="main">
			<SubMap v-if="dataType" class="sub-map" :dataList="list" :dataType="dataType" :任务_uid="任务_uid" />
		</view>

		<view class="picker" v-if="show">
			<u-picker :show="show" :columns="columns" keyName="label" @confirm="confirmHandle"
				@cancel="cancelHandle"></u-picker>
		</view>
	</view>
</template>

<script>
	const TAB_FILTER_CHANGE = "tabFilterChange";
	const TAB_SEARCH_FILTER_CHANGE = "tabSearchFilterChange";
	const DATA_TYPE_ARRAY = ["地膜监测", "地膜调查", "棚膜调查"];

	import SubMap from "./subNVues/index.nvue";

	export default {
		data() {
			return {
				list: [],
				currentTask: {},
				tasks: [],
				show: false,
				formUid: null,
				dataType: null,
				任务_uid: null,
				menuName: null,
				tabSearchFilter: [],
			};
		},
		components: { SubMap },
		mounted() {
			this.init();
		},
		watch: {
			async tabSearchFilter(val) {
				await this.getTaskData();
			},
		},
		computed: {
			initialFilter() {
				const userInfo = JSON.parse(uni.getStorageSync("userInfo"));
				switch (this.dataType) {
					case DATA_TYPE_ARRAY[0]: {
						return [
							"and",
							["=", "监测点类型", "地膜监测国控点"],
							["=", "任务_uid", this.任务_uid],
							["=", "分配采集人", userInfo._id]
						];
					}
					default: {
						return [
							'and',
							["=", "任务_uid", this.任务_uid],
							["=", "分配采集人", userInfo._id]
						];
					}
				}
			},
		},
		onShow() {
			this.getTaskData();
		},
		onLoad(option) {
			this.formUid = option.formUid;
			this.dataType = option.dataType;
			this.任务_uid = option.任务_uid;
			this.menuName = option.menuName;

			// 监听事件
			uni.$on(TAB_FILTER_CHANGE, (filter) => {
				this.tabSearchFilter = filter;
			});
			uni.$on(TAB_SEARCH_FILTER_CHANGE, (filter) => {
				this.tabSearchFilter = filter;
			});
		},
		methods: {
			async init() {
				this.getTaskData();
				this.setMapNVUEPage();
			},
			async getTaskData() {
				const userInfo = JSON.parse(uni.getStorageSync("userInfo"));
				let filter = this.tabSearchFilter.length > 0 ? this.tabSearchFilter : this.initialFilter
				let points = await getApp().$apis.formData.getFormRecords(this.formUid, {
					filter
				});
				const list = points.list.map((item) => {
					item.longitude = Number(item.经度);
					item.latitude = Number(item.纬度);
					return item;
				});
				uni.$emit("setPointData", list);
				this.list = list;
			},
			setMapNVUEPage() {
				// #ifdef APP-PLUS
				// uni.getSubNVueById('map').show()
				// let content = uni.createSelectorQuery().in(this).select('.main')
				// content
				//   .boundingClientRect(res => {
				//     console.log(res)
				//     uni.$emit('setMapStyle', res)
				//   })
				//   .exec()
				// #endif
			},
			taskClickHandle() {
				let columns = [
					this.tasks.map((item) => {
						item.label = item.任务名称;
						item.value = item._id;
						return item;
					}),
				];
				this.columns = columns;
				this.show = true;
			},
			async confirmHandle(e) {
				this.currentTask = e.value[0];
				let points = await getApp().$apis.formData.getFormRecords(
					POINT_FORM_URL, {
						filter: ["=", "taskid", this.currentTask._id],
					}
				);
				const list = points.list.map((item) => {
					item.longitude = item.经度;
					item.latitude = item.纬度;
					return item;
				});
				console.log(list);
				this.list = list;

				this.show = false;
			},

			cancelHandle(e) {
				this.show = false;
			},
			// 参数
			rightClickHandler() {
				const pageUrl =
					`/pages/PointListPage/index?子任务表_uid=${this.formUid}&任务类型=${this.dataType}&任务_uid=${this.任务_uid}&menuName=${this.menuName}`;
				uni.redirectTo({ url: pageUrl });
			},
		},
	};
</script>

<style scoped>
	page {
		display: flex;
	}

	::v-deep.u-navbar__content__left {
		display: none !important;
	}

	.wrapper {
		/* height: 100vh; */
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.task-select {}

	.main {
		flex: 1;
		flex-direction: column;
		background: #fff;
		display: flex;
	}

	.submap {
		flex: 1;
	}
</style>