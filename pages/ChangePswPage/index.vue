<template>
  <view class="user-wrapper">
    <u-navbar
      title="修改密码"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      @leftClick="leftClickHandler"
    ></u-navbar>
    <view class="head-wrapper" />
    <view class="body-wrapper">
      <view class="user-info-item">
        <view class="user-info-item-label">
          <view class="operations-item-label"><text>原密码：</text></view>
        </view>
        <view class="check-wrapper">
          <u-input v-model="oldPassword" :type="'text'" />
        </view>
      </view>

      <view class="operations-wrapper">
        <view class="operations-item">
          <view class="operations-item-label"><text>新密码：</text></view>
          <view class="check-wrapper">
            <u-input v-model="newPassword" :type="'text'" />
          </view>
        </view>
        <view class="operations-item">
          <view class="operations-item-label"><text> 确认密码：</text></view>
          <view class="check-wrapper">
            <u-input v-model="confirmPassword" :type="'text'" />
          </view>
        </view>

        <view class="logout">
          <u-button
            text="确认修改"
            type="primary"
            @click="changePassword"
          ></u-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import TabPage from "@/pages/TabPage";
import BasePage from "@/pages/BasePage";
export default {
  mixins: [TabPage, BasePage],
  data() {
    return {
      oldPassword: null,
      newPassword: null,
      confirmPassword: null,
    };
  },

  onLoad() {
    uni.hideTabBar();
  },
  created() {},
  methods: {
    async changePassword() {
      try {
        this.showLoading("修改密码中");
        if (this.checkWord(this.oldPassword)) {
          if (!this.checkNewWord(this.newPassword)) {
            this.showError(
              "必须包含大写字母、小写字母、数字和特殊字符，不少于8位"
            );
            return;
          }
          if (this.newPassword !== this.confirmPassword) {
            this.showError("新密码与确认密码不一致，请确认");
            return;
          }

          await this.$apis.login.changePassword(
            null,
            this.oldPassword,
            this.newPassword,
            this.confirmPassword
          );
          this.showSuccess("修改成功");
          uni.clearStorageSync();
          uni.$u.route({
            type: "redirectTo",
            url: `/pages/Login/index`,
          });
        }
      } catch (e) {
        console.log(e);
        this.showError("修改失败");
      } finally {
        this.hideLoading();
      }
    },
    checkWord(keyword) {
      const reg = /^[A-Za-z0-9]+$/;

      return !!reg.test(keyword);
    },
    checkNewWord(keyword) {
      const reg =
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&+=.])(?=.*[^\w\d\s])\S{8,}$/;

      return !!reg.test(keyword);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
}

.user-wrapper {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f9;
  .head-wrapper {
    box-sizing: border-box;
    position: fixed;
    width: 100%;
    background: url("@/static/images/userBackground.png") no-repeat;
    background-position: center;
    background-size: 100% 100%;
    display: flex;
    height: 450rpx;
    align-items: center;
    .user-img-wrapper {
      margin-left: 50rpx;
    }
    .user-roles-wrapper {
      margin-left: 35rpx;
      display: flex;
      flex-direction: column;
      .user-roles-name {
        font-size: 36rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 50rpx;
      }

      .user-roles-code {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 40rpx;
      }
    }
  }

  .body-wrapper {
    position: absolute;
    top: 332rpx;
    margin: 0 30rpx 20rpx 30rpx;
    width: calc(100% - 60rpx);
    .user-info-item {
      box-sizing: border-box;
      padding: 40rpx;
      background: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      .check-wrapper {
        display: flex;
        flex: 1;
      }
      .user-info-item-label {
        display: flex;
        .operations-item-label {
          width: 6em;
          display: flex;
          text {
            margin-left: 26rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
          }
        }
      }
    }

    // 伪类选择器的使用问题？？？
    .user-info-item:nth-of-type(n) {
      border-bottom-left-radius: 16rpx;
      border-bottom-right-radius: 16rpx;
    }
    .user-info-item:nth-of-type(1) {
      // border-top-left-radius: 16rpx;
      // border-top-right-radius: 16rpx;
      border-radius: 16rpx 16rpx 0 0;
    }

    .operations-wrapper {
      border-radius: 0 0 16rpx 16rpx;

      .operations-item {
        padding: 0 40rpx 40rpx 40rpx;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        .operations-item-label {
          width: 6em;
          display: flex;
          text {
            margin-left: 26rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
          }
        }
        .check-wrapper {
          display: flex;
          flex: 1;
          .check-title {
            cursor: pointer;
            margin-right: 24rpx !important;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
          }
        }
      }

      .operations-item:nth-last-of-type(2) {
        border-bottom-left-radius: 16rpx;
        border-bottom-right-radius: 16rpx;
      }
    }
  }
}
/deep/.logout {
  padding: 50rpx;
  margin-top: 100rpx;
  span {
    // color: #00a88a;
    color: #fff;
  }
}
</style>
