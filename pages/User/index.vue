<template>
	<view>
		<u-navbar title="我的" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder></u-navbar>
		<u-cell-group>
			<u-cell v-for="item in userList" :title="item.title" :value="getValue(item)" :icon="item.icon"></u-cell>
			<u-cell title="修改密码" isLink @click="showPassword = true"></u-cell>
			<u-cell title="投诉举报" isLink @click="report"></u-cell>
			<u-cell title="应用版本" :value="versionInfo.version" isLink @click="checkVersion"></u-cell>
		</u-cell-group>
		<u-popup :show="showPassword" @close="close" @open="open" mode="center" round="14">
			<view class="psd-container">
				<view class="psd-wraper">
					<u-form :model="psdObj" ref="uForm">
						<u-form-item :prop="item.key" v-for="(item, index) in psdList" :key="index">
							<u-input :placeholder="item.placeholder" :adjustPosition="false" :clearable="true" type="password" border="surround" fontSize="32rpx" v-model="psdObj[item.key]" prefixIcon="lock" placeholderClass="data-placeholder"> </u-input>
						</u-form-item>
					</u-form>
					<view class="btn">
						<u-button text="修改密码" type="primary" @click="savePsd"></u-button>
					</view>
				</view>
			</view>
		</u-popup>
		<view class="logout">
			<u-button text="退出登录" type="primary" @click="outLogin"></u-button>
		</view>
		<u-modal :show="modalShow" :title="modal.title" :content="modal.content" showCancelButton @cancel="modalCancel" @confirm="modalConfirm"></u-modal>
	</view>
</template>

<script>
	const VERSION_TEST = "DEV.0.1";
	export default {
		data() {
			return {
				userInfo: null,
				userList: [{
						title: "姓名",
						value: "nickname",
					},
					{
						title: "用户名",
						value: "username",
					},
					{
						title: "用户身份",
						value: "role_name",
						type: "role_name",
					},
					{
						title: "绑定手机号",
						value: "phone",
					},
				],
				versionInfo: {
					version: null,
				},
				showPassword: false,
				psdList: [{
						title: "旧密码",
						key: "oldPassword",
						placeholder: "请输入旧密码",
					},
					{
						title: "新密码",
						key: "newPassword",
						placeholder: "请输入新密码",
					},
					{
						title: "确认密码：",
						key: "confirmPassword",
						placeholder: "请输入确认密码",
					},
				],
				psdObj: {
					oldPassword: "",
					newPassword: "",
					confirmPassword: "",
				},
				rules: {
					oldPassword: [{
						required: true,
						message: "请输入旧密码",
						trigger: ["blur", "change"],
					}, ],
					newPassword: [{
							required: true,
							message: "请输入新密码",
							trigger: ["blur", "change"],
						},
						{
							pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,
							// 正则检验前先将值转为字符串
							transform(value) {
								return String(value);
							},
							message: "密码应由6-16位数字、字母组成",
						},
					],
					confirmPassword: [{
							required: true,
							message: "请输入确认密码",
							trigger: ["blur", "change"],
						},
						{
							required: true,
							message: "两次密码不相同!",
							trigger: "blur",
							validator: (rule, value, callback) => {
								if (value !== this.psdObj.newPassword) {
									callback(new Error("两次密码不相同!"));
								} else {
									callback();
								}
							},
						},
					],
				},
				modalShow: false,
				modal: {
					title: "密码修改",
					content: "您确认要修改密码吗？",
				},
			};
		},
		created() {
			let version = "";
			try {
				version = plus.runtime.version;
			} catch (e) {
				version = VERSION_TEST;
			}
			if (process.env.NODE_ENV == "dev") {
				version = `测试环境 ${version}`;
			}
			this.versionInfo.version = version;

			let userInfo = JSON.parse(uni.getStorageSync("userInfo"));
			this.userInfo = userInfo;
		},

		methods: {
			report() {
				const pageUrl = `${this.$constants.PAGE.FORM_PAGE_URL}?formUid=投诉举报表&cacheable=false&title=投诉举报`
				uni.navigateTo({
					url: pageUrl
				})
			},
			getValue(item) {
				if (!item.type) {
					return this.userInfo[item.value];
				}
				if (item.type == "role_name") {
					console.log("this.userINfo", this.userInfo);
					return this.userInfo[item.value][0];
				}
			},
			outLogin() {
				uni.showToast({
					title: "退出成功",
					complete() {
						uni.clearStorageSync();
						uni.$u.route({
							type: "redirectTo",
							url: `/pages/Login/index`,
						});
					},
				});
			},
			async checkVersion() {
				this.showLoading(this.$constants.VERSION.CHECK);
				const result = await this.$apis.version.checkVersion();
				uni.hideLoading();
				if (!result) {
					this.showError(this.$constants.VERSION.FAILED);
					return;
				}
				const { upgrade, version, url } = result;
				if (upgrade === true) {
					uni.showModal({
						title: "版本更新",
						content: this.$constants.VERSION.NEW_VERSION,
						success: function(res) {
							if (res.confirm) {
								plus.runtime.openURL(url);
							}
						},
					});
				} else {
					this.showInfo(this.$constants.VERSION.LATEST_ALREADY);
				}
			},

			open() {
				this.$refs.uForm.setRules(this.rules);
			},
			close() {
				this.showPassword = false;
				this.psdObj = {
					oldPassword: "",
					newPassword: "",
					confirmPassword: "",
				};
			},

			savePsd() {
				this.$refs.uForm
					.validate()
					.then(res => {
						this.modalShow = true;
					})
					.catch(errors => {
						uni.$u.toast("校验失败");
						this.modalShow = false;
					});
			},
			modalConfirm() {
				console.log("校验通过");
				this.loading = true;
				uni.showLoading({ title: "", mask: true });
				try {
					uni.getStorage({
						key: "userInfo",
						success: async user => {
							let userInfo = JSON.parse(user.data);
							let res = await this.$apis.login.changePassword(userInfo._id, this.psdObj.oldPassword, this.psdObj.newPassword, this.psdObj.confirmPassword).catch(err => {
								uni.showToast({
									title: err,
									icon: "error",
									duration: 2000,
								});
							});
							if (res) {
								uni.showToast({
									title: "修改成功",
									icon: "success",
									mask: true,
									duration: 2000,
									complete() {
										uni.clearStorageSync();
										setTimeout(() => {
											uni.reLaunch({
												url: `/pages/Login/index`
											})
										}, 2000)
									},
								});
							}
						},
					});
				} catch (err) {}
				this.loading = false;
				uni.hideLoading();
				this.modalShow = false;
			},
			modalCancel() {
				this.modalShow = false;
			},

			showLoading(msg) {
				uni.showLoading({
					title: msg,
					mask: true,
				});
			},
			showError(msg) {
				uni.showToast({
					title: msg,
					icon: "error",
					duration: 2000,
				});
			},

			showInfo(msg) {
				uni.showToast({
					title: msg,
					icon: "info",
					duration: 2000,
				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	@import "@/static/styles/common.scss";

	/deep/ .u-navbar__content {
		.u-navbar__content__title {
			color: $gt-navbar-title-color !important;
		}
	}

	/deep/ .u-navbar__content__left {
		display: none;
	}

	.logout {
		padding: 50rpx;
		margin-top: 100rpx;
	}

	/deep/.u-cell__value {
		width: 50vw;
	}

	// /deep/.u-popup__content {
	// 	width: 80%;
	// 	height: 50%;
	// }
	/deep/.u-input__content {
		width: 400rpx;
	}

	.psd-wraper {
		padding: 40rpx;

		.btn {
			margin-top: 50rpx;
		}
	}

	.psd-container {
		width: 80vw;
		display: flex;
		justify-content: center;
	}
</style>